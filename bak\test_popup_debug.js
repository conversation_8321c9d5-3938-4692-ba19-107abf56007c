// test_popup_debug.js - 专门调试设置弹出层的工具

"ui";

ui.layout(
    <vertical padding="16dp">
        <text textSize="18sp" textColor="#2196F3" text="设置弹出层调试工具" gravity="center" marginBottom="16dp"/>
        
        <card cardBackgroundColor="#fff3cd" cardCornerRadius="8dp" cardElevation="2dp" margin="8dp">
            <vertical padding="12dp">
                <text textSize="14sp" textColor="#856404" textStyle="bold" text="⚠️ 使用说明" />
                <text textSize="12sp" textColor="#856404" text="• 请先进入小红书主页" marginTop="4dp" />
                <text textSize="12sp" textColor="#856404" text="• 点击'打开弹出层'按钮" marginTop="2dp" />
                <text textSize="12sp" textColor="#856404" text="• 然后点击'分析弹出层'查看详细信息" marginTop="2dp" />
            </vertical>
        </card>

        <horizontal marginTop="16dp">
            <button id="openPopupBtn" text="打开弹出层" layout_weight="1" bg="#4CAF50" textColor="white"/>
            <button id="analyzePopupBtn" text="分析弹出层" layout_weight="1" bg="#2196F3" textColor="white"/>
        </horizontal>
        
        <horizontal marginTop="8dp">
            <button id="findSettingsBtn" text="查找设置按钮" layout_weight="1" bg="#FF9800" textColor="white"/>
            <button id="clickSettingsBtn" text="点击设置按钮" layout_weight="1" bg="#9C27B0" textColor="white"/>
        </horizontal>

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="分析结果:" />
        <text id="analysisResult" text="等待分析..." textSize="14sp" textColor="#666666"/>

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="详细日志:" />
        <ScrollView layout_weight="1">
            <text id="debugLog" text="" textSize="10sp" />
        </ScrollView>

        <button id="clearBtn" text="清空日志" marginTop="8dp"/>
    </vertical>
);

// 日志函数
function log(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    
    ui.run(() => {
        const currentLog = ui.debugLog.getText().toString();
        ui.debugLog.setText(currentLog + logMessage + "\n");
    });
    
    console.log(logMessage);
}

function setResult(result, color) {
    if (!color) color = "#666666";
    ui.run(() => {
        ui.analysisResult.setText(result);
        ui.analysisResult.setTextColor(colors.parseColor(color));
    });
}

// 打开设置弹出层
function openSettingsPopup() {
    log("=== 打开设置弹出层 ===");
    setResult("打开中...", "#FF9800");
    
    try {
        // 检查是否在小红书主页
        if (currentPackage() !== "com.xingin.xhs") {
            log("启动小红书应用...");
            launch("com.xingin.xhs");
            sleep(3000);
        }
        
        // 点击设置按钮
        log("查找设置按钮 (i48)...");
        const settingsBtn = id("com.xingin.xhs:id/i48").findOne(5000);
        if (!settingsBtn) {
            throw new Error("找不到设置按钮");
        }
        
        log("点击设置按钮...");
        settingsBtn.click();
        sleep(1000);
        
        log("设置弹出层已打开");
        setResult("弹出层已打开", "#4CAF50");
        
    } catch (e) {
        log("打开弹出层失败: " + e.toString());
        setResult("打开失败", "#f44336");
    }
}

// 分析弹出层内容
function analyzePopup() {
    log("=== 分析弹出层内容 ===");
    setResult("分析中...", "#FF9800");
    
    try {
        // 检查弹出层是否存在
        const popup = id("com.xingin.xhs:id/g10").findOne(2000);
        if (!popup) {
            throw new Error("找不到弹出层 (g10)");
        }
        
        log("✓ 找到弹出层 (g10)");
        
        // 获取所有文本元素
        log("--- 弹出层中的所有文本元素 ---");
        const allTexts = textMatches(/.+/).find();
        log("总共找到 " + allTexts.length + " 个文本元素");
        
        let settingsCount = 0;
        for (let i = 0; i < allTexts.length; i++) {
            const textElement = allTexts[i];
            const textContent = textElement.text();
            const bounds = textElement.bounds();
            
            if (textContent && textContent.trim()) {
                log("文本" + (i+1) + ": '" + textContent.trim() + "' (Y:" + bounds.centerY() + ")");
                
                if (textContent.includes("设置")) {
                    settingsCount++;
                    log("  → 这是设置相关文本！");
                }
            }
        }
        
        log("--- 专门查找'设置'按钮 ---");
        const settingsButtons = text("设置").find();
        log("通过text('设置')找到 " + settingsButtons.length + " 个按钮");
        
        for (let i = 0; i < settingsButtons.length; i++) {
            const btn = settingsButtons[i];
            const bounds = btn.bounds();
            log("设置按钮" + (i+1) + ": Y坐标=" + bounds.centerY() + ", 可点击=" + btn.clickable());
        }
        
        // 通过desc查找
        const descSettings = desc("设置").find();
        log("通过desc('设置')找到 " + descSettings.length + " 个元素");
        
        // 通过textContains查找
        const containsSettings = textContains("设置").find();
        log("通过textContains('设置')找到 " + containsSettings.length + " 个元素");
        
        setResult("分析完成: " + settingsCount + " 个设置文本", "#4CAF50");
        
    } catch (e) {
        log("分析失败: " + e.toString());
        setResult("分析失败", "#f44336");
    }
}

// 查找设置按钮
function findSettingsButton() {
    log("=== 查找设置按钮 ===");
    setResult("查找中...", "#FF9800");
    
    try {
        // 方式1: 通过文本查找
        const settingsButtons = text("设置").find();
        log("方式1 - text('设置'): 找到 " + settingsButtons.length + " 个");
        
        let bottomBtn = null;
        let maxY = -1;
        
        if (settingsButtons.length > 0) {
            for (let i = 0; i < settingsButtons.length; i++) {
                const btn = settingsButtons[i];
                const bounds = btn.bounds();
                log("按钮" + (i+1) + ": Y=" + bounds.centerY() + ", 可点击=" + btn.clickable());
                
                if (bounds.centerY() > maxY) {
                    maxY = bounds.centerY();
                    bottomBtn = btn;
                }
            }
            log("选择底部按钮: Y=" + maxY);
        }
        
        // 方式2: 通过desc查找
        const descBtn = desc("设置").findOne(1000);
        log("方式2 - desc('设置'): " + (descBtn ? "找到" : "未找到"));
        
        // 方式3: 通过textContains查找
        const containsBtn = textContains("设置").findOne(1000);
        log("方式3 - textContains('设置'): " + (containsBtn ? "找到" : "未找到"));
        
        if (bottomBtn) {
            setResult("找到底部设置按钮", "#4CAF50");
            // 存储找到的按钮供点击使用
            global.foundSettingsBtn = bottomBtn;
        } else {
            setResult("未找到设置按钮", "#f44336");
        }
        
    } catch (e) {
        log("查找失败: " + e.toString());
        setResult("查找失败", "#f44336");
    }
}

// 点击设置按钮
function clickSettingsButton() {
    log("=== 点击设置按钮 ===");
    setResult("点击中...", "#FF9800");
    
    try {
        if (!global.foundSettingsBtn) {
            throw new Error("请先执行'查找设置按钮'");
        }
        
        log("点击找到的设置按钮...");
        global.foundSettingsBtn.click();
        sleep(2000);
        
        // 验证是否成功进入设置页面
        log("验证是否进入设置页面...");
        
        const stillInPopup = id("com.xingin.xhs:id/g10").exists();
        log("是否还在弹出层: " + stillInPopup);
        
        const settingsPageIndicator = textContains("账号与安全").exists() || 
                                    textContains("隐私设置").exists() || 
                                    textContains("通用设置").exists();
        log("是否在设置页面: " + settingsPageIndicator);
        
        if (!stillInPopup && settingsPageIndicator) {
            setResult("成功进入设置页面", "#4CAF50");
            log("✓ 成功进入设置页面");
        } else if (stillInPopup) {
            setResult("仍在弹出层，点击失败", "#f44336");
            log("✗ 仍在弹出层，点击可能失败");
        } else {
            setResult("页面状态不明确", "#FF9800");
            log("? 页面状态不明确");
        }
        
    } catch (e) {
        log("点击失败: " + e.toString());
        setResult("点击失败", "#f44336");
    }
}

// UI事件处理
ui.openPopupBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    threads.start(openSettingsPopup);
});

ui.analyzePopupBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    threads.start(analyzePopup);
});

ui.findSettingsBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    threads.start(findSettingsButton);
});

ui.clickSettingsBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    threads.start(clickSettingsButton);
});

ui.clearBtn.on("click", () => {
    ui.debugLog.setText("");
    setResult("日志已清空", "#666666");
});

// 初始化
log("弹出层调试工具已启动");

if (!auto.service) {
    log("警告: 无障碍服务未开启");
    setResult("需要无障碍服务", "#f44336");
} else {
    setResult("就绪", "#4CAF50");
}
