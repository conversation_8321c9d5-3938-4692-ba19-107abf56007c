ui确实已经显示了，但是功能还没有实现。关于功能现在的需求是：
1.根据用户输入的关键字进行搜索笔记，可按小红书的筛选功能进行搜索：
小红书搜索筛选功能：
排序依据：综合、最新、最多点赞、最多评论、最多搜藏。
发布时间：一天内、一周内、半年内。
位置距离：同城、附近。

2、自动轮询搜索出来的笔记，根据用户输入的评论区关键字，采集评论区评论用户的uid，并存为任务结果列表（用户昵称、uid、评论内容）。

3、笔记截流评论，对搜索筛选出来的笔记，轮询笔记时自动根据用户预置的评论话术，或让ai大模型生成话术进行笔记评论。

4、笔记截流点赞，对搜索筛选出来的笔记，轮询笔记时自动点赞符合评论区关键字，采集评论区的评论用户（也就是功能2之中采集到的评论区评论用户的uid这里加一个直接点赞）

5、精准过滤，在功能2这里，可以通过精准过滤的设置，二次提取目标客户，分为限制地区，ai分析过滤评论用户的评论是否为目标客户。

6、ai大模型生成话术进行笔记评论，这个功能是针对功能3说到此功能。用户需要设置好提示词。然后开启此功能后，在进行功能3的时候，针对每一条笔记的评论都将发送到ai大模型生成评论话术返回，然后程序自动发布。但是为了能够让大模型生成的评论可以更贴合笔记更生动吸引人，还需要先将笔记发给大模型分析，如果是图文类笔记则直接发送笔记中的文字给大模型，如果是视频，则通过第三方视频下载地址解析接口把视频解析下载到本地再发送给大模型，文字或视频发送给大模型时，都要加上提示词，返回的内容直接发布。

这里的关键点是，如何实现把笔记中的文字复制下来发送给大模型，视频可以用小红书内置的复制链接来发送给第三方视频下载地址解析接口。所以可以把这一部份功能放开发最后来实现。

7、任务结果列表（用户昵称、uid、评论内容、评论日期），任务结果列表这里，是采集到的所有用户列表，可全选或手选用户进行轮询批量关注或私信（需设置预置的私信话术或按功能6中的，将每一个用户的评论内容发给大模型，让大模型生成私信内容，返回后私信用户。这里需要设置私信的提示词）。


这些功能最好做一个计划和实现方案，逐个去实现。
