// xhs_safety_control.js - 小红书安全控制模块
// 负责评论次数限制、时间控制等安全功能

const utils = require('./utils.js');

// Auto.js兼容的存储创建
var storage;
if (typeof storages !== 'undefined') {
    storage = storages.create("xhs_safety_control");
} else {
    // 如果storages不可用，使用备用方案
    storage = {
        put: function (key, value) { console.log("Safety Storage put:", key, value); },
        get: function (key, defaultValue) { console.log("Safety Storage get:", key); return defaultValue; }
    };
}

// 安全控制状态存储
let commentCountToday = 0; // 今日评论次数
let lastCommentTime = 0; // 上次评论时间戳
let dailyResetTime = null; // 每日重置时间

// 初始化安全控制
function initSafetyControl() {
    try {
        // 从存储中读取今日评论次数
        const today = new Date().toDateString();
        const storedDate = storage.get("safety_last_date", "");
        const storedCount = storage.get("safety_comment_count", 0);

        if (storedDate === today) {
            commentCountToday = storedCount;
        } else {
            // 新的一天，重置计数
            commentCountToday = 0;
            storage.put("safety_last_date", today);
            storage.put("safety_comment_count", 0);
        }

        lastCommentTime = storage.get("safety_last_comment_time", 0);

        utils.log("SAFETY: 安全控制初始化完成 - 今日评论次数: " + commentCountToday);
        return true;
    } catch (e) {
        utils.log("ERROR: SAFETY: 安全控制初始化失败: " + e.toString());
        return false;
    }
}

// 检查笔记是否满足评论数要求
function checkNoteCommentCount(noteInfo, minCommentCount) {
    if (!noteInfo || typeof noteInfo.commentCount !== 'number') {
        utils.log("SAFETY: 笔记信息无效或缺少评论数信息");
        return false;
    }

    if (minCommentCount <= 0) {
        // 没有最小评论数要求
        return true;
    }

    const meetRequirement = noteInfo.commentCount >= minCommentCount;
    utils.log("SAFETY: 笔记评论数检查 - 当前: " + noteInfo.commentCount + ", 要求: " + minCommentCount + ", 结果: " + (meetRequirement ? "通过" : "不通过"));

    return meetRequirement;
}

// 检查是否达到每日评论次数限制
function checkDailyCommentLimit(maxCommentsPerAccount) {
    if (maxCommentsPerAccount <= 0) {
        // 没有次数限制
        return true;
    }

    const withinLimit = commentCountToday < maxCommentsPerAccount;
    utils.log("SAFETY: 每日评论次数检查 - 当前: " + commentCountToday + ", 限制: " + maxCommentsPerAccount + ", 结果: " + (withinLimit ? "通过" : "已达限制"));

    return withinLimit;
}

// 计算并执行评论延迟
function executeCommentDelay(delayMinSeconds, delayMaxSeconds) {
    if (delayMinSeconds <= 0 && delayMaxSeconds <= 0) {
        // 没有延迟要求
        return;
    }

    // 确保最小值不大于最大值
    const minDelay = Math.max(0, delayMinSeconds);
    const maxDelay = Math.max(minDelay, delayMaxSeconds);

    // 计算随机延迟时间（秒）
    const delaySeconds = minDelay + Math.random() * (maxDelay - minDelay);
    const delayMs = Math.floor(delaySeconds * 1000);

    utils.log("SAFETY: 执行评论延迟 - " + delaySeconds.toFixed(1) + " 秒");

    // 执行延迟
    sleep(delayMs);
}

// 检查评论时间间隔
function checkCommentTimeInterval(minIntervalSeconds) {
    if (minIntervalSeconds <= 0 || lastCommentTime === 0) {
        // 没有时间间隔要求或首次评论
        return true;
    }

    const currentTime = Date.now();
    const timeSinceLastComment = (currentTime - lastCommentTime) / 1000; // 转换为秒

    const intervalOk = timeSinceLastComment >= minIntervalSeconds;
    utils.log("SAFETY: 评论时间间隔检查 - 距离上次: " + timeSinceLastComment.toFixed(1) + " 秒, 要求: " + minIntervalSeconds + " 秒, 结果: " + (intervalOk ? "通过" : "间隔太短"));

    return intervalOk;
}

// 记录评论操作
function recordCommentAction() {
    try {
        commentCountToday++;
        lastCommentTime = Date.now();

        // 保存到存储
        const today = new Date().toDateString();
        storage.put("safety_last_date", today);
        storage.put("safety_comment_count", commentCountToday);
        storage.put("safety_last_comment_time", lastCommentTime);

        utils.log("SAFETY: 记录评论操作 - 今日总计: " + commentCountToday + " 次");
        return true;
    } catch (e) {
        utils.log("ERROR: SAFETY: 记录评论操作失败: " + e.toString());
        return false;
    }
}

// 综合安全检查
function performSafetyCheck(noteInfo, safetyConfig) {
    utils.log("SAFETY: 开始执行综合安全检查...");

    if (!safetyConfig) {
        utils.log("SAFETY: 安全配置为空，跳过检查");
        return { passed: true, reason: "无安全配置" };
    }

    // 1. 检查笔记评论数要求
    if (!checkNoteCommentCount(noteInfo, safetyConfig.safetyMinCommentCount)) {
        return {
            passed: false,
            reason: "笔记评论数不足 (当前: " + (noteInfo.commentCount || 0) + ", 要求: " + safetyConfig.safetyMinCommentCount + ")"
        };
    }

    // 2. 检查每日评论次数限制
    if (!checkDailyCommentLimit(safetyConfig.safetyMaxCommentsPerAccount)) {
        return {
            passed: false,
            reason: "已达每日评论次数限制 (当前: " + commentCountToday + ", 限制: " + safetyConfig.safetyMaxCommentsPerAccount + ")"
        };
    }

    // 3. 检查评论时间间隔（使用最小延迟时间作为间隔要求）
    if (!checkCommentTimeInterval(safetyConfig.safetyCommentDelayMin)) {
        return {
            passed: false,
            reason: "评论时间间隔太短，需要等待"
        };
    }

    utils.log("SAFETY: 综合安全检查通过");
    return { passed: true, reason: "安全检查通过" };
}

// 执行评论前的安全操作
function executePreCommentSafety(safetyConfig) {
    utils.log("SAFETY: 执行评论前安全操作...");

    if (!safetyConfig) {
        return;
    }

    // 执行随机延迟
    executeCommentDelay(safetyConfig.safetyCommentDelayMin, safetyConfig.safetyCommentDelayMax);
}

// 执行评论后的安全操作
function executePostCommentSafety() {
    utils.log("SAFETY: 执行评论后安全操作...");

    // 记录评论操作
    recordCommentAction();
}

// 获取安全状态信息
function getSafetyStatus() {
    return {
        commentCountToday: commentCountToday,
        lastCommentTime: lastCommentTime,
        lastCommentTimeFormatted: lastCommentTime > 0 ? new Date(lastCommentTime).toLocaleString('zh-CN') : '无'
    };
}

// 重置每日计数（用于测试或手动重置）
function resetDailyCount() {
    try {
        commentCountToday = 0;
        const today = new Date().toDateString();
        storage.put("safety_last_date", today);
        storage.put("safety_comment_count", 0);

        utils.log("SAFETY: 每日评论计数已重置");
        return true;
    } catch (e) {
        utils.log("ERROR: SAFETY: 重置每日计数失败: " + e.toString());
        return false;
    }
}

// Auto.js兼容的导出方式
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        initSafetyControl: initSafetyControl,
        checkNoteCommentCount: checkNoteCommentCount,
        checkDailyCommentLimit: checkDailyCommentLimit,
        executeCommentDelay: executeCommentDelay,
        checkCommentTimeInterval: checkCommentTimeInterval,
        recordCommentAction: recordCommentAction,
        performSafetyCheck: performSafetyCheck,
        executePreCommentSafety: executePreCommentSafety,
        executePostCommentSafety: executePostCommentSafety,
        getSafetyStatus: getSafetyStatus,
        resetDailyCount: resetDailyCount
    };
} else {
    // Auto.js环境 - 使用全局导出
    this.initSafetyControl = initSafetyControl;
    this.checkNoteCommentCount = checkNoteCommentCount;
    this.checkDailyCommentLimit = checkDailyCommentLimit;
    this.executeCommentDelay = executeCommentDelay;
    this.checkCommentTimeInterval = checkCommentTimeInterval;
    this.recordCommentAction = recordCommentAction;
    this.performSafetyCheck = performSafetyCheck;
    this.executePreCommentSafety = executePreCommentSafety;
    this.executePostCommentSafety = executePostCommentSafety;
    this.getSafetyStatus = getSafetyStatus;
    this.resetDailyCount = resetDailyCount;
}

utils.log("SAFETY: 小红书安全控制模块 (xhs_safety_control.js) 加载完毕");
