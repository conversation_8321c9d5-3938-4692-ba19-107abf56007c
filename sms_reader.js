/**
 * 短信验证码读取模块
 * 使用ContentObserver监听新短信，兼容性好
 */

var utils = require(files.path("./utils.js"));

// 导入Android类
importClass(android.net.Uri);
importClass(android.database.ContentObserver);
importClass(android.os.Handler);
importClass(android.os.Looper);

/**
 * 短信读取器
 */
function SmsReader() {
    this.isListening = false;
    this.foundCode = null;
    this.contentObserver = null;
    this.handler = null;
}

/**
 * 检查短信权限
 */
SmsReader.prototype.requestSmsPermission = function () {
    utils.log("SMS_READER: 检查短信权限...");

    try {
        // 方法1：尝试直接检查权限
        if (typeof context !== 'undefined' && context.checkSelfPermission) {
            var readSmsPermission = context.checkSelfPermission("android.permission.READ_SMS");
            var receiveSmsPermission = context.checkSelfPermission("android.permission.RECEIVE_SMS");

            if (readSmsPermission === 0 && receiveSmsPermission === 0) {
                utils.log("SMS_READER: 短信权限检查通过");
                return true;
            } else {
                utils.log("SMS_READER: 权限状态 - READ_SMS: " + readSmsPermission + ", RECEIVE_SMS: " + receiveSmsPermission);
            }
        }

        // 方法2：尝试Auto.js的权限请求（可能不工作）
        if (typeof runtime !== 'undefined' && runtime.requestPermissions) {
            utils.log("SMS_READER: 尝试使用runtime.requestPermissions...");
            var permissions = ["android.permission.READ_SMS", "android.permission.RECEIVE_SMS"];
            var result = runtime.requestPermissions(permissions);
            utils.log("SMS_READER: runtime.requestPermissions结果: " + result);

            if (result) {
                utils.log("SMS_READER: 权限请求成功");
                return true;
            }
        }

        // 方法3：尝试实际读取短信来验证权限
        utils.log("SMS_READER: 尝试实际读取短信来验证权限...");
        var testResult = this.testSmsAccess();
        if (testResult) {
            utils.log("SMS_READER: 短信访问测试成功，权限正常");
            return true;
        }

        // 如果所有方法都失败，提示用户手动检查
        utils.log("SMS_READER: 自动权限检查失败，请手动确认权限状态");
        utils.log("SMS_READER: 设置 -> 应用管理 -> Auto.js -> 权限管理 -> 短信");
        utils.log("SMS_READER: 如果已授权，可能是Auto.js版本兼容性问题，将继续尝试运行");

        return true; // 假设权限已授予，继续执行

    } catch (error) {
        utils.log("SMS_READER: 权限检查异常: " + error, "error");
        utils.log("SMS_READER: 将假设权限已授予，继续执行");
        return true;
    }
};

/**
 * 测试短信访问权限
 */
SmsReader.prototype.testSmsAccess = function () {
    try {
        var uri = Uri.parse("content://sms/inbox");
        var cursor = context.getContentResolver().query(
            uri,
            ["_id"],
            null,
            null,
            "_id DESC LIMIT 1"
        );

        if (cursor) {
            var hasData = cursor.moveToFirst();
            cursor.close();
            utils.log("SMS_READER: 短信数据库访问测试 " + (hasData ? "成功" : "成功（无数据）"));
            return true;
        } else {
            utils.log("SMS_READER: 短信数据库访问测试失败 - cursor为null");
            return false;
        }
    } catch (error) {
        utils.log("SMS_READER: 短信数据库访问测试失败: " + error);
        return false;
    }
};

/**
 * 从短信内容中提取验证码
 */
SmsReader.prototype.extractVerificationCode = function (smsBody) {
    if (!smsBody) return null;

    // 多种验证码提取模式
    var patterns = [
        /验证码[：:\s]*(\d{4,8})/,           // 验证码：123456
        /验证码为[：:\s]*(\d{4,8})/,         // 验证码为123456  
        /code[：:\s]*(\d{4,8})/i,           // code: 123456
        /(\d{4,8})[，,。.\s]*为您的验证码/,    // 123456为您的验证码
        /您的验证码是[：:\s]*(\d{4,8})/,     // 您的验证码是123456
        /动态码[：:\s]*(\d{4,8})/,          // 动态码：123456
        /校验码[：:\s]*(\d{4,8})/,          // 校验码：123456
        /(\d{6})/,                         // 直接匹配6位数字
        /(\d{4})/                          // 直接匹配4位数字
    ];

    for (var i = 0; i < patterns.length; i++) {
        var match = smsBody.match(patterns[i]);
        if (match && match[1]) {
            var code = match[1];
            // 验证码长度通常是4-8位
            if (code.length >= 4 && code.length <= 8) {
                utils.log("SMS_READER: 提取到验证码: " + code);
                return code;
            }
        }
    }

    return null;
};

/**
 * 检查短信是否来自豆包相关服务
 */
SmsReader.prototype.isDoubaoSms = function (sender, body) {
    if (!sender && !body) return false;

    // 豆包相关的发送方标识
    var doubaoSenders = [
        "豆包", "DOUBAO", "字节", "ByteDance",
        "抖音", "TikTok", "今日头条", "头条"
    ];

    // 检查发送方
    if (sender) {
        for (var i = 0; i < doubaoSenders.length; i++) {
            if (sender.includes(doubaoSenders[i])) {
                return true;
            }
        }
    }

    // 检查短信内容
    if (body) {
        for (var i = 0; i < doubaoSenders.length; i++) {
            if (body.includes(doubaoSenders[i])) {
                return true;
            }
        }

        // 检查是否包含验证码相关词汇
        if (body.includes("验证码") || body.includes("动态码") ||
            body.includes("校验码") || body.includes("登录")) {
            return true;
        }
    }

    return false;
};

/**
 * 使用ContentObserver监听新短信
 */
SmsReader.prototype.startSmsObserver = function (callback) {
    utils.log("SMS_READER: 启动短信内容观察者...");

    var self = this;

    try {
        // 创建Handler
        if (!this.handler) {
            this.handler = new Handler(Looper.getMainLooper());
        }

        // 使用JavaAdapter创建ContentObserver（Auto.js正确方式）
        this.contentObserver = new JavaAdapter(ContentObserver, {
            onChange: function (selfChange, uri) {
                utils.log("SMS_READER: 检测到短信数据库变化");

                // 读取最新短信
                var code = self.getLatestSmsCode();
                if (code && callback) {
                    utils.log("SMS_READER: 从新短信中提取到验证码: " + code);
                    callback(code);
                }
            }
        }, this.handler);

        // 注册观察者
        var smsUri = Uri.parse("content://sms");
        context.getContentResolver().registerContentObserver(smsUri, true, this.contentObserver);

        utils.log("SMS_READER: 短信内容观察者启动成功");
        return true;

    } catch (error) {
        utils.log("SMS_READER: 启动短信观察者失败: " + error, "error");
        utils.log("SMS_READER: 可能的原因：");
        utils.log("SMS_READER: 1. Auto.js版本不支持ContentObserver");
        utils.log("SMS_READER: 2. Android系统版本限制");
        utils.log("SMS_READER: 3. 权限不足");
        return false;
    }
};

/**
 * 停止短信观察者
 */
SmsReader.prototype.stopSmsObserver = function () {
    try {
        if (this.contentObserver) {
            context.getContentResolver().unregisterContentObserver(this.contentObserver);
            this.contentObserver = null;
            utils.log("SMS_READER: 短信内容观察者已停止");
        }
    } catch (error) {
        utils.log("SMS_READER: 停止短信观察者失败: " + error, "error");
    }
};

/**
 * 获取最新短信中的验证码
 */
SmsReader.prototype.getLatestSmsCode = function () {
    try {
        var uri = Uri.parse("content://sms/inbox");
        var cursor = context.getContentResolver().query(
            uri,
            ["address", "body", "date"],
            null,
            null,
            "date DESC LIMIT 1"
        );

        if (cursor && cursor.moveToFirst()) {
            var address = cursor.getString(0) || "";
            var body = cursor.getString(1) || "";
            var date = cursor.getLong(2);

            cursor.close();

            // 检查是否是最近的短信（5分钟内）
            var currentTime = Date.now();
            if (currentTime - date < 5 * 60 * 1000) {
                utils.log("SMS_READER: 检查最新短信 - 发送方: " + address);
                utils.log("SMS_READER: 短信内容: " + body.substring(0, 50) + "...");

                // 检查是否是豆包相关短信并提取验证码
                if (this.isDoubaoSms(address, body)) {
                    return this.extractVerificationCode(body);
                }
            }
        }

        if (cursor) cursor.close();
        return null;

    } catch (error) {
        utils.log("SMS_READER: 读取最新短信失败: " + error, "error");
        return null;
    }
};

/**
 * 等待验证码（自动监听 + 手动输入备用）
 */
SmsReader.prototype.waitForVerificationCode = function (timeoutSeconds) {
    if (!timeoutSeconds) timeoutSeconds = 120;

    utils.log("SMS_READER: 开始等待验证码，超时: " + timeoutSeconds + "秒");
    utils.log("SMS_READER: 将同时启动自动监听和手动输入选项");

    var self = this;
    var code = null;
    var finished = false;

    // 启动短信监听
    var observerStarted = this.startSmsObserver(function (receivedCode) {
        if (!finished && receivedCode) {
            code = receivedCode;
            finished = true;
            utils.log("SMS_READER: 自动获取到验证码: " + code);
        }
    });

    if (!observerStarted) {
        utils.log("SMS_READER: 自动监听启动失败，使用手动输入模式");
        return this.waitForVerificationCodeManual(timeoutSeconds);
    }

    // 同时显示手动输入对话框
    ui.run(function () {
        dialogs.rawInput("豆包登录 - 验证码输入",
            "正在自动监听短信验证码...\n\n" +
            "如果自动获取失败，请手动输入：\n" +
            "1. 检查短信中的4-8位数字\n" +
            "2. 通常格式如：验证码123456\n" +
            "3. 请在" + timeoutSeconds + "秒内输入",
            "", function (input) {
                if (!finished && input && input.trim()) {
                    code = input.trim();
                    finished = true;
                    utils.log("SMS_READER: 用户手动输入验证码: " + code);
                }
            });
    });

    // 等待结果
    var waitCount = 0;
    var maxWait = timeoutSeconds; // 每秒检查一次

    while (!finished && waitCount < maxWait) {
        sleep(1000);
        waitCount++;

        if (waitCount % 10 === 0) {
            utils.log("SMS_READER: 等待验证码中... (" + waitCount + "/" + maxWait + ")");
        }
    }

    // 停止监听
    this.stopSmsObserver();

    if (code) {
        utils.log("SMS_READER: 成功获取验证码: " + code);
        return code;
    } else {
        utils.log("SMS_READER: 验证码获取超时");
        return null;
    }
};

/**
 * 手动输入验证码（备用方案）
 */
SmsReader.prototype.waitForVerificationCodeManual = function (timeoutSeconds) {
    if (!timeoutSeconds) timeoutSeconds = 120;

    utils.log("SMS_READER: 使用手动输入模式");

    // 显示对话框让用户输入验证码
    var code = null;

    ui.run(function () {
        dialogs.rawInput("豆包登录 - 验证码输入",
            "请输入收到的短信验证码:\n\n" +
            "提示：\n" +
            "1. 检查短信中的4-8位数字\n" +
            "2. 通常格式如：验证码123456\n" +
            "3. 请在" + timeoutSeconds + "秒内输入",
            "", function (input) {
                if (input && input.trim()) {
                    code = input.trim();
                    utils.log("SMS_READER: 用户输入验证码: " + code);
                } else {
                    utils.log("SMS_READER: 用户取消输入验证码");
                }
            });
    });

    // 等待用户输入
    var waitCount = 0;
    var maxWait = timeoutSeconds; // 每秒检查一次

    while (code === null && waitCount < maxWait) {
        sleep(1000);
        waitCount++;
    }

    if (code) {
        utils.log("SMS_READER: 成功获取验证码: " + code);
        return code;
    } else {
        utils.log("SMS_READER: 验证码输入超时");
        return null;
    }
};

/**
 * 获取最新验证码
 */
SmsReader.prototype.getLatestVerificationCode = function (timeoutSeconds) {
    return this.waitForVerificationCode(timeoutSeconds);
};

/**
 * 开始监听
 */
SmsReader.prototype.startListening = function (callback, timeoutSeconds) {
    var code = this.waitForVerificationCode(timeoutSeconds);
    if (callback) {
        callback(code);
    }
};

/**
 * 停止监听
 */
SmsReader.prototype.stopListening = function () {
    this.isListening = false;
    utils.log("SMS_READER: 已停止监听");
};

module.exports = {
    SmsReader: SmsReader
};

utils.log("短信验证码读取模块加载完毕 (sms_reader.js)");
