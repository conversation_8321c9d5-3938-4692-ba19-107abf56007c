/**
 * 豆包WebView自动化模块
 * 通过Auto.js的webview直接操作豆包网页版
 * 集成自动登录和改进的AI回复获取功能
 */

var utils = require(files.path("./utils.js"));
var DoubaoLoginManager = require(files.path("./doubao_login.js")).DoubaoLoginManager;

/**
 * 豆包WebView自动化类
 */
function DoubaoWebView() {
    this.webview = null;
    this.webviewControl = null;
    this.isReady = false;
    this.currentConversation = null;
    this.loginManager = null;
    this.phoneNumber = null;
}

/**
 * 初始化豆包WebView
 * @param {string} phoneNumber - 豆包登录手机号
 */
DoubaoWebView.prototype.init = function (phoneNumber) {
    this.phoneNumber = phoneNumber;
    try {
        utils.log("DOUBAO_WEBVIEW: 开始初始化豆包WebView...");

        // 创建带控制按钮的悬浮窗
        utils.log("DOUBAO_WEBVIEW: 创建悬浮窗...");
        this.webview = floaty.rawWindow(
            `<vertical>
                <horizontal bg="#ff0000" h="50">
                    <text text="豆包AI" textColor="#ffffff" layout_weight="1" gravity="center"/>
                    <button id="minimizeBtn" text="最小化" w="80" h="40" textColor="#ffffff" bg="#ff6600"/>
                    <button id="closeBtn" text="关闭" w="80" h="40" textColor="#ffffff" bg="#cc0000"/>
                </horizontal>
                <webview id="webview" h="*" w="*" />
            </vertical>`
        );

        if (!this.webview) {
            throw new Error("无法创建悬浮窗");
        }

        utils.log("DOUBAO_WEBVIEW: 设置悬浮窗属性...");
        // 设置为小窗口，不影响小红书操作
        var windowWidth = Math.min(400, device.width * 0.8);
        var windowHeight = Math.min(600, device.height * 0.7);
        var windowX = device.width - windowWidth - 20; // 靠右显示
        var windowY = 100; // 距离顶部100像素

        this.webview.setPosition(windowX, windowY);
        this.webview.setSize(windowWidth, windowHeight);
        this.webview.setTouchable(true);

        // 添加按钮事件
        var self = this;
        this.isMinimized = false;

        // 最小化按钮
        this.webview.minimizeBtn.click(function () {
            if (!self.isMinimized) {
                // 最小化：只显示标题栏
                self.webview.setSize(200, 50);
                self.webview.setPosition(device.width - 220, 50);
                self.webview.minimizeBtn.setText("还原");
                self.isMinimized = true;
                utils.log("DOUBAO_WEBVIEW: 窗口已最小化");
            } else {
                // 还原窗口
                var windowWidth = Math.min(400, device.width * 0.8);
                var windowHeight = Math.min(600, device.height * 0.7);
                var windowX = device.width - windowWidth - 20;
                var windowY = 100;

                self.webview.setSize(windowWidth, windowHeight);
                self.webview.setPosition(windowX, windowY);
                self.webview.minimizeBtn.setText("最小化");
                self.isMinimized = false;
                utils.log("DOUBAO_WEBVIEW: 窗口已还原");
            }
        });

        // 关闭按钮
        this.webview.closeBtn.click(function () {
            utils.log("DOUBAO_WEBVIEW: 用户点击关闭按钮");
            self.cleanup();
        });

        utils.log("DOUBAO_WEBVIEW: 等待悬浮窗创建完成...");
        sleep(3000);

        utils.log("DOUBAO_WEBVIEW: 获取WebView控件...");
        this.webviewControl = this.webview.webview;
        if (!this.webviewControl) {
            throw new Error("无法获取webview控件");
        }

        var self = this;
        utils.log("DOUBAO_WEBVIEW: 配置WebView...");
        ui.run(function () {
            try {
                var settings = self.webviewControl.getSettings();
                settings.setJavaScriptEnabled(true);
                settings.setDomStorageEnabled(true);
                settings.setAllowFileAccess(true);
                settings.setAllowContentAccess(true);

                // 使用移动端User-Agent，避免显示PC版
                settings.setUserAgentString(
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
                );

                utils.log("DOUBAO_WEBVIEW: 加载豆包移动版网站...");
                self.webviewControl.loadUrl("https://www.doubao.com/chat/");

            } catch (configError) {
                utils.log("DOUBAO_WEBVIEW: 配置WebView失败: " + configError, "error");
            }
        });

        utils.log("DOUBAO_WEBVIEW: 等待页面加载...");
        sleep(15000); // 等待15秒让页面充分加载

        // 初始化登录管理器
        if (this.phoneNumber) {
            utils.log("DOUBAO_WEBVIEW: 初始化登录管理器...");
            this.loginManager = new DoubaoLoginManager();
            this.loginManager.init(this.webviewControl);

            // 检查登录状态并自动登录
            utils.log("DOUBAO_WEBVIEW: 检查登录状态...");
            var isLoggedIn = this.loginManager.checkLoginStatus();

            if (!isLoggedIn) {
                utils.log("DOUBAO_WEBVIEW: 需要登录，开始自动登录流程...");
                var loginResult = this.loginManager.autoLogin(this.phoneNumber);

                if (loginResult.success) {
                    utils.log("DOUBAO_WEBVIEW: 自动登录成功");
                } else {
                    utils.log("DOUBAO_WEBVIEW: 自动登录失败: " + loginResult.message, "warn");
                }
            } else {
                utils.log("DOUBAO_WEBVIEW: 已经登录，跳过登录流程");
            }
        } else {
            utils.log("DOUBAO_WEBVIEW: 未提供手机号，跳过登录流程");
        }

        utils.log("DOUBAO_WEBVIEW: 滚动到页面底部...");
        this.scrollToBottom();

        utils.log("DOUBAO_WEBVIEW: 检查页面加载状态...");
        this.checkPageElements();

        utils.log("DOUBAO_WEBVIEW: 设置为就绪状态");
        this.isReady = true;

        utils.log("DOUBAO_WEBVIEW: 豆包WebView初始化完成");
        return true;

    } catch (error) {
        utils.log("DOUBAO_WEBVIEW: 初始化失败: " + error, "error");
        this.cleanup();
        return false;
    }
};

/**
 * 等待页面加载完成
 */
DoubaoWebView.prototype.waitForPageLoad = function () {
    utils.log("DOUBAO_WEBVIEW: 等待页面加载...");

    var maxWait = 30; // 最多等待30秒
    var waitCount = 0;

    while (waitCount < maxWait) {
        try {
            // 检查页面是否加载完成
            var result = this.executeJS(`
                (function() {
                    if (document.readyState === 'complete') {
                        // 检查是否有输入框（表示页面已加载）
                        var inputBox = document.querySelector('textarea') || 
                                     document.querySelector('input[type="text"]') ||
                                     document.querySelector('[contenteditable="true"]');
                        return inputBox ? 'ready' : 'loading';
                    }
                    return 'loading';
                })();
            `);

            if (result === 'ready') {
                this.isReady = true;
                utils.log("DOUBAO_WEBVIEW: 页面加载完成");
                return true;
            }
        } catch (e) {
            // 忽略JS执行错误，继续等待
        }

        sleep(1000);
        waitCount++;
    }

    utils.log("DOUBAO_WEBVIEW: 页面加载超时", "warn");
    return false;
};

/**
 * 执行JavaScript代码
 */
DoubaoWebView.prototype.executeJS = function (jsCode) {
    if (!this.webviewControl) {
        throw new Error("WebView未初始化");
    }

    var result = null;
    var completed = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(jsCode, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                result = value ? value.replace(/^"|"$/g, '') : null; // 移除引号
                completed = true;
            }
        }));
    });

    // 等待JS执行完成
    var waitCount = 0;
    while (!completed && waitCount < 50) { // 最多等待5秒
        sleep(100);
        waitCount++;
    }

    return result;
};

/**
 * 滚动到页面底部
 */
DoubaoWebView.prototype.scrollToBottom = function () {
    utils.log("DOUBAO_WEBVIEW: 滚动到页面底部...");

    this.executeJS(`
        (function() {
            // 滚动到页面底部，确保输入框可见
            window.scrollTo(0, document.body.scrollHeight);

            // 也尝试滚动主要内容区域
            var containers = document.querySelectorAll('div[class*="scroll"], div[class*="container"]');
            for (var i = 0; i < containers.length; i++) {
                containers[i].scrollTop = containers[i].scrollHeight;
            }

            return 'scrolled';
        })();
    `);

    sleep(2000); // 等待滚动完成
};

/**
 * 检查页面元素是否加载完成
 */
DoubaoWebView.prototype.checkPageElements = function () {
    utils.log("DOUBAO_WEBVIEW: 开始检查页面元素...");

    var elementsInfo = this.executeJS(`
        (function() {
            var results = [];

            // 检查输入框
            results.push("=== 输入框检查 ===");
            var inputSelectors = [
                'textarea[data-testid="chat_input_input"]',  // 豆包专用输入框
                'textarea[placeholder*="发消息"]',
                'textarea[placeholder*="输入"]',
                'textarea',
                '[contenteditable="true"]'
            ];

            var foundInput = false;
            inputSelectors.forEach(function(selector) {
                try {
                    var elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        foundInput = true;
                        elements.forEach(function(el, index) {
                            var info = selector + '[' + index + ']';
                            if (el.placeholder) info += ' placeholder="' + el.placeholder + '"';
                            if (el.className) info += ' class="' + el.className + '"';
                            results.push('✓ 找到: ' + info);
                        });
                    }
                } catch(e) {
                    results.push('✗ 检查失败: ' + selector + ' - ' + e.message);
                }
            });

            if (!foundInput) {
                results.push('✗ 未找到任何输入框');
            }

            // 检查发送按钮
            results.push("=== 发送按钮检查 ===");
            var sendSelectors = [
                'button[type="submit"]',
                'button:last-child',
                '.send-btn',
                '[aria-label*="发送"]',
                'button svg',
                'button'
            ];

            var foundSend = false;
            sendSelectors.forEach(function(selector) {
                try {
                    var elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        foundSend = true;
                        results.push('✓ 找到 ' + elements.length + ' 个: ' + selector);
                    }
                } catch(e) {
                    results.push('✗ 检查失败: ' + selector + ' - ' + e.message);
                }
            });

            if (!foundSend) {
                results.push('✗ 未找到任何按钮');
            }

            // 检查页面标题
            results.push("=== 页面信息 ===");
            results.push('页面标题: ' + document.title);
            results.push('页面URL: ' + window.location.href);

            return results.join('\\n');
        })();
    `);

    utils.log("DOUBAO_WEBVIEW: 页面元素检查结果:");
    utils.log(elementsInfo || "无法获取页面元素信息");
};

/**
 * 检查是否需要登录并自动点击登录按钮 (暂时注释)
 */
DoubaoWebView.prototype.handleLogin = function () {
    utils.log("DOUBAO_WEBVIEW: 检查登录状态...");

    var loginResult = this.executeJS(`
        (function() {
            // 查找登录按钮
            var loginBtn = document.querySelector('button:contains("登录")') ||
                          document.querySelector('button:contains("登陆")') ||
                          document.querySelector('.login-btn') ||
                          document.querySelector('[class*="login"]');

            if (loginBtn && loginBtn.textContent.includes('登录')) {
                loginBtn.click();
                return 'login_clicked';
            }

            // 检查是否已经登录（有用户头像或输入框）
            var inputBox = document.querySelector('textarea[placeholder*="发消息"]') ||
                          document.querySelector('textarea[placeholder*="输入"]');

            if (inputBox) {
                return 'already_logged_in';
            }

            return 'login_needed';
        })();
    `);

    utils.log("DOUBAO_WEBVIEW: 登录检查结果: " + loginResult);

    if (loginResult === 'login_clicked') {
        utils.log("DOUBAO_WEBVIEW: 已点击登录按钮，请手动完成登录...");
        sleep(10000); // 等待10秒让用户手动登录
        return true;
    } else if (loginResult === 'already_logged_in') {
        utils.log("DOUBAO_WEBVIEW: 已经登录");
        return true;
    } else {
        utils.log("DOUBAO_WEBVIEW: 需要登录但未找到登录按钮");
        return false;
    }
};

/**
 * 发送消息给豆包并获取回复
 */
DoubaoWebView.prototype.sendMessage = function (message) {
    if (!this.isReady) {
        throw new Error("WebView未准备就绪");
    }

    try {
        utils.log("DOUBAO_WEBVIEW: 发送消息: " + message.substring(0, 50) + "...");

        // 1. 先处理登录 (暂时注释)
        // if (!this.handleLogin()) {
        //     throw new Error("登录失败");
        // }

        utils.log("DOUBAO_WEBVIEW: 跳过登录检查，直接尝试发送消息...");

        // 2. 暂停让用户观察页面状态
        utils.log("DOUBAO_WEBVIEW: 暂停10秒，请观察页面是否正常显示...");
        sleep(10000);

        // 3. 再次检查页面元素
        utils.log("DOUBAO_WEBVIEW: 发送前再次检查页面元素...");
        this.checkPageElements();

        // 4. 使用坐标点击和模拟输入的方法
        utils.log("DOUBAO_WEBVIEW: 尝试坐标点击和模拟输入...");

        var inputSuccess = this.executeJS(`
            (function() {
                var results = [];

                // 查找所有可能的输入元素
                var inputElements = [];

                // 优先查找豆包的专用输入框
                var chatInput = document.querySelector('textarea[data-testid="chat_input_input"]');
                if (chatInput) {
                    inputElements.push(chatInput);
                    results.push('✓ 找到豆包专用输入框: data-testid="chat_input_input"');
                } else {
                    results.push('✗ 未找到豆包专用输入框');
                }

                // 查找其他输入元素作为备用
                var textareas = document.querySelectorAll('textarea');
                var inputs = document.querySelectorAll('input');
                var editables = document.querySelectorAll('[contenteditable]');

                results.push('找到 ' + textareas.length + ' 个textarea');
                results.push('找到 ' + inputs.length + ' 个input');
                results.push('找到 ' + editables.length + ' 个contenteditable');

                // 如果没找到专用输入框，合并其他输入元素
                if (!chatInput) {
                    for (var i = 0; i < textareas.length; i++) inputElements.push(textareas[i]);
                    for (var i = 0; i < inputs.length; i++) inputElements.push(inputs[i]);
                    for (var i = 0; i < editables.length; i++) inputElements.push(editables[i]);
                }

                if (inputElements.length === 0) {
                    return 'no_input_found|' + results.join(';');
                }

                // 直接使用专用输入框，或选择第一个可见的输入框
                var targetInput = null;

                if (chatInput) {
                    targetInput = chatInput;
                    results.push('使用豆包专用输入框');
                } else {
                    // 找到第一个可见的输入框
                    for (var i = 0; i < inputElements.length; i++) {
                        var element = inputElements[i];
                        var rect = element.getBoundingClientRect();
                        var area = rect.width * rect.height;

                        results.push('元素[' + i + ']: ' + element.tagName + ', 面积=' + area + ', 可见=' + (rect.width > 0 && rect.height > 0));

                        if (rect.width > 0 && rect.height > 0) {
                            targetInput = element;
                            results.push('选择第一个可见输入元素: ' + targetInput.tagName);
                            break;
                        }
                    }
                }

                if (!targetInput) {
                    return 'no_visible_input|' + results.join(';');
                }

                // 获取元素的屏幕坐标
                var rect = targetInput.getBoundingClientRect();
                var centerX = rect.left + rect.width / 2;
                var centerY = rect.top + rect.height / 2;

                results.push('输入框中心坐标: (' + centerX + ', ' + centerY + ')');

                // 先点击元素获得焦点
                targetInput.click();
                targetInput.focus();
                results.push('已点击并聚焦输入框');

                // 添加视觉反馈 - 改变输入框背景色
                var originalBg = targetInput.style.backgroundColor;
                targetInput.style.backgroundColor = 'yellow';
                results.push('已设置输入框背景为黄色（视觉反馈）');

                // 清空内容
                targetInput.value = '';
                targetInput.textContent = '';
                targetInput.innerHTML = '';

                // 输入内容 - 使用多种方法确保输入成功
                var message = ${JSON.stringify(message)};

                // 方法1: 直接设置value
                if (targetInput.tagName.toLowerCase() === 'textarea' || targetInput.tagName.toLowerCase() === 'input') {
                    targetInput.value = message;
                    results.push('方法1: 设置value属性');
                } else {
                    targetInput.textContent = message;
                    results.push('方法1: 设置textContent');
                }

                // 方法2: 模拟逐字符输入
                targetInput.focus();
                targetInput.value = '';

                // 逐字符输入，模拟真实用户输入
                for (var charIndex = 0; charIndex < message.length; charIndex++) {
                    var char = message.charAt(charIndex);
                    targetInput.value += char;

                    // 触发每个字符的输入事件
                    var inputEvent = new Event('input', { bubbles: true, cancelable: true });
                    targetInput.dispatchEvent(inputEvent);

                    // 模拟按键事件
                    var keydownEvent = new KeyboardEvent('keydown', {
                        key: char,
                        bubbles: true,
                        cancelable: true
                    });
                    targetInput.dispatchEvent(keydownEvent);

                    var keyupEvent = new KeyboardEvent('keyup', {
                        key: char,
                        bubbles: true,
                        cancelable: true
                    });
                    targetInput.dispatchEvent(keyupEvent);
                }
                results.push('方法2: 逐字符模拟输入完成');

                // 方法3: 触发各种事件
                var events = ['focus', 'input', 'change', 'keyup', 'blur', 'focusout'];
                for (var i = 0; i < events.length; i++) {
                    var event = new Event(events[i], { bubbles: true, cancelable: true });
                    targetInput.dispatchEvent(event);
                }
                results.push('方法3: 触发所有相关事件');

                // 方法4: 使用React/Vue的方式触发更新
                if (targetInput._valueTracker) {
                    targetInput._valueTracker.setValue('');
                }

                // 模拟React的onChange
                var nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, "value").set;
                nativeInputValueSetter.call(targetInput, message);

                var reactEvent = new Event('input', { bubbles: true });
                targetInput.dispatchEvent(reactEvent);
                results.push('方法4: React/Vue兼容方式');

                // 等待一下再恢复背景色
                setTimeout(function() {
                    targetInput.style.backgroundColor = originalBg;
                }, 2000);

                // 验证输入结果
                var finalValue = targetInput.value || targetInput.textContent || targetInput.innerHTML;
                results.push('最终输入值: ' + finalValue.substring(0, 50) + '...');

                // 检查输入是否真的成功
                if (finalValue && finalValue.length > 0 && finalValue.includes('分享链接内容')) {
                    results.push('✓ 输入验证成功 - 内容已正确输入');

                    // 再次聚焦确保输入框处于活动状态
                    targetInput.focus();

                    return 'input_success|' + results.join(';') + '|COORDS:' + centerX + ',' + centerY;
                } else {
                    results.push('✗ 输入验证失败 - 内容未正确输入，尝试备用方案');

                    // 备用方案：尝试使用document.execCommand
                    targetInput.focus();
                    targetInput.select();
                    document.execCommand('insertText', false, message);
                    results.push('备用方案: 使用document.execCommand');

                    // 再次验证
                    var backupValue = targetInput.value || targetInput.textContent || targetInput.innerHTML;
                    results.push('备用方案后的值: ' + backupValue.substring(0, 50) + '...');

                    return 'input_partial|' + results.join(';') + '|COORDS:' + centerX + ',' + centerY;
                }
            })();
        `);

        utils.log("DOUBAO_WEBVIEW: 输入框操作结果: " + inputSuccess);

        // 如果JavaScript方法失败，尝试坐标点击作为备用方案
        if (!inputSuccess.startsWith('input_success') && !inputSuccess.startsWith('input_partial')) {
            utils.log("DOUBAO_WEBVIEW: JavaScript输入失败，尝试坐标点击备用方案...");

            // 尝试点击屏幕下方的输入区域（通常输入框在底部）
            var inputX = device.width / 2;  // 屏幕中央
            var inputY = device.height * 0.9;  // 屏幕底部90%位置

            utils.log("DOUBAO_WEBVIEW: 尝试点击坐标 (" + inputX + ", " + inputY + ")");
            click(inputX, inputY);
            sleep(1000);

            // 尝试输入文本
            utils.log("DOUBAO_WEBVIEW: 尝试直接输入文本...");
            setText(message);
            sleep(1000);

            inputSuccess = "coordinate_input|使用坐标点击和setText方法";
        }

        sleep(5000); // 等待输入完成，增加到5秒让输入充分生效

        // 5. 查找并点击发送按钮
        utils.log("DOUBAO_WEBVIEW: 查找发送按钮...");

        var sendSuccess = this.executeJS(`
            (function() {
                var results = [];

                // 查找发送按钮
                var sendButton = null;

                // 尝试多种选择器查找发送按钮
                var selectors = [
                    '#flow-end-msg-send',
                    'button[id*="send"]',
                    'button[class*="send"]',
                    'button:last-child',
                    'button'
                ];

                for (var i = 0; i < selectors.length; i++) {
                    var buttons = document.querySelectorAll(selectors[i]);
                    results.push('选择器 ' + selectors[i] + ': 找到 ' + buttons.length + ' 个');

                    if (buttons.length > 0) {
                        // 找到最后一个可见的按钮
                        for (var j = buttons.length - 1; j >= 0; j--) {
                            var btn = buttons[j];
                            var rect = btn.getBoundingClientRect();
                            if (rect.width > 0 && rect.height > 0) {
                                sendButton = btn;
                                results.push('使用按钮: ' + selectors[i] + '[' + j + ']');
                                break;
                            }
                        }
                        if (sendButton) break;
                    }
                }

                if (sendButton) {
                    // 获取按钮坐标
                    var rect = sendButton.getBoundingClientRect();
                    var centerX = rect.left + rect.width / 2;
                    var centerY = rect.top + rect.height / 2;

                    results.push('发送按钮坐标: (' + centerX + ', ' + centerY + ')');

                    // 添加视觉反馈 - 改变按钮背景色
                    var originalBg = sendButton.style.backgroundColor;
                    sendButton.style.backgroundColor = 'red';
                    results.push('已设置发送按钮背景为红色（视觉反馈）');

                    // 点击按钮
                    sendButton.click();
                    results.push('已点击发送按钮');

                    // 等待一下再恢复背景色
                    setTimeout(function() {
                        sendButton.style.backgroundColor = originalBg;
                    }, 2000);

                    return 'button_clicked|' + results.join(';') + '|COORDS:' + centerX + ',' + centerY;
                } else {
                    results.push('未找到发送按钮，尝试按Enter键');

                    // 查找输入框并按Enter
                    var inputElement = document.querySelector('textarea') ||
                                     document.querySelector('input') ||
                                     document.querySelector('[contenteditable]');

                    if (inputElement) {
                        inputElement.focus();

                        // 模拟按Enter键
                        var enterEvent = new KeyboardEvent('keydown', {
                            key: 'Enter',
                            code: 'Enter',
                            keyCode: 13,
                            which: 13,
                            bubbles: true,
                            cancelable: true
                        });
                        inputElement.dispatchEvent(enterEvent);

                        results.push('已在输入框按Enter键');
                        return 'enter_sent|' + results.join(';');
                    } else {
                        return 'no_send_method|' + results.join(';');
                    }
                }
            })();
        `);

        utils.log("DOUBAO_WEBVIEW: 发送按钮操作结果: " + sendSuccess);

        // 如果JavaScript方法失败，尝试坐标点击发送按钮
        if (!sendSuccess.startsWith('button_clicked') && !sendSuccess.startsWith('enter_sent')) {
            utils.log("DOUBAO_WEBVIEW: JavaScript发送失败，尝试坐标点击发送按钮...");

            // 尝试点击屏幕右下角的发送按钮位置
            var sendX = device.width * 0.9;  // 屏幕右侧90%位置
            var sendY = device.height * 0.9;  // 屏幕底部90%位置

            utils.log("DOUBAO_WEBVIEW: 尝试点击发送按钮坐标 (" + sendX + ", " + sendY + ")");
            click(sendX, sendY);
            sleep(1000);

            sendSuccess = "coordinate_send|使用坐标点击发送按钮";
        }

        utils.log("DOUBAO_WEBVIEW: 消息已发送，等待回复...");

        // 3. 等待并获取回复
        return this.waitForResponse();

    } catch (error) {
        utils.log("DOUBAO_WEBVIEW: 发送消息失败: " + error, "error");
        return null;
    }
};

/**
 * 等待豆包回复
 */
DoubaoWebView.prototype.waitForResponse = function () {
    utils.log("DOUBAO_WEBVIEW: 等待豆包回复...");

    // 先等待5秒让消息发送完成
    sleep(5000);
    utils.log("DOUBAO_WEBVIEW: 等待5秒后开始检查回复...");

    var maxWait = 120; // 增加到120秒
    var waitCount = 0;
    var lastResponseLength = 0;
    var stableCount = 0; // 连续稳定次数

    while (waitCount < maxWait) {
        try {
            var response = this.executeJS(`
                (function() {
                    // 使用改进的豆包AI回复获取逻辑
                    var results = [];
                    var foundResponse = '';

                    // 方法1: 使用正确的豆包AI回复选择器
                    var aiMessages = document.querySelectorAll('div[data-testid="receive_message"]');
                    results.push('找到 ' + aiMessages.length + ' 个AI回复消息');

                    // 查找最新的AI回复消息
                    if (aiMessages.length > 0) {
                        var latestAiMessage = aiMessages[aiMessages.length - 1];

                        // 查找消息文本内容
                        var textContent = latestAiMessage.querySelector('div[data-testid="message_text_content"]');
                        if (textContent) {
                            var text = textContent.textContent || textContent.innerText || '';

                            if (text.length > 15) {
                                foundResponse = text.trim();
                                results.push('✅ 找到AI回复: ' + foundResponse.substring(0, 50) + '...');

                                // 如果是合理长度的回复，立即标记为完成
                                if (foundResponse.length > 20 && foundResponse.length < 300) {
                                    results.push('🎯 检测到合理长度的AI回复，标记为完成');
                                    foundResponse = 'IMMEDIATE_RETURN:' + foundResponse;
                                }
                            }
                        }
                    }

                    // 方法2: 如果没找到，使用备用选择器
                    if (!foundResponse) {
                        results.push('使用备用方法查找AI回复...');

                        // 查找包含markdown内容的容器
                        var containers = document.querySelectorAll('div[class*="markdown"], div[class*="flow-markdown"], div[class*="container"], div[class*="paragraph"]');

                        for (var i = containers.length - 1; i >= 0; i--) {
                            var container = containers[i];
                            var text = container.textContent || container.innerText || '';

                            // 更严格的过滤条件，排除导航和UI元素
                            if (text.length > 15 && text.length < 500 &&
                                !text.includes('分享链接内容') &&
                                !text.includes('你好，我是豆包') &&
                                !text.includes('AI 搜索') &&
                                !text.includes('帮我写作') &&
                                !text.includes('编程') &&
                                !text.includes('图像生成') &&
                                !text.includes('更多') &&
                                !text.includes('To pick up') &&
                                !text.includes('draggable') &&
                                !text.includes('发消息') &&
                                !text.includes('输入') &&
                                !text.includes('选择技能')) {

                                foundResponse = text.trim();
                                results.push('✅ 备用方法找到AI回复: ' + foundResponse.substring(0, 50) + '...');
                                break;
                            }
                        }
                    }

                    // 如果没找到明确的消息容器，查找最近添加的文本内容
                    if (!foundResponse) {
                        results.push('未找到明确消息容器，查找最近的文本内容...');

                        var allDivs = document.querySelectorAll('div');
                        var validTexts = [];

                        for (var i = allDivs.length - 1; i >= 0; i--) {
                            var div = allDivs[i];
                            var text = div.textContent || div.innerText || '';

                            // 过滤条件：长度合适，不包含提示文字，不是输入框
                            if (text.length > 10 && text.length < 1000 &&
                                !text.includes('版权') && !text.includes('©') &&
                                !text.includes('豆包') && !text.includes('发消息') &&
                                !text.includes('对话记录将会出现在这里') &&
                                !text.includes('新建的对话') &&
                                !text.includes('选择技能') &&
                                !text.includes('输入 @') &&
                                !div.querySelector('textarea') && !div.querySelector('input') &&
                                !div.querySelector('button')) {

                                validTexts.push(text.trim());
                                if (validTexts.length >= 5) break; // 只取前5个
                            }
                        }

                        results.push('找到 ' + validTexts.length + ' 个有效文本');
                        if (validTexts.length > 0) {
                            // 显示所有找到的文本，方便调试
                            for (var j = 0; j < Math.min(validTexts.length, 3); j++) {
                                results.push('文本[' + j + ']: ' + validTexts[j].substring(0, 50) + '...');
                            }
                            foundResponse = validTexts[0]; // 取第一个（最新的）
                            results.push('使用最新文本作为回复');
                        }
                    }

                    return foundResponse + '|DEBUG:' + results.join(';');
                })();
            `);

            if (response && response.length > 0) {
                // 分离实际回复和调试信息
                var parts = response.split('|DEBUG:');
                var actualResponse = parts[0];
                var debugInfo = parts[1] || '';

                utils.log("DOUBAO_WEBVIEW: 回复查找调试信息: " + debugInfo);

                // 检查是否有立即返回标记
                if (actualResponse && actualResponse.startsWith('IMMEDIATE_RETURN:')) {
                    var finalResponse = actualResponse.substring('IMMEDIATE_RETURN:'.length);
                    utils.log("DOUBAO_WEBVIEW: 检测到立即返回标记，直接返回: " + finalResponse.substring(0, 100) + "...");
                    return finalResponse;
                }

                if (actualResponse && actualResponse.trim().length > 0) {
                    // 检查是否是有效的AI回复（不包含UI元素）
                    var isValidResponse = !actualResponse.includes('AI 搜索') &&
                        !actualResponse.includes('帮我写作') &&
                        !actualResponse.includes('To pick up') &&
                        actualResponse.length < 500;

                    if (isValidResponse) {
                        // 检查回复是否还在增长（流式输出）
                        if (actualResponse.length > lastResponseLength) {
                            lastResponseLength = actualResponse.length;
                            stableCount = 0; // 重置稳定计数
                            waitCount = Math.max(0, waitCount - 2); // 重置等待时间
                            utils.log("DOUBAO_WEBVIEW: 有效回复还在增长，当前长度: " + actualResponse.length);
                        } else if (actualResponse.length === lastResponseLength && lastResponseLength > 0) {
                            stableCount++;
                            utils.log("DOUBAO_WEBVIEW: 有效回复长度稳定 " + stableCount + " 次，长度: " + actualResponse.length);

                            // 连续3次稳定才认为完成
                            if (stableCount >= 3) {
                                utils.log("DOUBAO_WEBVIEW: 获取到完整回复: " + actualResponse.substring(0, 100) + "...");
                                return actualResponse;
                            }
                        }
                    } else {
                        utils.log("DOUBAO_WEBVIEW: 获取到的内容包含UI元素，继续等待有效回复...");
                    }
                } else {
                    utils.log("DOUBAO_WEBVIEW: 未获取到有效回复内容");
                }
            }
        } catch (e) {
            // 忽略JS执行错误
        }

        sleep(1000);
        waitCount++;
    }

    utils.log("DOUBAO_WEBVIEW: 等待回复超时", "warn");
    return null;
};

/**
 * 清理资源
 */
DoubaoWebView.prototype.cleanup = function () {
    var self = this;
    try {
        if (this.webview) {
            ui.run(function () {
                try {
                    self.webview.close();
                } catch (closeError) {
                    utils.log("DOUBAO_WEBVIEW: 关闭webview时出错: " + closeError, "warn");
                }
            });
            this.webview = null;
        }
        this.isReady = false;
        utils.log("DOUBAO_WEBVIEW: 资源已清理");
    } catch (error) {
        utils.log("DOUBAO_WEBVIEW: 清理资源时出错: " + error, "warn");
    }
};

/**
 * 生成评论的主函数
 * @param {string} shareLink - 分享链接内容
 * @param {string} promptTemplate - 提示词模板
 * @param {string} phoneNumber - 豆包登录手机号
 */
function generateCommentWithDoubaoWebView(shareLink, promptTemplate, phoneNumber) {
    var doubaoWebView = new DoubaoWebView();

    try {
        // 初始化WebView，传递手机号
        if (!doubaoWebView.init(phoneNumber)) {
            return { success: false, comment: null, error: "WebView初始化失败" };
        }

        // 构建完整的提示词
        var fullPrompt = "分享链接内容：\n" + shareLink + "\n\n" + promptTemplate;

        // 发送消息并获取回复
        var response = doubaoWebView.sendMessage(fullPrompt);

        if (response && response.trim() !== "") {
            utils.log("DOUBAO_WEBVIEW: 获取到回复: " + response.substring(0, 50) + "...");

            // 不要立即关闭WebView，让调用方决定何时关闭
            utils.log("DOUBAO_WEBVIEW: AI回复生成完成，保持WebView打开等待评论发布完成");

            return {
                success: true,
                comment: response.trim(),
                error: null,
                webview: doubaoWebView // 返回WebView实例，让调用方控制关闭时机
            };
        } else {
            utils.log("DOUBAO_WEBVIEW: 未获取到有效回复，关闭窗口");
            doubaoWebView.cleanup();
            return { success: false, comment: null, error: "未获取到有效回复" };
        }

    } catch (error) {
        utils.log("DOUBAO_WEBVIEW: 生成评论失败: " + error, "error");
        return { success: false, comment: null, error: error.toString() };
    }
    // 注意：不在这里清理资源，让调用者决定何时关闭
}

module.exports = {
    DoubaoWebView: DoubaoWebView,
    generateCommentWithDoubaoWebView: generateCommentWithDoubaoWebView
};

utils.log("豆包WebView模块加载完毕 (doubao_webview.js)");
