// test_account_switch.js - 多账号切换功能测试脚本
// 专门用于测试小红书账号切换和登录流程

"ui";

// 导入必要的模块
const utils = require('./utils.js');
const accountManager = require('./xhs_account_manager.js');

// 测试UI布局
ui.layout(
    <vertical padding="16dp">
        <text textSize="18sp" textColor="#2196F3" text="小红书多账号切换测试工具" gravity="center" marginBottom="16dp" />

        <card cardBackgroundColor="#fff3cd" cardCornerRadius="8dp" cardElevation="2dp" margin="8dp">
            <vertical padding="12dp">
                <text textSize="14sp" textColor="#856404" textStyle="bold" text="⚠️ 测试说明" />
                <text textSize="12sp" textColor="#856404" text="• 请确保小红书app已安装且可正常使用" marginTop="4dp" />
                <text textSize="12sp" textColor="#856404" text="• 测试前请手动关闭小红书app" marginTop="2dp" />
                <text textSize="12sp" textColor="#856404" text="• 请准备至少2个有效的小红书账号" marginTop="2dp" />
                <text textSize="12sp" textColor="#856404" text="• 测试过程中请勿手动操作手机" marginTop="2dp" />
            </vertical>
        </card>

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试账号配置:" />
        <text textSize="12sp" text="格式: 用户名,密码 (每行一组)" />
        <input id="testAccountList" hint="例如:&#10;***********,password123&#10;***********,password456" lines="4" />

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试选项:" />
        <checkbox id="testAppControl" text="测试应用启动/关闭" checked="true" />
        <checkbox id="testLogout" text="测试退出登录流程" checked="true" />
        <checkbox id="testLogin" text="测试登录流程" checked="true" />
        <checkbox id="testFullSwitch" text="测试完整账号切换" checked="false" />

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试控制:" />
        <horizontal>
            <button id="startTestBtn" text="开始测试" layout_weight="1" bg="#4CAF50" textColor="white" />
            <button id="stopTestBtn" text="停止测试" layout_weight="1" bg="#f44336" textColor="white" />
        </horizontal>

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试状态:" />
        <text id="testStatus" text="状态: 未开始" textSize="14sp" />
        <text id="currentStep" text="当前步骤: 无" textSize="12sp" textColor="#666666" />

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试日志:" />
        <ScrollView layout_weight="1">
            <text id="testLog" text="" textSize="10sp" />
        </ScrollView>

        <horizontal marginTop="16dp">
            <button id="clearLogBtn" text="清空日志" layout_weight="1" />
            <button id="exportLogBtn" text="导出日志" layout_weight="1" />
        </horizontal>
    </vertical>
);

// 全局变量
let isTestRunning = false;
let testResults = [];
let currentTestStep = "";

// 日志函数
function addTestLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;

    ui.run(() => {
        const currentLog = ui.testLog.getText().toString();
        ui.testLog.setText(currentLog + logMessage + "\n");
    });

    console.log(logMessage);
    utils.log("TEST: " + message);
}

function updateTestStatus(status) {
    ui.run(() => {
        ui.testStatus.setText("状态: " + status);
    });
}

function updateCurrentStep(step) {
    currentTestStep = step;
    ui.run(() => {
        ui.currentStep.setText("当前步骤: " + step);
    });
    addTestLog("开始执行: " + step);
}

// 测试函数
function testAppControl() {
    updateCurrentStep("测试应用启动/关闭");

    try {
        // 测试关闭应用
        addTestLog("测试关闭小红书应用...");
        const closeResult = accountManager.closeXhsApp();
        addTestLog("关闭应用结果: " + (closeResult ? "成功" : "失败"));

        if (!closeResult) {
            throw new Error("关闭应用失败");
        }

        sleep(3000);

        // 测试启动应用
        addTestLog("测试启动小红书应用...");
        const startResult = accountManager.startXhsApp();
        addTestLog("启动应用结果: " + (startResult ? "成功" : "失败"));

        if (!startResult) {
            throw new Error("启动应用失败");
        }

        testResults.push({
            test: "应用控制",
            result: "通过",
            details: "应用启动和关闭功能正常"
        });

        return true;

    } catch (e) {
        addTestLog("应用控制测试失败: " + e.toString());
        testResults.push({
            test: "应用控制",
            result: "失败",
            details: e.toString()
        });
        return false;
    }
}

function testLogoutFlow() {
    updateCurrentStep("测试退出登录流程");

    try {
        addTestLog("开始测试退出登录流程...");

        // 确保小红书已启动
        if (currentPackage() !== "com.xingin.xhs") {
            addTestLog("启动小红书应用...");
            launch("com.xingin.xhs");
            sleep(3000);
        }

        // 等待应用完全加载
        addTestLog("等待应用加载完成...");
        sleep(3000);

        // 详细测试退出登录流程
        addTestLog("步骤1: 查找设置按钮");
        const settingsBtn = id("com.xingin.xhs:id/i48").findOne(3000);
        if (!settingsBtn) {
            throw new Error("找不到设置按钮");
        }
        addTestLog("✓ 找到设置按钮");

        // 执行退出登录
        addTestLog("步骤2: 执行完整退出登录流程");
        const logoutResult = accountManager.performLogout();
        addTestLog("退出登录结果: " + (logoutResult ? "成功" : "失败"));

        if (logoutResult) {
            testResults.push({
                test: "退出登录",
                result: "通过",
                details: "退出登录流程执行成功"
            });
        } else {
            throw new Error("退出登录流程失败");
        }

        return true;

    } catch (e) {
        addTestLog("退出登录测试失败: " + e.toString());
        testResults.push({
            test: "退出登录",
            result: "失败",
            details: e.toString()
        });
        return false;
    }
}

function testLoginFlow(account) {
    updateCurrentStep("测试登录流程");

    try {
        addTestLog("开始测试登录流程 - 账号: " + account.username);

        // 等待登录页面加载
        sleep(2000);

        // 执行登录
        const loginResult = accountManager.performLogin(account);
        addTestLog("登录结果: " + (loginResult ? "成功" : "失败"));

        if (loginResult) {
            testResults.push({
                test: "登录流程",
                result: "通过",
                details: "账号 " + account.username + " 登录成功"
            });
        } else {
            throw new Error("登录流程失败");
        }

        return true;

    } catch (e) {
        addTestLog("登录测试失败: " + e.toString());
        testResults.push({
            test: "登录流程",
            result: "失败",
            details: e.toString()
        });
        return false;
    }
}

function testFullAccountSwitch(accountList) {
    updateCurrentStep("测试完整账号切换");

    try {
        addTestLog("开始测试完整账号切换流程...");

        if (accountList.length < 2) {
            throw new Error("需要至少2个账号才能测试切换功能");
        }

        // 设置账号列表
        const accountString = accountList.map(acc => acc.username + "," + acc.password).join("\n");
        accountManager.setAccountList(accountString);

        addTestLog("已设置账号列表，共 " + accountList.length + " 个账号");

        // 执行账号切换
        const switchResult = accountManager.switchToNextAccount();
        addTestLog("账号切换结果: " + (switchResult ? "成功" : "失败"));

        if (switchResult) {
            const status = accountManager.getAccountStatus();
            addTestLog("切换后状态 - 当前账号: " + status.currentAccount);

            testResults.push({
                test: "完整账号切换",
                result: "通过",
                details: "账号切换功能正常，当前账号: " + status.currentAccount
            });
        } else {
            throw new Error("账号切换失败");
        }

        return true;

    } catch (e) {
        addTestLog("完整账号切换测试失败: " + e.toString());
        testResults.push({
            test: "完整账号切换",
            result: "失败",
            details: e.toString()
        });
        return false;
    }
}

// 解析账号列表
function parseTestAccountList(accountString) {
    const accounts = [];
    const lines = accountString.split('\n');

    for (let line of lines) {
        line = line.trim();
        if (line && line.includes(',')) {
            const parts = line.split(',');
            if (parts.length >= 2) {
                accounts.push({
                    username: parts[0].trim(),
                    password: parts[1].trim()
                });
            }
        }
    }

    return accounts;
}

// 主测试函数
function runTests() {
    if (isTestRunning) {
        addTestLog("测试已在运行中...");
        return;
    }

    isTestRunning = true;
    testResults = [];
    updateTestStatus("运行中");

    try {
        addTestLog("=== 开始多账号切换功能测试 ===");

        // 获取测试配置
        const accountString = ui.testAccountList.getText().toString().trim();
        const testAppControlEnabled = ui.testAppControl.checked;
        const testLogoutEnabled = ui.testLogout.checked;
        const testLoginEnabled = ui.testLogin.checked;
        const testFullSwitchEnabled = ui.testFullSwitch.checked;

        if (!accountString && (testLoginEnabled || testFullSwitchEnabled)) {
            throw new Error("请输入测试账号信息");
        }

        const accountList = parseTestAccountList(accountString);
        addTestLog("解析到 " + accountList.length + " 个测试账号");

        // 初始化账号管理器
        accountManager.initAccountManager();

        // 执行测试
        let allTestsPassed = true;

        if (testAppControlEnabled) {
            const appControlResult = testAppControl();
            allTestsPassed = allTestsPassed && appControlResult;
            if (!appControlResult && testFullSwitchEnabled) {
                addTestLog("应用控制测试失败，跳过完整切换测试");
                testFullSwitchEnabled = false;
            }
        }

        if (testLogoutEnabled && accountList.length > 0) {
            const logoutResult = testLogoutFlow();
            allTestsPassed = allTestsPassed && logoutResult;
        }

        if (testLoginEnabled && accountList.length > 0) {
            const loginResult = testLoginFlow(accountList[0]);
            allTestsPassed = allTestsPassed && loginResult;
        }

        if (testFullSwitchEnabled && accountList.length >= 2) {
            const switchResult = testFullAccountSwitch(accountList);
            allTestsPassed = allTestsPassed && switchResult;
        }

        // 输出测试结果
        addTestLog("=== 测试完成 ===");
        addTestLog("总体结果: " + (allTestsPassed ? "全部通过" : "存在失败"));

        for (let result of testResults) {
            addTestLog(`${result.test}: ${result.result} - ${result.details}`);
        }

        updateTestStatus(allTestsPassed ? "测试通过" : "测试失败");

    } catch (e) {
        addTestLog("测试执行失败: " + e.toString());
        updateTestStatus("测试异常");
    } finally {
        isTestRunning = false;
        updateCurrentStep("测试完成");
    }
}

// UI事件处理
ui.startTestBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }

    threads.start(runTests);
});

ui.stopTestBtn.on("click", () => {
    if (isTestRunning) {
        isTestRunning = false;
        updateTestStatus("已停止");
        addTestLog("用户手动停止测试");
    }
});

ui.clearLogBtn.on("click", () => {
    ui.testLog.setText("");
    addTestLog("日志已清空");
});

ui.exportLogBtn.on("click", () => {
    try {
        const logContent = ui.testLog.getText().toString();
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
        const fileName = `XHS_Account_Test_Log_${timestamp}.txt`;
        const filePath = files.join(files.getSdcardPath(), fileName);

        files.write(filePath, logContent);
        toast("日志已导出: " + fileName);
        addTestLog("日志已导出到: " + filePath);
    } catch (e) {
        toast("导出失败: " + e.toString());
        addTestLog("导出失败: " + e.toString());
    }
});

// 初始化
addTestLog("多账号切换测试工具已启动");
updateTestStatus("就绪");

// 单步测试函数
function testElementDetection() {
    updateCurrentStep("测试元素检测");

    try {
        addTestLog("开始测试关键元素检测...");

        // 检查小红书是否已启动
        if (currentPackage() !== "com.xingin.xhs") {
            addTestLog("启动小红书应用进行元素检测...");
            launch("com.xingin.xhs");
            sleep(3000);
        }

        // 检测关键元素
        const elements = [
            { name: "设置按钮", id: "com.xingin.xhs:id/i48", critical: true },
            { name: "退出登录按钮", id: "com.xingin.xhs:id/ebt", critical: true },
            { name: "确认退出按钮", id: "com.xingin.xhs:id/h0g", critical: true },
            { name: "其它登录方式", id: "com.xingin.xhs:id/gil", critical: true },
            { name: "手机号登录", id: "com.xingin.xhs:id/axu", critical: true },
            { name: "密码登录", id: "com.xingin.xhs:id/iho", critical: false },
            { name: "协议勾选框", id: "com.xingin.xhs:id/gws", critical: false },
            { name: "登录按钮", id: "com.xingin.xhs:id/f9l", critical: true }
        ];

        let detectedCount = 0;
        let criticalCount = 0;
        let criticalFound = 0;

        for (let element of elements) {
            if (element.critical) criticalCount++;

            const found = id(element.id).exists();
            const priority = element.critical ? "[关键]" : "[普通]";
            const status = found ? "✓" : "✗";

            addTestLog(`${status} ${priority} ${element.name}: ${found ? "找到" : "未找到"}`);

            if (found) {
                detectedCount++;
                if (element.critical) criticalFound++;
            }
        }

        addTestLog(`元素检测完成: ${detectedCount}/${elements.length} 个元素 (关键: ${criticalFound}/${criticalCount})`);

        const testPassed = criticalFound === criticalCount;
        testResults.push({
            test: "元素检测",
            result: testPassed ? "通过" : (criticalFound > 0 ? "部分通过" : "失败"),
            details: `检测到 ${detectedCount}/${elements.length} 个元素，关键元素 ${criticalFound}/${criticalCount}`
        });

        return detectedCount > 0;

    } catch (e) {
        addTestLog("元素检测测试失败: " + e.toString());
        testResults.push({
            test: "元素检测",
            result: "失败",
            details: e.toString()
        });
        return false;
    }
}

function testStepByStep() {
    updateCurrentStep("分步测试模式");

    try {
        addTestLog("=== 开始分步测试模式 ===");

        // 1. 元素检测
        addTestLog("步骤1: 元素检测");
        testElementDetection();

        // 2. 应用控制
        if (ui.testAppControl.checked) {
            addTestLog("步骤2: 应用控制测试");
            testAppControl();
        }

        // 3. 退出登录（如果需要）
        if (ui.testLogout.checked) {
            addTestLog("步骤3: 退出登录测试");
            testLogoutFlow();
        }

        // 4. 登录测试（如果有账号）
        const accountString = ui.testAccountList.getText().toString().trim();
        if (ui.testLogin.checked && accountString) {
            const accountList = parseTestAccountList(accountString);
            if (accountList.length > 0) {
                addTestLog("步骤4: 登录测试");
                testLoginFlow(accountList[0]);
            }
        }

        addTestLog("=== 分步测试完成 ===");

    } catch (e) {
        addTestLog("分步测试失败: " + e.toString());
    }
}

// 添加分步测试按钮到UI
ui.layout(
    <vertical padding="16dp">
        <text textSize="18sp" textColor="#2196F3" text="小红书多账号切换测试工具" gravity="center" marginBottom="16dp" />

        <card cardBackgroundColor="#fff3cd" cardCornerRadius="8dp" cardElevation="2dp" margin="8dp">
            <vertical padding="12dp">
                <text textSize="14sp" textColor="#856404" textStyle="bold" text="⚠️ 测试说明" />
                <text textSize="12sp" textColor="#856404" text="• 请确保小红书app已安装且可正常使用" marginTop="4dp" />
                <text textSize="12sp" textColor="#856404" text="• 测试前请手动关闭小红书app" marginTop="2dp" />
                <text textSize="12sp" textColor="#856404" text="• 请准备至少2个有效的小红书账号" marginTop="2dp" />
                <text textSize="12sp" textColor="#856404" text="• 测试过程中请勿手动操作手机" marginTop="2dp" />
                <text textSize="12sp" textColor="#856404" text="• 流程：设置按钮 → 弹出层底部设置 → 设置页面滑动 → 退出登录" marginTop="2dp" />
            </vertical>
        </card>

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试账号配置:" />
        <text textSize="12sp" text="格式: 用户名,密码 (每行一组)" />
        <input id="testAccountList" hint="例如:&#10;***********,password123&#10;***********,password456" lines="4" />

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试选项:" />
        <checkbox id="testAppControl" text="测试应用启动/关闭" checked="true" />
        <checkbox id="testLogout" text="测试退出登录流程" checked="true" />
        <checkbox id="testLogin" text="测试登录流程" checked="true" />
        <checkbox id="testFullSwitch" text="测试完整账号切换" checked="false" />

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试控制:" />
        <horizontal>
            <button id="startTestBtn" text="完整测试" layout_weight="1" bg="#4CAF50" textColor="white" />
            <button id="stepTestBtn" text="分步测试" layout_weight="1" bg="#2196F3" textColor="white" />
        </horizontal>
        <horizontal marginTop="8dp">
            <button id="elementTestBtn" text="元素检测" layout_weight="1" bg="#FF9800" textColor="white" />
            <button id="stopTestBtn" text="停止测试" layout_weight="1" bg="#f44336" textColor="white" />
        </horizontal>

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试状态:" />
        <text id="testStatus" text="状态: 未开始" textSize="14sp" />
        <text id="currentStep" text="当前步骤: 无" textSize="12sp" textColor="#666666" />

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试日志:" />
        <ScrollView layout_weight="1">
            <text id="testLog" text="" textSize="10sp" />
        </ScrollView>

        <horizontal marginTop="16dp">
            <button id="clearLogBtn" text="清空日志" layout_weight="1" />
            <button id="exportLogBtn" text="导出日志" layout_weight="1" />
        </horizontal>
    </vertical>
);

// 更新UI事件处理
ui.stepTestBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }

    threads.start(testStepByStep);
});

ui.elementTestBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }

    threads.start(testElementDetection);
});

// 检查无障碍服务
if (!auto.service) {
    addTestLog("警告: 无障碍服务未开启，请先开启无障碍服务");
    updateTestStatus("需要无障碍服务");
}
