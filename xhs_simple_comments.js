/**
 * 小红书图文笔记处理模块
 * 负责图文笔记的业务统一处理：评论发布、用户信息采集、点赞、返回搜索结果页
 */

// 引入工具模块
const utils = require('./utils.js');
const configManager = require('./config.js');
const noteCommentingModule = require('./xhs_note_commenting.js');
const xhsActions = require('./xhs_actions.js');

/**
 * 图文笔记业务统一处理函数
 * @param {string} noteTitle - 笔记标题
 * @param {string} noteAuthor - 笔记作者
 * @returns {boolean} - 处理是否成功
 */
function processImageTextNote(noteTitle, noteAuthor) {
    utils.log("SIMPLE_PROCESS: 开始处理图文笔记: " + noteTitle);

    try {
        const config = configManager.loadConfig();
        var processSuccess = false;

        // 1. 评论发布（预设评论或AI评论）
        if (config.task_comment_notes || config.enableLlmComments) {
            utils.log("SIMPLE_PROCESS: 开始评论发布...");
            var commentSuccess = noteCommentingModule.publishCommentInCurrentNote(
                noteTitle,
                noteAuthor || "Unknown Author",
                [] // 空数组，让评论模块根据配置决定使用AI评论还是预设评论
            );

            if (commentSuccess) {
                utils.log("SIMPLE_PROCESS: ✓ 评论发布成功");
                processSuccess = true;
            } else {
                utils.log("SIMPLE_PROCESS: ✗ 评论发布失败或跳过");
            }
        }

        // 2. 用户信息采集（如果启用）
        if (config.task_collect_users) {
            utils.log("SIMPLE_PROCESS: 开始用户信息采集...");
            try {
                var collectedCount = collectAndSaveComments(
                    noteTitle,
                    config.commentKeywords || "",
                    config.targetRegion || 0,
                    config.task_like_users || false
                );
                utils.log("SIMPLE_PROCESS: ✓ 用户信息采集完成，采集到 " + collectedCount + " 条评论");
                processSuccess = true;
            } catch (collectionError) {
                utils.log("SIMPLE_PROCESS: ✗ 用户信息采集失败: " + collectionError);
            }
        }

        // 3. 点赞功能（如果启用且未在采集中处理）
        if (config.task_like_users && !config.task_collect_users) {
            utils.log("SIMPLE_PROCESS: 开始独立点赞操作...");
            // 这里可以添加独立的点赞逻辑
            // 目前点赞功能集成在用户信息采集中
        }

        // 4. 返回搜索结果页
        utils.log("SIMPLE_PROCESS: 返回搜索结果页...");
        const noteTypes = require('./xhs_note_types.js');
        const currentNoteType = noteTypes.NOTE_TYPES.IMAGE_TEXT;

        try {
            var backSuccess = xhsActions.backToPreviousPage(currentNoteType);
            if (!backSuccess) {
                utils.log("SIMPLE_PROCESS: 标准返回失败，使用备用方法");
                back();
                sleep(1000);
            }
        } catch (backError) {
            utils.log("SIMPLE_PROCESS: backToPreviousPage调用失败: " + backError + "，使用备用方法");
            back();
            sleep(1000);
        }

        utils.log("SIMPLE_PROCESS: 图文笔记处理完成，成功: " + processSuccess);
        return processSuccess;

    } catch (error) {
        utils.log("SIMPLE_PROCESS: 图文笔记处理异常: " + error);

        // 异常情况下也要尝试返回
        try {
            back();
            sleep(1000);
        } catch (backError) {
            utils.log("SIMPLE_PROCESS: 异常返回也失败: " + backError);
        }

        return false;
    }
}

// 创建持久化存储用于点赞去重
const likedCommentsStorage = storages.create("xhs_liked_comments");

/**
 * 生成评论的唯一标识
 * @param {string} nickname - 用户昵称
 * @param {string} commentText - 评论内容
 * @returns {string} - 评论唯一标识
 */
function generateCommentId(nickname, commentText) {
    const rawId = (nickname || "Unknown") + "::" + (commentText || "Empty");
    let hash = 0;
    for (let i = 0; i < rawId.length; i++) {
        const char = rawId.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    return "comment_" + Math.abs(hash) + "_" + rawId.length;
}

/**
 * 检查评论是否已经点赞过
 * @param {string} nickname - 用户昵称
 * @param {string} commentText - 评论内容
 * @returns {boolean} - 是否已点赞过
 */
function isCommentLiked(nickname, commentText) {
    const commentId = generateCommentId(nickname, commentText);
    const result = likedCommentsStorage.get(commentId, false);
    utils.log(`LIKE_DEDUP: 检查评论是否已点赞: "${commentId}" -> ${result}`);
    return result;
}

/**
 * 标记评论为已点赞
 * @param {string} nickname - 用户昵称
 * @param {string} commentText - 评论内容
 */
function markCommentAsLiked(nickname, commentText) {
    const commentId = generateCommentId(nickname, commentText);
    const timestamp = new Date().toISOString();
    likedCommentsStorage.put(commentId, {
        liked: true,
        timestamp: timestamp,
        nickname: nickname,
        commentText: commentText.substring(0, 100) // 只保存前100个字符
    });
    utils.log(`LIKE_DEDUP: 标记评论为已点赞: "${commentId}" at ${timestamp}`);
}

/**
 * 获取已点赞评论的数量
 * @returns {number} - 已点赞评论数量
 */
function getLikedCommentsCount() {
    return likedCommentsStorage.keys().length;
}

/**
 * 对评论进行点赞操作
 * @param {UiObject} commentElement - 评论内容元素
 * @param {UiObject} nicknameElement - 昵称元素
 * @param {string} nickname - 用户昵称
 * @param {string} commentText - 评论内容
 * @returns {boolean} - 点赞是否成功
 */
function likeComment(commentElement, nicknameElement, nickname, commentText) {
    try {
        utils.log(`SIMPLE_LIKE: 开始为用户 "${nickname}" 的评论点赞...`);

        // 检查是否已经点赞过这条评论
        if (isCommentLiked(nickname, commentText)) {
            utils.log(`SIMPLE_LIKE: ✓ 评论已点赞过，跳过: ${nickname} - "${commentText.substring(0, 30)}..."`);
            return true; // 返回true表示已处理（虽然是跳过）
        }

        // 方法1: 通过评论容器查找点赞按钮
        // 评论容器通常是包含评论内容和昵称的父容器
        let commentContainer = null;

        // 尝试从评论内容元素向上查找容器
        let currentElement = commentElement;
        for (let i = 0; i < 5; i++) { // 最多向上查找5层
            if (currentElement && currentElement.parent()) {
                currentElement = currentElement.parent();
                // 查找容器内是否有点赞按钮
                let likeButton = currentElement.findOne(id("com.xingin.xhs:id/euy"));
                if (likeButton) {
                    commentContainer = currentElement;
                    utils.log(`SIMPLE_LIKE: 在第${i + 1}层父容器中找到点赞按钮`);
                    break;
                }
            } else {
                break;
            }
        }

        if (!commentContainer) {
            // 方法2: 通过昵称元素向上查找容器
            currentElement = nicknameElement;
            for (let i = 0; i < 5; i++) { // 最多向上查找5层
                if (currentElement && currentElement.parent()) {
                    currentElement = currentElement.parent();
                    // 查找容器内是否有点赞按钮
                    let likeButton = currentElement.findOne(id("com.xingin.xhs:id/euy"));
                    if (likeButton) {
                        commentContainer = currentElement;
                        utils.log(`SIMPLE_LIKE: 通过昵称元素在第${i + 1}层父容器中找到点赞按钮`);
                        break;
                    }
                } else {
                    break;
                }
            }
        }

        if (!commentContainer) {
            utils.log(`SIMPLE_LIKE: ✗ 无法找到包含点赞按钮的评论容器: ${nickname}`);
            return false;
        }

        // 在容器内查找点赞按钮
        let likeButton = commentContainer.findOne(id("com.xingin.xhs:id/euy"));
        if (!likeButton) {
            utils.log(`SIMPLE_LIKE: ✗ 在评论容器内未找到点赞按钮: ${nickname}`);
            return false;
        }

        // 检查点赞按钮是否可点击
        if (!likeButton.clickable()) {
            utils.log(`SIMPLE_LIKE: ✗ 点赞按钮不可点击: ${nickname}`);
            return false;
        }

        // 检查是否已经点赞过（可选：通过点赞按钮的状态判断）
        // 注意：这里可能需要根据实际的UI状态来判断是否已点赞

        // 执行点赞操作
        utils.log(`SIMPLE_LIKE: 执行点赞操作: ${nickname} - "${commentText.substring(0, 30)}..."`);

        if (likeButton.click()) {
            utils.log(`SIMPLE_LIKE: ✓ 点赞成功: ${nickname}`);

            // 标记评论为已点赞
            markCommentAsLiked(nickname, commentText);

            sleep(500); // 短暂等待，避免操作过快
            return true;
        } else {
            utils.log(`SIMPLE_LIKE: ✗ 点赞按钮点击失败: ${nickname}`);
            return false;
        }

    } catch (error) {
        utils.log(`SIMPLE_LIKE: ✗ 点赞操作异常: ${nickname} - ${error}`);
        return false;
    }
}

/**
 * 采集当前笔记的评论并保存到文件
 * @param {string} noteTitle - 笔记标题
 * @param {string} keywordsString - 关键词，用逗号分隔
 * @param {number} targetRegionIndex - 目标区域索引（0表示不限）
 * @param {boolean} enableLiking - 是否启用点赞功能
 * @returns {Promise<number>} - 返回采集到的评论数量
 */
function collectAndSaveComments(noteTitle, keywordsString, targetRegionIndex, enableLiking) {
    // 设置默认值
    if (typeof targetRegionIndex === 'undefined') {
        targetRegionIndex = 0;
    }

    utils.log("SIMPLE: 开始采集评论，笔记标题: " + noteTitle);
    utils.log("SIMPLE: 目标区域: " + utils.getRegionNameByIndex(targetRegionIndex));

    return new Promise((resolve) => {
        // 存储所有采集到的评论
        let allComments = [];

        // 参考视频笔记的做法：不在采集过程中立即获取用户信息，而是在最后统一获取

        // 滚动到评论区
        utils.log("SIMPLE: 滚动到评论区...");
        for (let i = 0; i < 3; i++) {
            swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.2, 500);
            sleep(800);
        }

        // 移除滚动次数限制 - 采集所有评论直到到达底部
        let currentScroll = 0;
        let bottomDetectedCount = 0; // 检测到底部的次数
        let likedCount = 0; // 记录点赞成功的数量

        // 递归函数，用于采集评论
        function collectComments() {
            // 在函数开始处，打印allComments数组的长度和内容
            utils.log(`SIMPLE: 递归调用开始，allComments数组长度: ${allComments.length}`);

            // 打印前5条评论的详细信息
            if (allComments.length > 0) {
                utils.log("SIMPLE: 递归调用开始，allComments数组中的前5条评论:");
                for (let i = 0; i < Math.min(allComments.length, 5); i++) {
                    let commentStart = allComments[i];
                    if (commentStart) {
                        utils.log(`SIMPLE: allComments[${i}] = { nickname: "${commentStart.nickname}", content: "${commentStart.content.substring(0, 30)}${commentStart.content.length > 30 ? '...' : ''}" }`);
                    } else {
                        utils.log(`SIMPLE: allComments[${i}] = ${commentStart}`);
                    }
                }
            }

            // 移除滚动次数限制 - 只在到达底部时停止

            // 先提取当前屏幕上的评论，再检查是否到达底部
            utils.log("SIMPLE: 提取当前屏幕上的评论...");

            let newCommentsCount = 0; // 记录本次新增的评论数量

            try {
                // 直接查找所有评论内容元素和昵称元素
                let commentElements = id("com.xingin.xhs:id/jfh").find();
                let nicknameElements = id("com.xingin.xhs:id/jmt").find();

                utils.log(`SIMPLE: 找到 ${commentElements.length} 个评论内容元素和 ${nicknameElements.length} 个昵称元素`);

                // 提取评论
                let newComments = [];
                let beforeCount = allComments.length; // 记录添加前的评论数量

                // 确保两个数组长度相同
                const minLength = Math.min(commentElements.length, nicknameElements.length);

                // 遍历并提取评论
                for (let i = 0; i < minLength; i++) {
                    try {
                        // 获取当前索引的评论元素和昵称元素
                        let commentElement = commentElements[i];
                        let nicknameElement = nicknameElements[i];

                        // 检查元素是否存在
                        if (!commentElement || !nicknameElement) {
                            utils.log(`SIMPLE: 跳过无效评论元素 #${i + 1}`);
                            continue;
                        }

                        // 获取文本内容 - 使用临时变量存储，避免引用问题
                        let commentTextRaw = commentElement.text();
                        let nicknameRaw = nicknameElement.text();

                        // 创建副本，确保不使用引用
                        let commentText = String(commentTextRaw || "");
                        let nickname = String(nicknameRaw || "");

                        // 添加调试信息
                        utils.log(`SIMPLE: 评论元素 #${i + 1}: commentText="${commentText}", nickname="${nickname}"`);

                        // 如果找到了昵称和评论内容，则创建评论对象
                        if (commentText && nickname) {
                            // 检查是否匹配关键词
                            let matchesKeyword = false;
                            if (keywordsString) {
                                const keywords = keywordsString.split(',').map(k => k.trim());
                                for (let j = 0; j < keywords.length; j++) {
                                    if (commentText.includes(keywords[j])) {
                                        matchesKeyword = true;
                                        utils.log(`SIMPLE: 评论匹配关键字 "${keywords[j]}": ${nickname} - "${commentText.substring(0, 30)}..."`);
                                        break;
                                    }
                                }
                            }

                            // 如果匹配关键词，还需要检查区域筛选
                            if (matchesKeyword && targetRegionIndex > 0) {
                                // 提取用户属地信息 - 创建独立的变量避免引用问题
                                let extractedLocation = utils.extractUserLocation(commentText);
                                let currentUserLocation = String(extractedLocation || ""); // 强制转换为字符串
                                let currentRegionMatches = utils.matchUserRegion(currentUserLocation, targetRegionIndex);

                                if (!currentRegionMatches) {
                                    utils.log("SIMPLE: 评论匹配关键字但区域不匹配，跳过: " + nickname + " - 属地: \"" + currentUserLocation + "\"");
                                    matchesKeyword = false; // 区域不匹配，设置为不匹配
                                } else {
                                    utils.log("SIMPLE: 评论匹配关键字且区域匹配: " + nickname + " - 属地: \"" + currentUserLocation + "\"");
                                }
                            }

                            // 如果启用点赞功能且评论匹配关键词，执行点赞操作
                            if (enableLiking && matchesKeyword) {
                                utils.log(`SIMPLE: 准备为匹配关键词的评论点赞: ${nickname}`);
                                try {
                                    let likeSuccess = likeComment(commentElement, nicknameElement, nickname, commentText);
                                    if (likeSuccess) {
                                        likedCount++; // 增加点赞成功计数
                                        utils.log(`SIMPLE: ✓ 点赞成功: ${nickname} - "${commentText.substring(0, 30)}..." (总计已点赞: ${likedCount})`);
                                    } else {
                                        utils.log(`SIMPLE: ✗ 点赞失败: ${nickname} - "${commentText.substring(0, 30)}..."`);
                                    }
                                } catch (likeError) {
                                    utils.log(`SIMPLE: ✗ 点赞操作异常: ${nickname} - ${likeError}`);
                                }
                            }

                            try {
                                // 创建评论对象 - 使用字符串值而不是引用
                                let newNickname = String(nickname || "");
                                let newContent = String(commentText || "");
                                let newMatchesKeyword = Boolean(matchesKeyword);

                                // 创建一个全新的评论对象
                                let newCommentObj = {
                                    nickname: newNickname,
                                    content: newContent,
                                    matchesKeyword: newMatchesKeyword,
                                    xhsId: "", // 初始化小红书号字段
                                    userInfoCollected: false // 标记是否已采集用户信息
                                };

                                // 如果评论匹配关键字，标记需要获取用户信息（参考视频笔记的做法）
                                if (newMatchesKeyword) {
                                    utils.log("SIMPLE: 评论匹配关键字，标记需要获取用户信息: " + newNickname);
                                    newCommentObj.needsUserInfo = true; // 标记需要获取用户信息
                                    newCommentObj.nicknameElement = nicknameElement; // 保存元素引用
                                }

                                // 添加到新评论数组
                                newComments.push(newCommentObj);

                                // 添加详细日志，记录评论对象
                                utils.log(`SIMPLE: DEBUG - 创建评论对象: ${JSON.stringify(newCommentObj)}, 内存地址: ${newCommentObj}`);
                            } catch (createError) {
                                utils.log(`SIMPLE: 创建评论对象时出错: ${createError}`);
                            }
                            utils.log(`SIMPLE: 提取到评论 #${newComments.length}: ${nickname} - "${commentText.substring(0, 30)}${commentText.length > 30 ? '...' : ''}"`);
                        } else {
                            utils.log(`SIMPLE: 跳过空评论 #${i + 1}`);
                        }
                    } catch (e) {
                        utils.log(`SIMPLE: 提取评论 #${i + 1} 时出错: ${e}`);
                    }
                }

                utils.log(`SIMPLE: 本次提取到 ${newComments.length} 条评论`);

                // 将新评论添加到总评论列表
                // 使用深拷贝确保每个评论对象都是独立的
                for (let i = 0; i < newComments.length; i++) {
                    try {
                        // 添加详细日志，记录newComments数组中的评论对象
                        utils.log(`SIMPLE: DEBUG - newComments[${i}] = ${JSON.stringify(newComments[i])}, 内存地址: ${newComments[i]}`);

                        // 获取当前评论对象
                        let currentComment = newComments[i];

                        // 检查评论对象是否有效
                        if (!currentComment || !currentComment.nickname || !currentComment.content) {
                            utils.log(`SIMPLE: 跳过无效评论对象 #${i + 1}`);
                            continue;
                        }

                        // 创建新的字符串变量，确保不使用引用
                        let currentNickname = String(currentComment.nickname || "");
                        let currentContent = String(currentComment.content || "");
                        let currentMatchesKeyword = Boolean(currentComment.matchesKeyword);

                        // 添加调试日志，记录当前评论对象的值
                        utils.log(`SIMPLE: DEBUG - 当前评论值: nickname="${currentNickname}", content="${currentContent.substring(0, 30)}${currentContent.length > 30 ? '...' : ''}", matchesKeyword=${currentMatchesKeyword}`);

                        // 创建一个全新的评论对象，保留所有必要字段
                        let commentToAdd = {
                            nickname: currentNickname,
                            content: currentContent,
                            matchesKeyword: currentMatchesKeyword,
                            xhsId: currentComment.xhsId || "",
                            userInfoCollected: currentComment.userInfoCollected || false,
                            needsUserInfo: currentComment.needsUserInfo || false, // 保留用户信息需求标记
                            nicknameElement: currentComment.nicknameElement // 保留元素引用
                        };

                        // 添加到总评论列表
                        allComments.push(commentToAdd);

                        // 添加详细日志，记录allComments数组中的评论对象
                        let lastIndex = allComments.length - 1;
                        utils.log(`SIMPLE: DEBUG - allComments[${lastIndex}] = ${JSON.stringify(allComments[lastIndex])}, 内存地址: ${allComments[lastIndex]}`);
                    } catch (err) {
                        utils.log(`SIMPLE: 处理评论对象时出错: ${err}`);
                    }
                }

                // 计算本次新增的评论数量
                newCommentsCount = allComments.length - beforeCount;
                utils.log(`SIMPLE: 本次新增 ${newCommentsCount} 条评论，当前总计 ${allComments.length} 条评论`);

                // 检查是否到达底部
                let bottomIndicator = text("- 到底了 -").findOne(500);
                let isAtBottom = bottomIndicator && bottomIndicator.visibleToUser();

                // 如果检测到底部提示，进行最后一次评论采集确认
                if (isAtBottom) {
                    bottomDetectedCount++; // 增加底部检测计数
                    utils.log(`SIMPLE: 检测到底部提示 '- 到底了 -'（第${bottomDetectedCount}次），进行最后一次评论采集确认...`);

                    // 如果已经检测到底部3次，强制结束
                    if (bottomDetectedCount >= 3) {
                        utils.log(`SIMPLE: 已连续${bottomDetectedCount}次检测到底部，强制结束采集`);
                        finishCollection(`连续${bottomDetectedCount}次检测到底部，强制结束`);
                        return;
                    }

                    // 如果本次有新评论且底部检测次数少于3次，继续滚动一次
                    if (newCommentsCount > 0) {
                        utils.log(`SIMPLE: 底部检测时发现 ${newCommentsCount} 条新评论，继续滚动一次确保完全采集...`);
                        // 继续滚动，不立即停止
                    } else {
                        // 没有新评论且到达底部，确认结束
                        utils.log("SIMPLE: 底部检测时没有新评论，确认已到达评论区底部。");
                        finishCollection("已到达评论区底部");
                        return;
                    }
                } else {
                    // 重置底部检测计数（如果没有检测到底部）
                    bottomDetectedCount = 0;
                    // 未到达底部的情况
                    if (newCommentsCount === 0 && currentScroll >= 3) {
                        // 如果已经滚动了至少3次且没有新评论，结束采集
                        utils.log("SIMPLE: 连续滚动未发现新评论，可能已到达评论末尾。");
                        finishCollection("连续滚动未发现新评论");
                        return;
                    } else if (newCommentsCount > 0) {
                        utils.log(`SIMPLE: 本次采集到 ${newCommentsCount} 条新评论，继续滚动寻找更多评论...`);
                    }
                }

                // 滚动加载更多评论
                currentScroll++;
                utils.log(`SIMPLE: 滚动加载更多评论 (第 ${currentScroll} 次)...`);

                // 执行滚动
                swipe(
                    device.width / 2,
                    device.height * 0.7,
                    device.width / 2,
                    device.height * 0.3,
                    500
                );

                // 等待加载
                sleep(2500);

                // 在递归调用前，打印allComments数组的长度和内容
                utils.log(`SIMPLE: 递归调用前，allComments数组长度: ${allComments.length}`);

                // 打印前5条评论的详细信息
                if (allComments.length > 0) {
                    utils.log("SIMPLE: 递归调用前，allComments数组中的前5条评论:");
                    for (let i = 0; i < Math.min(allComments.length, 5); i++) {
                        let commentBefore = allComments[i];
                        if (commentBefore) {
                            utils.log(`SIMPLE: allComments[${i}] = { nickname: "${commentBefore.nickname}", content: "${commentBefore.content.substring(0, 30)}${commentBefore.content.length > 30 ? '...' : ''}" }`);
                        } else {
                            utils.log(`SIMPLE: allComments[${i}] = ${commentBefore}`);
                        }
                    }
                }

                // 继续采集
                collectComments();
            } catch (e) {
                utils.log(`SIMPLE: 采集评论时出错: ${e}`);
                finishCollection("发生错误: " + e);
            }
        }

        // 完成采集并保存结果
        function finishCollection(reason) {
            utils.log(`SIMPLE: 完成评论采集，原因: ${reason}`);

            // 统计匹配关键字的评论数量
            let matchedCount = 0;
            for (let i = 0; i < allComments.length; i++) {
                if (allComments[i].matchesKeyword) {
                    matchedCount++;
                }
            }

            utils.log("SIMPLE: 共采集到 " + allComments.length + " 条评论，其中 " + matchedCount + " 条匹配关键字");
            if (enableLiking) {
                utils.log("SIMPLE: 点赞功能已启用，共成功点赞 " + likedCount + " 条评论");
            }

            // 参考视频笔记的做法：在保存前，统一获取所有匹配关键字评论的用户信息
            utils.log("SIMPLE: 开始获取 " + matchedCount + " 条匹配评论的用户信息...");

            const userProfile = require('./xhs_user_profile.js');
            let userInfoPromises = [];
            let processedUsers = new Set(); // 用于跟踪已处理的用户
            let userInfoMap = new Map(); // 存储用户昵称 -> 小红书号的映射

            // 第一步：去重并获取唯一用户的信息
            for (let i = 0; i < allComments.length; i++) {
                if (allComments[i].needsUserInfo && allComments[i].nicknameElement) {
                    const comment = allComments[i];
                    const nickname = comment.nickname;

                    // 检查是否已经处理过这个用户
                    if (!processedUsers.has(nickname)) {
                        processedUsers.add(nickname);
                        utils.log(`SIMPLE: 首次处理用户 ${nickname}，开始获取用户信息`);

                        const promise = userProfile.getUserInfoFromNickname(comment.nicknameElement, comment.content)
                            .then(function (userInfo) {
                                if (userInfo.success) {
                                    const xhsId = userInfo.xhsId || "";
                                    userInfoMap.set(nickname, xhsId);
                                    utils.log(`SIMPLE: ✓ 成功获取用户信息: ${userInfo.nickname} -> ${xhsId}`);
                                } else {
                                    utils.log(`SIMPLE: ✗ 获取用户信息失败: ${userInfo.error || '未知错误'}`);
                                }
                                return userInfo;
                            })
                            .catch(function (error) {
                                utils.log(`SIMPLE: ✗ 获取用户信息异常: ${error}`);
                                return { success: false, error: error };
                            });
                        userInfoPromises.push(promise);
                    } else {
                        utils.log(`SIMPLE: 用户 ${nickname} 已处理过，跳过重复获取`);
                    }
                }
            }

            // 第二步：等待所有用户信息获取完成后，将信息分配给所有相关评论

            // 等待所有用户信息获取完成后再保存文件
            if (userInfoPromises.length > 0) {
                utils.log(`SIMPLE: 等待 ${userInfoPromises.length} 个用户信息获取完成（最多等待30秒）...`);

                // 设置总体超时（30秒）避免ScriptInterruptedException
                const allPromises = Promise.all(userInfoPromises);
                const overallTimeout = new Promise(function (resolve) {
                    setTimeout(function () {
                        utils.log(`SIMPLE: 用户信息获取超时，继续保存文件...`);
                        resolve();
                    }, 30000);
                });

                Promise.race([allPromises, overallTimeout])
                    .then(function () {
                        utils.log(`SIMPLE: 用户信息获取完成或超时，开始分配用户信息到所有相关评论...`);

                        // 将获取到的用户信息分配给所有相关评论
                        for (let i = 0; i < allComments.length; i++) {
                            if (allComments[i].needsUserInfo) {
                                const nickname = allComments[i].nickname;
                                const xhsId = userInfoMap.get(nickname);
                                if (xhsId) {
                                    allComments[i].xhsId = xhsId;
                                    allComments[i].userInfoCollected = true;
                                    utils.log(`SIMPLE: ✓ 分配用户信息: ${nickname} -> ${xhsId}`);
                                }
                            }
                        }

                        utils.log(`SIMPLE: 用户信息分配完成，开始保存文件...`);
                        saveAndFinish();
                    })
                    .catch(function (error) {
                        utils.log(`SIMPLE: 用户信息获取过程中出现错误: ${error}，继续保存文件...`);
                        saveAndFinish();
                    });
            } else {
                utils.log(`SIMPLE: 无需获取用户信息，直接保存文件...`);
                saveAndFinish();
            }

            function saveAndFinish() {
                try {
                    saveCommentsToFile(allComments, noteTitle, keywordsString);
                    utils.log(`SIMPLE: 已保存评论信息`);
                } catch (e) {
                    utils.log(`SIMPLE: 保存评论信息时出错: ${e}`);
                }

                // 保存用户数据到数据管理器
                try {
                    const dataManager = require('./data_manager.js');
                    const usersToSave = [];

                    for (let i = 0; i < allComments.length; i++) {
                        const comment = allComments[i];
                        if (comment.matchesKeyword) {
                            const userObj = {
                                uid: comment.nickname + "_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9), // 生成唯一ID
                                nickname: comment.nickname,
                                xhsId: comment.xhsId || '未获取',
                                commentText: comment.content,
                                noteId: 'note_' + Date.now(), // 生成笔记ID
                                noteTitle: noteTitle || '未知',
                                timestamp: Date.now(),
                                status: 'collected'
                            };
                            usersToSave.push(userObj);
                        }
                    }

                    if (usersToSave.length > 0) {
                        const saveResult = dataManager.saveScrapedCommentUsers(usersToSave);
                        if (saveResult) {
                            utils.log(`SIMPLE: ✓ 已保存 ${usersToSave.length} 个用户到数据管理器`);
                        } else {
                            utils.log(`SIMPLE: ✗ 保存用户到数据管理器失败`);
                        }
                    }
                } catch (e) {
                    utils.log(`SIMPLE: 保存用户数据时出错: ${e}`);
                }

                resolve(allComments.length);
            }
        }

        // 开始采集评论
        collectComments();
    });
}

/**
 * 将评论保存到文件，在保存前进行去重处理
 * @param {Array} comments - 评论数组
 * @param {string} noteTitle - 笔记标题
 * @param {string} keywordsString - 关键词字符串
 */
function saveCommentsToFile(comments, noteTitle, keywordsString) {
    // 在保存前进行去重处理
    utils.log("SIMPLE: 开始对评论进行去重处理...");
    utils.log(`SIMPLE: 去重前评论数量: ${comments.length} 条`);

    // 调试：检查传入的评论中有多少条匹配关键字
    const matchedInInput = comments.filter(c => c && c.matchesKeyword);
    utils.log(`SIMPLE: 传入参数中匹配关键字的评论数量: ${matchedInInput.length} 条`);

    // 调试：打印前5条匹配关键字的评论
    for (let i = 0; i < Math.min(matchedInInput.length, 5); i++) {
        const comment = matchedInInput[i];
        utils.log(`SIMPLE: 匹配评论 #${i + 1}: ${comment.nickname} - "${comment.content.substring(0, 30)}..." - matchesKeyword: ${comment.matchesKeyword}`);
    }

    // 创建评论数组的深拷贝，确保原始评论对象不会被修改
    const commentsCopy = comments.map(comment => {
        if (!comment) return null;
        // 使用字符串值而不是引用，确保每个评论对象都是独立的
        const copiedComment = {
            nickname: String(comment.nickname || ""),
            content: String(comment.content || ""),
            matchesKeyword: Boolean(comment.matchesKeyword),
            xhsId: String(comment.xhsId || ""),
            userInfoCollected: Boolean(comment.userInfoCollected || false)
        };

        // 调试：检查原始评论和拷贝后的评论的matchesKeyword字段
        if (comment.matchesKeyword) {
            utils.log(`SIMPLE: 拷贝匹配关键字的评论: ${comment.nickname} - matchesKeyword: ${comment.matchesKeyword} -> ${copiedComment.matchesKeyword}`);
        }

        return copiedComment;
    });
    utils.log(`SIMPLE: 创建了评论数组的深拷贝，长度: ${commentsCopy.length} 条`);

    // 统计拷贝后有多少条匹配关键字的评论
    const matchedInCopy = commentsCopy.filter(c => c && c.matchesKeyword);
    utils.log(`SIMPLE: 深拷贝后匹配关键字的评论数量: ${matchedInCopy.length} 条`);

    // 添加详细日志，打印comments数组中的所有评论对象
    utils.log("SIMPLE: 去重前，打印comments数组中的评论对象...");

    // 打印前20条评论的详细信息
    for (let i = 0; i < Math.min(commentsCopy.length, 20); i++) {
        let commentCopy = commentsCopy[i];
        if (commentCopy) {
            utils.log(`SIMPLE: comments[${i}] = { nickname: "${commentCopy.nickname}", content: "${commentCopy.content.substring(0, 30)}${commentCopy.content.length > 30 ? '...' : ''}" }`);
        } else {
            utils.log(`SIMPLE: comments[${i}] = ${commentCopy}`);
        }
    }

    // 添加调试信息，检查评论对象的结构
    if (commentsCopy.length > 0) {
        let sampleComment = commentsCopy[0];
        utils.log(`SIMPLE: 评论对象示例: ${JSON.stringify(sampleComment)}`);
    }

    // 使用Map进行去重，键为评论内容和昵称的组合
    let uniqueCommentMap = new Map();
    let uniqueCommentsArray = [];

    for (let idx = 0; idx < commentsCopy.length; idx++) {
        let commentItem = commentsCopy[idx];
        if (!commentItem || !commentItem.content) {
            utils.log(`SIMPLE: 跳过无效评论 #${idx + 1}`);
            continue;
        }

        // 添加更详细的日志，帮助调试
        utils.log(`SIMPLE: 评论 #${idx + 1} 详情: nickname=${commentItem.nickname}, content=${commentItem.content.substring(0, 30)}`);

        // 创建唯一标识，使用昵称+评论内容的组合
        // 这样可以确保同一用户的不同评论被保留，不同用户的相同评论也被保留
        let uniqueKey = commentItem.nickname + ":" + commentItem.content;

        // 记录唯一标识
        utils.log(`SIMPLE: 评论 #${idx + 1} 唯一标识: ${uniqueKey.substring(0, 50)}${uniqueKey.length > 50 ? '...' : ''}`);

        // 检查是否已经存在相同的唯一标识
        let isDuplicate = uniqueCommentMap.has(uniqueKey);
        utils.log(`SIMPLE: 评论 #${idx + 1} 是否重复: ${isDuplicate}`);

        // 如果这个标识还没有被记录，则添加到结果数组中
        if (!isDuplicate) {
            uniqueCommentMap.set(uniqueKey, commentItem);
            uniqueCommentsArray.push(commentItem);
            utils.log(`SIMPLE: 添加唯一评论 #${uniqueCommentsArray.length}: ${commentItem.nickname} - "${commentItem.content.substring(0, 30)}${commentItem.content.length > 30 ? '...' : ''}"`);
        } else {
            // 记录与之重复的评论
            let duplicateComment = uniqueCommentMap.get(uniqueKey);
            utils.log(`SIMPLE: 跳过重复评论: ${commentItem.nickname} - "${commentItem.content.substring(0, 30)}${commentItem.content.length > 30 ? '...' : ''}"`);
            utils.log(`SIMPLE: 与之重复的评论: ${duplicateComment.nickname} - "${duplicateComment.content.substring(0, 30)}${duplicateComment.content.length > 30 ? '...' : ''}"`);
        }
    }

    utils.log(`SIMPLE: 去重后评论数量: ${uniqueCommentsArray.length} 条`);

    // 不再使用备用逻辑，直接使用去重后的结果
    utils.log(`SIMPLE: 最终使用的评论数量: ${uniqueCommentsArray.length} 条`);

    // 使用去重后的评论数组继续处理
    // 创建目录 - 使用多种方法尝试创建目录
    utils.log("SIMPLE: 准备创建保存目录...");

    // 尝试多个可能的目录路径
    let logDir;
    let dirCreated = false;

    // 尝试在SD卡根目录创建
    try {
        logDir = files.join(files.getSdcardPath(), "XHS_Comments_Simple");
        utils.log(`SIMPLE: 尝试创建目录: ${logDir}`);

        if (!files.exists(logDir)) {
            // 尝试使用不同的方法创建目录
            try {
                // 方法1: 使用files.create创建目录
                files.create(logDir + "/.nomedia");
                utils.log(`SIMPLE: 使用files.create创建目录成功: ${logDir}`);
                dirCreated = true;
            } catch (e1) {
                utils.log(`SIMPLE: 使用files.create创建目录失败: ${e1}`);

                // 方法2: 使用shell命令创建目录
                try {
                    shell("mkdir -p " + logDir, true);
                    if (files.exists(logDir)) {
                        utils.log(`SIMPLE: 使用shell命令创建目录成功: ${logDir}`);
                        dirCreated = true;
                    } else {
                        utils.log(`SIMPLE: 使用shell命令创建目录失败`);
                    }
                } catch (e2) {
                    utils.log(`SIMPLE: 使用shell命令创建目录失败: ${e2}`);
                }
            }
        } else {
            utils.log(`SIMPLE: 目录已存在: ${logDir}`);
            dirCreated = true;
        }
    } catch (dirError) {
        utils.log(`SIMPLE: 在SD卡根目录创建文件夹失败: ${dirError}`);
    }

    // 如果创建目录失败，尝试使用当前工作目录
    if (!dirCreated) {
        try {
            logDir = files.cwd();
            utils.log(`SIMPLE: 使用当前工作目录: ${logDir}`);
            dirCreated = true;
        } catch (cwdError) {
            utils.log(`SIMPLE: 获取当前工作目录失败: ${cwdError}`);
            throw new Error("无法创建或访问任何目录，无法保存评论");
        }
    }

    // 创建文件名
    let timestamp = new Date().getTime();
    let safeTitle = noteTitle.replace(/[\\/:*?"<>|]/g, "_").substring(0, 20);
    let filePath = files.join(logDir, `XHS_Comments_${safeTitle}_${timestamp}.txt`); // 使用let而不是const，因为可能会更新

    utils.log(`SIMPLE: 准备创建文件: ${filePath}`);

    try {
        // 创建文件内容 - 使用去重后的评论数组
        let content = `===== 笔记标题: "${noteTitle}" | 时间: ${new Date().toLocaleString()} | 共 ${uniqueCommentsArray.length} 条评论 =====\n\n`;
        content += `关键字: "${keywordsString}"\n\n`;

        // 匹配关键字的评论 - 使用去重后的评论数组
        let matchedComments = uniqueCommentsArray.filter(c => c.matchesKeyword);
        content += `【匹配关键字的评论】(${matchedComments.length} 条)\n\n`;

        if (matchedComments.length > 0) {
            for (let idx1 = 0; idx1 < matchedComments.length; idx1++) {
                content += `${idx1 + 1}. 用户: ${matchedComments[idx1].nickname}\n`;
                content += `   评论: ${matchedComments[idx1].content}\n`;
                if (matchedComments[idx1].xhsId) {
                    content += `   小红书号: ${matchedComments[idx1].xhsId}\n`;
                }
                content += `   ----------------------------\n`;
            }
        } else {
            content += "没有找到匹配关键字的评论\n\n";
        }

        // 其他评论 - 使用去重后的评论数组
        let otherComments = uniqueCommentsArray.filter(c => !c.matchesKeyword);
        content += `\n【其他评论】(${otherComments.length} 条)\n\n`;

        if (otherComments.length > 0) {
            for (let idx2 = 0; idx2 < otherComments.length; idx2++) {
                content += `${idx2 + 1}. 用户: ${otherComments[idx2].nickname}\n`;
                content += `   评论: ${otherComments[idx2].content}\n`;
                content += `   ----------------------------\n`;
            }
        } else {
            content += "没有找到其他评论\n\n";
        }

        // 写入文件 - 使用多种方法尝试写入
        try {
            // 方法1: 使用files.write
            utils.log("SIMPLE: 尝试使用files.write写入文件...");
            files.write(filePath, content);
            utils.log(`SIMPLE: 使用files.write写入文件成功: ${filePath}`);
        } catch (writeError) {
            utils.log(`SIMPLE: 使用files.write写入文件失败: ${writeError}`);

            // 方法2: 使用files.create和files.append
            try {
                utils.log("SIMPLE: 尝试使用files.create和files.append写入文件...");
                files.create(filePath);
                files.append(filePath, content);
                utils.log(`SIMPLE: 使用files.create和files.append写入文件成功: ${filePath}`);
            } catch (appendError) {
                utils.log(`SIMPLE: 使用files.create和files.append写入文件失败: ${appendError}`);

                // 方法3: 使用备用路径
                try {
                    let backupPath = files.join(files.cwd(), `XHS_Comments_Backup_${timestamp}.txt`);
                    utils.log(`SIMPLE: 尝试使用备用路径写入文件: ${backupPath}`);
                    files.write(backupPath, content);
                    utils.log(`SIMPLE: 使用备用路径写入文件成功: ${backupPath}`);
                    filePath = backupPath; // 更新文件路径
                } catch (backupError) {
                    utils.log(`SIMPLE: 使用备用路径写入文件失败: ${backupError}`);
                    throw new Error("所有写入文件的方法都失败了");
                }
            }
        }

        utils.log(`SIMPLE: 评论已保存到文件: ${filePath}`);

        // 显示通知 - 使用去重后的评论数量
        toast(`已保存 ${uniqueCommentsArray.length} 条评论到文件，其中 ${matchedComments.length} 条匹配关键字`);

        // 显示评论统计
        utils.log(`SIMPLE: 评论统计: 总共 ${uniqueCommentsArray.length} 条评论，其中 ${matchedComments.length} 条匹配关键字`);
    } catch (contentError) {
        utils.log(`SIMPLE: 创建或写入文件内容时出错: ${contentError}`);
        throw contentError;
    }
}

// 导出模块
module.exports = {
    processImageTextNote: processImageTextNote, // 新增：图文笔记业务统一处理
    saveCommentsToFile: saveCommentsToFile,
    collectAndSaveComments: collectAndSaveComments,
    likeComment: likeComment,
    isCommentLiked: isCommentLiked,
    markCommentAsLiked: markCommentAsLiked,
    getLikedCommentsCount: getLikedCommentsCount
};

utils.log("SIMPLE: 小红书评论采集模块 (简化版) 加载完毕。");
