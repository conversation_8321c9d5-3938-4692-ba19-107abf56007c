// 数据管理模块：负责存储和管理数据

// Storage instance for scraped comment users
const dataStorage = storages.create("xhs_auto_script_scraped_data");
const SCRAPED_USERS_STORAGE_KEY = "scraped_comment_users";

let targetUsers = []; // { user_id: "", nickname: "", comment: "", status: "pending/sent/failed" }
// let accounts = []; // { username: "", password: "", send_count_today: 0, last_send_time: null }

// function loadTargetUsers() {
//   // 从文件或存储中加载用户列表
//   // let storedUsers = files.read("./target_users.json");
//   // if (storedUsers) {
//   //   targetUsers = JSON.parse(storedUsers);
//   // }
//   // console.log("目标用户列表已加载，数量: " + targetUsers.length);
// }

// function saveTargetUsers() {
//   // 将用户列表保存到文件
//   // files.write("./target_users.json", JSON.stringify(targetUsers));
//   // console.log("目标用户列表已保存。");
// }

// function addTargetUser(user) {
//   // 添加新的目标用户，避免重复
//   // if (!targetUsers.find(u => u.user_id === user.user_id)) {
//   //   targetUsers.push(user);
//   // }
// }

// function updateUserStatus(userId, status) {
//   // 更新用户发送状态
//   // let user = targetUsers.find(u => u.user_id === userId);
//   // if (user) {
//   //   user.status = status;
//   // }
// }

// function getPendingUsers() {
//   // return targetUsers.filter(u => u.status === 'pending');
// }

// function loadAccounts(accountString) {
//   // 解析UI输入的账号信息
//   // accounts = [];
//   // if(accountString) {
//   //   const lines = accountString.split('\n');
//   //   lines.forEach(line => {
//   //     const parts = line.split('----');
//   //     if(parts.length === 2) {
//   //       accounts.push({username: parts[0].trim(), password: parts[1].trim(), send_count_today: 0, last_send_time: null});
//   //     }
//   //   });
//   // }
//   // console.log("账号已加载: ", accounts.length);
// }

// function getNextAvailableAccount() {
//   // 获取下一个可用的账号，用于轮询
//   // 每日发送上限等逻辑
//   // return accounts.find(acc => acc.send_count_today < 5); // 假设每日5条
// }

// function recordMessageSent(accountUsername) {
//   // let account = accounts.find(acc => acc.username === accountUsername);
//   // if (account) {
//   //   account.send_count_today++;
//   //   account.last_send_time = new Date();
//   //   // TODO: 需要持久化账号状态，或者在每次启动时重置计数器（如果需要基于“今日”）
//   // }
// }

// Functions for Scraped Comment Users
// ScrapedCommentUser structure:
// {
//   uid: "string",
//   nickname: "string",
//   commentText: "string",
//   noteId: "string",
//   noteTitle: "string" | null, // 可选
//   timestamp: "number" // e.g., Date.now()
// }

/**
 * Retrieves all scraped comment users from storage.
 * @returns {Array<Object>} An array of scraped user objects, or an empty array if none are found or an error occurs.
 */
function getScrapedCommentUsers() {
    try {
        const usersJson = dataStorage.get(SCRAPED_USERS_STORAGE_KEY);
        return usersJson ? JSON.parse(usersJson) : [];
    } catch (e) {
        console.error("DM_ERROR: Failed to get scraped comment users:", e);
        toast("获取已采集用户数据失败: " + e);
        return [];
    }
}

/**
 * 从评论内容中提取地区信息
 * @param {string} commentText - 评论内容
 * @returns {string} 提取到的地区名称，如果没有找到则返回'未知'
 */
function extractRegionFromComment(commentText) {
    if (!commentText || typeof commentText !== 'string') {
        return '未知';
    }

    // 定义地区关键词列表
    var regions = [
        '北京', '上海', '天津', '重庆',
        '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',
        '江苏', '浙江', '安徽', '福建', '江西', '山东',
        '河南', '湖北', '湖南', '广东', '广西', '海南',
        '四川', '贵州', '云南', '西藏', '陕西', '甘肃',
        '青海', '宁夏', '新疆',
        // 主要城市
        '深圳', '广州', '杭州', '南京', '苏州', '成都', '武汉',
        '西安', '长沙', '郑州', '济南', '青岛', '大连', '沈阳',
        '哈尔滨', '长春', '石家庄', '太原', '呼和浩特', '南昌',
        '合肥', '福州', '厦门', '南宁', '海口', '昆明', '贵阳',
        '拉萨', '兰州', '西宁', '银川', '乌鲁木齐'
    ];

    // 在评论中搜索地区关键词，支持多种表达方式
    for (var i = 0; i < regions.length; i++) {
        var region = regions[i];

        // 检查多种表达方式
        var patterns = [
            region,                    // 直接匹配：北京
            '在' + region,             // 在北京
            region + '的',             // 北京的
            region + '人',             // 北京人
            region + '这边',           // 北京这边
            region + '那边',           // 北京那边
            '来自' + region,           // 来自北京
            '我是' + region,           // 我是北京
            region + '本地',           // 北京本地
            region + '当地'            // 北京当地
        ];

        for (var j = 0; j < patterns.length; j++) {
            if (commentText.indexOf(patterns[j]) !== -1) {
                // 如果是城市，尝试匹配对应的省份
                var cityToProvince = {
                    '深圳': '广东', '广州': '广东', '杭州': '浙江', '南京': '江苏',
                    '苏州': '江苏', '成都': '四川', '武汉': '湖北', '西安': '陕西',
                    '长沙': '湖南', '郑州': '河南', '济南': '山东', '青岛': '山东',
                    '大连': '辽宁', '沈阳': '辽宁', '哈尔滨': '黑龙江', '长春': '吉林',
                    '石家庄': '河北', '太原': '山西', '呼和浩特': '内蒙古', '南昌': '江西',
                    '合肥': '安徽', '福州': '福建', '厦门': '福建', '南宁': '广西',
                    '海口': '海南', '昆明': '云南', '贵阳': '贵州', '拉萨': '西藏',
                    '兰州': '甘肃', '西宁': '青海', '银川': '宁夏', '乌鲁木齐': '新疆'
                };

                var finalRegion = cityToProvince[region] || region;
                console.log("DM_REGION: 从评论 '" + commentText + "' 中提取到地区: " + finalRegion);
                return finalRegion;
            }
        }
    }

    return '未知';
}

/**
 * Saves an array of new scraped comment users to storage.
 * Appends to existing users with deduplication.
 * @param {Array<Object>} newUsersArray - An array of new user objects to save.
 * @returns {boolean} True if saving was successful, false otherwise.
 */
function saveScrapedCommentUsers(newUsersArray) {
    if (!Array.isArray(newUsersArray) || newUsersArray.length === 0) {
        console.log("DM_INFO: No new scraped users to save.");
        return false;
    }
    try {
        var existingUsers = getScrapedCommentUsers();

        // 实现去重逻辑：基于 uid + noteId 的组合进行去重
        var combinedUsers = existingUsers.slice(); // 使用slice()代替扩展运算符
        var addedCount = 0;

        for (var i = 0; i < newUsersArray.length; i++) {
            var newUser = newUsersArray[i];

            // 数据验证：检查必要字段
            if (!newUser.uid || !newUser.nickname || !newUser.noteId) {
                console.log("DM_SKIP: 跳过无效用户数据 - 缺少必要字段: " + JSON.stringify(newUser));
                continue;
            }

            // 增强去重逻辑：多重检查避免重复
            var isDuplicate = false;
            for (var j = 0; j < combinedUsers.length; j++) {
                var existingUser = combinedUsers[j];
                // 主要去重：uid + noteId 组合
                if (existingUser.uid === newUser.uid && existingUser.noteId === newUser.noteId) {
                    isDuplicate = true;
                    console.log("DM_DEDUP: 发现重复用户 (uid+noteId): " + newUser.uid + " in " + newUser.noteId);
                    break;
                }
                // 备用去重：相同昵称 + 相同评论内容 + 相同笔记
                if (existingUser.nickname === newUser.nickname &&
                    existingUser.commentText === newUser.commentText &&
                    existingUser.noteId === newUser.noteId) {
                    isDuplicate = true;
                    console.log("DM_DEDUP: 发现重复用户 (nickname+comment+noteId): " + newUser.nickname);
                    break;
                }
            }

            if (!isDuplicate) {
                // 从评论内容中提取地区信息
                var extractedRegion = extractRegionFromComment(newUser.commentText || newUser.comment || '');

                // 添加时间戳和状态字段
                var userWithMetadata = {
                    uid: newUser.uid,
                    nickname: newUser.nickname,
                    xhsId: newUser.xhsId,
                    commentText: newUser.commentText,
                    noteId: newUser.noteId,
                    noteTitle: newUser.noteTitle,
                    timestamp: newUser.timestamp || Date.now(),
                    status: newUser.status || 'collected',
                    region: newUser.region || extractedRegion
                };
                combinedUsers.push(userWithMetadata);
                addedCount++;
            }
        }

        dataStorage.put(SCRAPED_USERS_STORAGE_KEY, JSON.stringify(combinedUsers));
        console.log("DM_SUCCESS: Added " + addedCount + " new unique users. Total: " + combinedUsers.length);
        toast("成功保存 " + addedCount + " 个新用户，总计 " + combinedUsers.length + " 个用户");
        return true;
    } catch (e) {
        console.error("DM_ERROR: Failed to save scraped comment users:", e);
        toast("保存采集用户数据失败: " + e);
        return false;
    }
}

/**
 * Clears all scraped comment users from storage.
 * @returns {boolean} True if clearing was successful, false otherwise.
 */
function clearScrapedCommentUsers() {
    try {
        dataStorage.remove(SCRAPED_USERS_STORAGE_KEY);
        console.log("DM_SUCCESS: Cleared all scraped comment users.");
        toast("已清空所有采集的用户数据");
        return true;
    } catch (e) {
        console.error("DM_ERROR: Failed to clear scraped comment users:", e);
        toast("清空采集用户数据失败: " + e);
        return false;
    }
}

function exportUsersToCSV(usersToExport, filePath) {
    if (!usersToExport || usersToExport.length === 0) {
        console.log("没有用户数据可导出 (CSV)");
        toast("没有用户数据可导出");
        return false;
    }
    // 增强的CSV头部，包含更多字段
    var csvContent = "用户ID,昵称,小红书号,评论内容,笔记ID,笔记标题,地区,状态,采集时间\n";

    for (var i = 0; i < usersToExport.length; i++) {
        var user = usersToExport[i];
        function CsvSafe(str) {
            return str ? '"' + String(str).replace(/"/g, '""') + '"' : '""';
        }
        function formatDate(timestamp) {
            if (!timestamp) return "";
            return new Date(timestamp).toLocaleString('zh-CN');
        }

        csvContent += CsvSafe(user.uid || user.user_id) + "," +
            CsvSafe(user.nickname) + "," +
            CsvSafe(user.xhsId || '') + "," +
            CsvSafe(user.commentText || user.comment) + "," +
            CsvSafe(user.noteId) + "," +
            CsvSafe(user.noteTitle) + "," +
            CsvSafe(user.region) + "," +
            CsvSafe(user.status) + "," +
            CsvSafe(formatDate(user.timestamp)) + "\n";
    }

    try {
        files.ensureDir(filePath.substring(0, filePath.lastIndexOf("/")));
        files.write(filePath, csvContent);
        console.log("用户数据已导出到 CSV: " + filePath);
        toast("用户数据已导出到: " + filePath);
        return true;
    } catch (e) {
        console.error("导出 CSV 文件失败: ", e);
        toast("导出 CSV 文件失败");
        return false;
    }
}

function exportUsersToTXT(usersToExport, filePath) {
    if (!usersToExport || usersToExport.length === 0) {
        console.log("没有用户数据可导出 (TXT)");
        toast("没有用户数据可导出");
        return false;
    }
    var txtContent = "";
    for (var i = 0; i < usersToExport.length; i++) {
        var user = usersToExport[i];
        txtContent += "User ID: " + (user.uid || user.user_id || 'N/A') + "\n";
        txtContent += "Nickname: " + (user.nickname || 'N/A') + "\n";
        txtContent += "XHS ID: " + (user.xhsId || 'N/A') + "\n";
        txtContent += "Comment: " + (user.commentText || user.comment || 'N/A') + "\n";
        txtContent += "Note Title: " + (user.noteTitle || 'N/A') + "\n";
        txtContent += "Region: " + (user.region || 'N/A') + "\n";
        txtContent += "Status: " + (user.status || 'N/A') + "\n";
        txtContent += "Timestamp: " + (user.timestamp ? new Date(user.timestamp).toLocaleString('zh-CN') : 'N/A') + "\n";
        txtContent += "--------------------\n";
    }

    try {
        files.ensureDir(filePath.substring(0, filePath.lastIndexOf("/")));
        files.write(filePath, txtContent);
        console.log("用户数据已导出到 TXT: " + filePath);
        toast("用户数据已导出到: " + filePath);
        return true;
    } catch (e) {
        console.error("导出 TXT 文件失败: ", e);
        toast("导出 TXT 文件失败");
        return false;
    }
}

/**
 * 根据关键词搜索用户
 * @param {string} keyword - 搜索关键词
 * @returns {Array<Object>} 匹配的用户数组
 */
function searchUsers(keyword) {
    try {
        var allUsers = getScrapedCommentUsers();
        if (!keyword || keyword.trim() === '') {
            return allUsers;
        }

        var searchTerm = keyword.toLowerCase();
        var results = [];
        for (var i = 0; i < allUsers.length; i++) {
            var user = allUsers[i];
            if ((user.nickname && user.nickname.toLowerCase().indexOf(searchTerm) !== -1) ||
                (user.commentText && user.commentText.toLowerCase().indexOf(searchTerm) !== -1) ||
                (user.noteTitle && user.noteTitle.toLowerCase().indexOf(searchTerm) !== -1) ||
                (user.region && user.region.toLowerCase().indexOf(searchTerm) !== -1)) {
                results.push(user);
            }
        }
        return results;
    } catch (e) {
        console.error("DM_ERROR: Failed to search users:", e);
        return [];
    }
}

/**
 * 根据地区筛选用户
 * @param {string} region - 地区名称
 * @returns {Array<Object>} 匹配的用户数组
 */
function filterUsersByRegion(region) {
    try {
        var allUsers = getScrapedCommentUsers();
        if (!region || region === '不限' || region === '全部') {
            return allUsers;
        }

        var results = [];
        for (var i = 0; i < allUsers.length; i++) {
            var user = allUsers[i];
            if (user.region === region) {
                results.push(user);
            }
        }
        return results;
    } catch (e) {
        console.error("DM_ERROR: Failed to filter users by region:", e);
        return [];
    }
}

/**
 * 更新现有用户的地区信息（从评论中重新提取）
 * @returns {boolean} 更新是否成功
 */
function updateUsersRegionFromComments() {
    try {
        var allUsers = getScrapedCommentUsers();
        var updatedCount = 0;

        for (var i = 0; i < allUsers.length; i++) {
            var user = allUsers[i];
            // 如果用户地区为空或为"未知"，尝试从评论中提取
            if (!user.region || user.region === '未知') {
                var extractedRegion = extractRegionFromComment(user.commentText || user.comment || '');
                if (extractedRegion !== '未知') {
                    user.region = extractedRegion;
                    updatedCount++;
                }
            }
        }

        if (updatedCount > 0) {
            dataStorage.put(SCRAPED_USERS_STORAGE_KEY, JSON.stringify(allUsers));
            console.log("DM_SUCCESS: Updated region for " + updatedCount + " users");
            toast("已更新 " + updatedCount + " 个用户的地区信息");
        } else {
            console.log("DM_INFO: No users needed region update");
            toast("没有需要更新地区信息的用户");
        }

        return true;
    } catch (e) {
        console.error("DM_ERROR: Failed to update users region:", e);
        toast("更新用户地区信息失败: " + e);
        return false;
    }
}



/**
 * 获取用户统计信息
 * @returns {Object} 统计信息对象
 */
function getUserStatistics() {
    try {
        var allUsers = getScrapedCommentUsers();
        var stats = {
            totalUsers: allUsers.length,
            regionStats: {},
            statusStats: {},
            recentUsers: 0 // 最近24小时采集的用户
        };

        var oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);

        for (var i = 0; i < allUsers.length; i++) {
            var user = allUsers[i];
            // 地区统计
            var region = user.region || '未知';
            stats.regionStats[region] = (stats.regionStats[region] || 0) + 1;

            // 状态统计
            var status = user.status || 'unknown';
            stats.statusStats[status] = (stats.statusStats[status] || 0) + 1;

            // 最近用户统计
            if (user.timestamp && user.timestamp > oneDayAgo) {
                stats.recentUsers++;
            }
        }

        return stats;
    } catch (e) {
        console.error("DM_ERROR: Failed to get user statistics:", e);
        return { totalUsers: 0, regionStats: {}, statusStats: {}, recentUsers: 0 };
    }
}

module.exports = {
    //   loadTargetUsers: loadTargetUsers,
    //   saveTargetUsers: saveTargetUsers,
    //   addTargetUser: addTargetUser,
    //   updateUserStatus: updateUserStatus,
    //   getPendingUsers: getPendingUsers,
    //   loadAccounts: loadAccounts,
    //   getNextAvailableAccount: getNextAvailableAccount,
    //   recordMessageSent: recordMessageSent,
    targetUsers: targetUsers, // 暴露列表以便UI显示
    //   accounts: accounts,
    exportUsersToCSV: exportUsersToCSV,
    exportUsersToTXT: exportUsersToTXT,
    // Scraped comment user functions
    getScrapedCommentUsers: getScrapedCommentUsers,
    saveScrapedCommentUsers: saveScrapedCommentUsers,
    clearScrapedCommentUsers: clearScrapedCommentUsers,
    // 新增功能
    searchUsers: searchUsers,
    filterUsersByRegion: filterUsersByRegion,
    getUserStatistics: getUserStatistics,
    updateUsersRegionFromComments: updateUsersRegionFromComments
};

console.log("数据管理模块加载完毕 (data_manager.js)");