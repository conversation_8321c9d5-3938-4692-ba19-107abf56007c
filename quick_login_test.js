// quick_login_test.js - 快速登录流程测试脚本
// 专门用于快速验证小红书登录代码逻辑

"ui";

// 简化的UI布局
ui.layout(
    <vertical padding="16dp">
        <text textSize="18sp" textColor="#2196F3" text="小红书登录流程快速测试" gravity="center" marginBottom="16dp" />

        <card cardBackgroundColor="#e8f5e8" cardCornerRadius="8dp" cardElevation="2dp" margin="8dp">
            <vertical padding="12dp">
                <text textSize="14sp" textColor="#2e7d32" textStyle="bold" text="✓ 快速测试说明" />
                <text textSize="12sp" textColor="#2e7d32" text="• 此工具专门测试登录/退出流程" marginTop="4dp" />
                <text textSize="12sp" textColor="#2e7d32" text="• 请确保小红书已安装并可正常使用" marginTop="2dp" />
                <text textSize="12sp" textColor="#2e7d32" text="• 建议使用测试账号进行验证" marginTop="2dp" />
            </vertical>
        </card>

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试账号:" />
        <input id="testUsername" hint="输入手机号" inputType="phone" />
        <input id="testPassword" hint="输入密码" inputType="textPassword" />

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="快速测试:" />
        <horizontal>
            <button id="testLogoutBtn" text="测试退出" layout_weight="1" bg="#FF9800" textColor="white" />
            <button id="testLoginBtn" text="测试登录" layout_weight="1" bg="#4CAF50" textColor="white" />
        </horizontal>

        <horizontal marginTop="8dp">
            <button id="testElementsBtn" text="检测元素" layout_weight="1" bg="#2196F3" textColor="white" />
            <button id="fullTestBtn" text="完整流程" layout_weight="1" bg="#9C27B0" textColor="white" />
        </horizontal>

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试结果:" />
        <text id="testResult" text="等待测试..." textSize="14sp" textColor="#666666" />

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="详细日志:" />
        <ScrollView layout_weight="1">
            <text id="detailLog" text="" textSize="10sp" />
        </ScrollView>

        <button id="clearBtn" text="清空日志" marginTop="8dp" />
    </vertical>
);

// 导入账号管理模块
let accountManager;
try {
    accountManager = require('./xhs_account_manager.js');
    accountManager.initAccountManager();
} catch (e) {
    console.error("加载账号管理模块失败:", e);
}

// 日志函数
function log(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;

    ui.run(() => {
        const currentLog = ui.detailLog.getText().toString();
        ui.detailLog.setText(currentLog + logMessage + "\n");
    });

    console.log(logMessage);
}

function setResult(result, color) {
    if (!color) color = "#666666";
    ui.run(() => {
        ui.testResult.setText(result);
        ui.testResult.setTextColor(colors.parseColor(color));
    });
}

// 元素检测测试
function testElements() {
    log("开始检测关键元素...");
    setResult("检测中...", "#FF9800");

    try {
        // 确保小红书已启动
        if (currentPackage() !== "com.xingin.xhs") {
            log("启动小红书应用...");
            launch("com.xingin.xhs");
            sleep(3000);
        }

        // 关键元素列表
        const elements = [
            { name: "设置按钮", id: "com.xingin.xhs:id/i48", critical: true },
            { name: "设置面板", id: "com.xingin.xhs:id/g10", critical: false },
            { name: "退出登录按钮", id: "com.xingin.xhs:id/ebt", critical: false },
            { name: "确认退出按钮", id: "com.xingin.xhs:id/h0g", critical: false },
            { name: "其它登录方式", id: "com.xingin.xhs:id/gil", critical: false },
            { name: "手机号登录", id: "com.xingin.xhs:id/axu", critical: false },
            { name: "密码登录", id: "com.xingin.xhs:id/iho", critical: false },
            { name: "协议勾选框", id: "com.xingin.xhs:id/gws", critical: false },
            { name: "登录按钮", id: "com.xingin.xhs:id/f9l", critical: false }
        ];

        let foundCount = 0;
        let criticalFound = 0;
        let criticalTotal = 0;

        for (let element of elements) {
            if (element.critical) criticalTotal++;

            const found = id(element.id).exists();
            if (found) {
                foundCount++;
                if (element.critical) criticalFound++;
            }

            const status = found ? "✓" : "✗";
            const priority = element.critical ? "[关键]" : "[普通]";
            log(`${status} ${priority} ${element.name}: ${found ? "找到" : "未找到"}`);
        }

        const result = `元素检测完成: ${foundCount}/${elements.length} (关键: ${criticalFound}/${criticalTotal})`;
        log(result);

        if (criticalFound === criticalTotal) {
            setResult("元素检测通过", "#4CAF50");
        } else {
            setResult("关键元素缺失", "#f44336");
        }

    } catch (e) {
        log("元素检测失败: " + e.toString());
        setResult("检测失败", "#f44336");
    }
}

// 退出登录测试
function testLogout() {
    log("开始测试退出登录流程...");
    setResult("退出中...", "#FF9800");

    try {
        if (!accountManager) {
            throw new Error("账号管理模块未加载");
        }

        const result = accountManager.performLogout();

        if (result) {
            log("退出登录成功");
            setResult("退出成功", "#4CAF50");
        } else {
            log("退出登录失败");
            setResult("退出失败", "#f44336");
        }

    } catch (e) {
        log("退出登录异常: " + e.toString());
        setResult("退出异常", "#f44336");
    }
}

// 登录测试
function testLogin() {
    const username = ui.testUsername.getText().toString().trim();
    const password = ui.testPassword.getText().toString().trim();

    if (!username || !password) {
        toast("请输入用户名和密码");
        return;
    }

    log("开始测试登录流程...");
    log("测试账号: " + username);
    setResult("登录中...", "#FF9800");

    try {
        if (!accountManager) {
            throw new Error("账号管理模块未加载");
        }

        const account = { username: username, password: password };
        const result = accountManager.performLogin(account);

        if (result) {
            log("登录成功");
            setResult("登录成功", "#4CAF50");
        } else {
            log("登录失败");
            setResult("登录失败", "#f44336");
        }

    } catch (e) {
        log("登录异常: " + e.toString());
        setResult("登录异常", "#f44336");
    }
}

// 完整流程测试
function testFullFlow() {
    const username = ui.testUsername.getText().toString().trim();
    const password = ui.testPassword.getText().toString().trim();

    if (!username || !password) {
        toast("请输入用户名和密码");
        return;
    }

    log("=== 开始完整流程测试 ===");
    setResult("完整测试中...", "#FF9800");

    try {
        if (!accountManager) {
            throw new Error("账号管理模块未加载");
        }

        // 1. 应用控制测试
        log("步骤1: 测试应用控制...");
        const closeResult = accountManager.closeXhsApp();
        log("关闭应用: " + (closeResult ? "成功" : "失败"));

        sleep(2000);

        const startResult = accountManager.startXhsApp();
        log("启动应用: " + (startResult ? "成功" : "失败"));

        if (!startResult) {
            throw new Error("应用启动失败");
        }

        // 2. 退出登录测试
        log("步骤2: 测试退出登录...");
        const logoutResult = accountManager.performLogout();
        log("退出登录: " + (logoutResult ? "成功" : "失败"));

        // 3. 登录测试
        log("步骤3: 测试登录...");
        const account = { username: username, password: password };
        const loginResult = accountManager.performLogin(account);
        log("登录: " + (loginResult ? "成功" : "失败"));

        // 结果评估
        const allSuccess = closeResult && startResult && logoutResult && loginResult;

        if (allSuccess) {
            log("=== 完整流程测试通过 ===");
            setResult("完整测试通过", "#4CAF50");
        } else {
            log("=== 完整流程测试失败 ===");
            setResult("完整测试失败", "#f44336");
        }

    } catch (e) {
        log("完整流程测试异常: " + e.toString());
        setResult("测试异常", "#f44336");
    }
}

// UI事件处理
ui.testElementsBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    threads.start(testElements);
});

ui.testLogoutBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    threads.start(testLogout);
});

ui.testLoginBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    threads.start(testLogin);
});

ui.fullTestBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    threads.start(testFullFlow);
});

ui.clearBtn.on("click", () => {
    ui.detailLog.setText("");
    setResult("日志已清空", "#666666");
});

// 初始化
log("快速登录测试工具已启动");

if (!auto.service) {
    log("警告: 无障碍服务未开启");
    setResult("需要无障碍服务", "#f44336");
} else {
    setResult("就绪", "#4CAF50");
}
