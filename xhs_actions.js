// ======================= xhs_actions.js START (incorporating adapted official extraction) =======================
// xhs_actions.js - 小红书App交互操作
// 包含搜索、导航等基础功能

const utils = require('./utils.js'); // 引入通用工具函数
const commentActions = require('./xhs_comment_actions.js'); // 引入评论区操作模块
const noteTypes = require('./xhs_note_types.js'); // 引入笔记类型检测模块
// noteNavigation 模块引用已移除 - 现在由 main.js 直接处理搜索结果页逻辑
// 移除循环依赖：simpleComments 和 videoComments 模块引用已移除

// 安全包装函数，避免findOne()和sleep()卡死
function safeFindOne(selector, timeout, description) {
    try {
        if (description) {
            utils.log(`PPACTIONS_SAFE: 尝试查找元素: ${description}`);
        }
        return selector.findOne(timeout || 1000);
    } catch (e) {
        if (description) {
            utils.log(`PPACTIONS_SAFE: 查找元素失败 (${description}): ${e.toString()}`);
        } else {
            utils.log(`PPACTIONS_SAFE: 查找元素失败: ${e.toString()}`);
        }
        return null;
    }
}

function safeSleep(duration, description) {
    try {
        if (description) {
            utils.log(`PPACTIONS_SAFE: 开始等待 ${duration}ms: ${description}`);
        }
        // 将长时间sleep拆分为多个短时间sleep，避免卡死
        if (duration > 1000) {
            const chunks = Math.ceil(duration / 500);
            for (let i = 0; i < chunks; i++) {
                const chunkDuration = Math.min(500, duration - i * 500);
                java.lang.Thread.sleep(chunkDuration);
                if (description && i % 2 === 0) {
                    utils.log(`PPACTIONS_SAFE: 等待进度 ${Math.round((i + 1) / chunks * 100)}%`);
                }
            }
        } else {
            java.lang.Thread.sleep(duration);
        }
        if (description) {
            utils.log(`PPACTIONS_SAFE: 等待完成: ${description}`);
        }
    } catch (e) {
        if (description) {
            utils.log(`PPACTIONS_SAFE: 等待失败 (${description}): ${e.toString()}`);
        } else {
            utils.log(`PPACTIONS_SAFE: 等待失败: ${e.toString()}`);
        }
    }
}

// --- 选择器 ---
const SELECTORS = {
    // 搜索结果页相关
    SEARCH_RESULTS_PAGE_INDICATOR: { id: "com.xingin.xhs:id/ch9", text: "筛选" }, // 使用 ch9 作为主要指示器

    // 笔记卡片选择器
    NOTE_CARD_ITEM: { id: "com.xingin.xhs:id/hpx" },
    NOTE_CARD_TITLE: { id: "com.xingin.xhs:id/g_q" },
    NOTE_CARD_USER_NICKNAME: { id: "com.xingin.xhs:id/zb" },

    // 笔记详情页相关
    NOTE_DETAIL_PAGE_INDICATOR: { id: "com.xingin.xhs:id/a0r", className: "android.view.FrameLayout" },
    NOTE_DETAIL_TITLE: { id: "com.xingin.xhs:id/g_s", className: "android.widget.TextView" },
    NOTE_DETAIL_COMMENT_ICON: { id: "com.xingin.xhs:id/dwu", className: "android.widget.TextView" },
    NOTE_DETAIL_USER_NICKNAME: { id: "com.xingin.xhs:id/nickNameTV", className: "android.widget.TextView" },

    // 通用
    BACK_BUTTON: { id: "com.xingin.xhs:id/a2q", desc: "返回", clickable: true },
    LOADING_INDICATOR: { id: "com.xingin.xhs:id/loading_progress_bar_id" }
};


// 辅助函数：尝试以多种方式点击筛选面板中的元素 (与你之前版本一致)
function tryClickInPanel(textElement, elementNameForLog) {
    if (!textElement) {
        utils.log(`WARN: PPACTIONS: [tryClickInPanel] 目标元素 "${elementNameForLog}" 未提供 (null)，无法尝试点击。`);
        return false;
    }
    utils.log(`PPACTIONS: [tryClickInPanel] 开始处理点击 "${elementNameForLog}".`);
    let clickSuccessful = false;
    let initialProps = "N/A";
    try {
        initialProps = `ID: ${textElement.id()}, Class: ${textElement.className()}, Text: "${textElement.text()}", Desc: "${textElement.desc()}", Bounds: ${textElement.bounds()}, Clickable: ${textElement.clickable()}, Visible: ${textElement.visibleToUser()}`;
        utils.log(`PPACTIONS: [tryClickInPanel] "${elementNameForLog}" 初始属性: ${initialProps}`);
    } catch (e) {
        utils.log(`WARN: PPACTIONS: [tryClickInPanel] 获取 "${elementNameForLog}" 初始属性失败: ${e.toString()}`);
    }

    utils.log(`PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "${elementNameForLog}" 文本元素自身.`);
    if (textElement.visibleToUser()) {
        if (textElement.clickable()) {
            utils.log(`PPACTIONS: [tryClickInPanel] "${elementNameForLog}" 可见且可直接点击. 尝试 click().`);
            try {
                if (textElement.click()) {
                    utils.log(`PPACTIONS: [tryClickInPanel] 成功直接点击 "${elementNameForLog}".`);
                    clickSuccessful = true;
                } else {
                    utils.log(`WARN: PPACTIONS: [tryClickInPanel] 直接点击 "${elementNameForLog}" 失败 (click() 返回 false).`);
                }
            } catch (e) {
                utils.log(`ERROR: PPACTIONS: [tryClickInPanel] 直接点击 "${elementNameForLog}" 时发生异常: ${e.toString()}`);
            }
        } else {
            utils.log(`PPACTIONS: [tryClickInPanel] "${elementNameForLog}" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.`);
        }
    } else {
        utils.log(`PPACTIONS: [tryClickInPanel] "${elementNameForLog}" 自身不可见 (visibleToUser: false). 跳过直接点击自身.`);
    }

    if (!clickSuccessful) {
        utils.log(`PPACTIONS: [tryClickInPanel] 步骤 2: "${elementNameForLog}" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.`);
        let parent = textElement.parent();
        for (let i = 0; i < 4 && parent && !clickSuccessful; i++) {
            utils.log(`PPACTIONS: [tryClickInPanel] 检查父控件 (层级 ${i + 1}) for "${elementNameForLog}".`);
            let parentProps = "N/A";
            try {
                parentProps = `ID: ${parent.id()}, Class: ${parent.className()}, Text: "${parent.text()}", Desc: "${parent.desc()}", Bounds: ${parent.bounds()}, Clickable: ${parent.clickable()}, Visible: ${parent.visibleToUser()}`;
                utils.log(`PPACTIONS: [tryClickInPanel] 父控件 (层级 ${i + 1}) 属性: ${parentProps}`);

                if (parent.visibleToUser() && parent.clickable()) {
                    utils.log(`PPACTIONS: [tryClickInPanel] 父控件 (层级 ${i + 1}) for "${elementNameForLog}" 可见且可点击. 尝试 click().`);
                    if (parent.click()) {
                        utils.log(`PPACTIONS: [tryClickInPanel] 成功点击 "${elementNameForLog}" 的父控件 (层级 ${i + 1}).`);
                        clickSuccessful = true;
                        break;
                    } else {
                        utils.log(`WARN: PPACTIONS: [tryClickInPanel] 点击 "${elementNameForLog}" 的父控件 (层级 ${i + 1}) 失败 (click() 返回 false).`);
                    }
                } else {
                    utils.log(`PPACTIONS: [tryClickInPanel] 父控件 (层级 ${i + 1}) for "${elementNameForLog}" 不可见 (${parent.visibleToUser()}) 或不可点击 (${parent.clickable()}).`);
                }
            } catch (e) {
                utils.log(`ERROR: PPACTIONS: [tryClickInPanel] 处理父控件 (层级 ${i + 1}) for "${elementNameForLog}" 时发生异常: ${e.toString()}. 父控件属性快照: ${parentProps}`);
            }
            parent = parent.parent();
            if (!parent) {
                utils.log(`PPACTIONS: [tryClickInPanel] 已达顶层或父控件为null，停止向上查找 for "${elementNameForLog}".`);
                break;
            }
        }
        if (!clickSuccessful) {
            utils.log(`PPACTIONS: [tryClickInPanel] 未能通过查找父控件点击 "${elementNameForLog}".`);
        }
    }

    if (!clickSuccessful) {
        utils.log(`PPACTIONS: [tryClickInPanel] 步骤 3: 前两步均失败 for "${elementNameForLog}". 最后尝试直接点击原始文本元素.`);
        if (textElement.visibleToUser()) {
            utils.log(`PPACTIONS: [tryClickInPanel] "${elementNameForLog}" 原始文本元素可见. 执行“最后尝试”点击 (clickable属性当前为 ${textElement.clickable()}).`);
            try {
                if (textElement.click()) {
                    utils.log(`PPACTIONS: [tryClickInPanel] "最后尝试" 点击 "${elementNameForLog}" 执行成功 (click() 返回 true).`);
                    clickSuccessful = true;
                } else {
                    utils.log(`WARN: PPACTIONS: [tryClickInPanel] "最后尝试" 点击 "${elementNameForLog}" 失败 (click() 返回 false).`);
                }
            } catch (e) {
                utils.log(`ERROR: PPACTIONS: [tryClickInPanel] "最后尝试" 点击 "${elementNameForLog}" 时发生异常: ${e.toString()}`);
            }
        } else {
            utils.log(`WARN: PPACTIONS: [tryClickInPanel] "${elementNameForLog}" 原始文本元素在“最后尝试”时不可见. 无法点击.`);
        }
    }

    if (clickSuccessful) {
        utils.log(`PPACTIONS: [tryClickInPanel] "${elementNameForLog}" 的点击操作已标记为成功.`);
    } else {
        utils.log(`WARN: PPACTIONS: [tryClickInPanel] 所有点击尝试对 "${elementNameForLog}" 均失败.`);
    }
    return clickSuccessful;
}


function ensureAppReadyForSearch() {
    if (!utils.ensureAppOpen("小红书")) {
        utils.log("ERROR: PPACTIONS: 无法启动或切换到小红书App。");
        return false;
    }
    utils.log("PPACTIONS: 小红书App已准备好。");
    safeSleep(1000, "等待App稳定");
    return true;
}

function searchNotes(keyword, sortByOption, publishTimeOption, locationDistanceOption) {
    utils.log(`PPACTIONS: 开始笔记搜索: 关键字="${keyword}", 排序=${sortByOption}, 时间=${publishTimeOption}, 位置=${locationDistanceOption}`);

    if (!ensureAppReadyForSearch()) {
        return { success: false, message: "App未准备好搜索" };
    }

    let searchIcon = safeFindOne(desc("搜索"), 3000, "搜索图标");
    if (!searchIcon) { searchIcon = safeFindOne(idContains("search_icon").clickable(true), 2000, "搜索图标(ID)"); }
    if (!searchIcon) { searchIcon = safeFindOne(idContains("search_bar_middle_layout").clickable(true), 1000, "搜索栏布局"); }

    if (searchIcon) {
        utils.log("PPACTIONS: 找到首页搜索图标/按钮，点击进入搜索页。");
        searchIcon.click();
        safeSleep(2500, "等待搜索页加载");
    } else {
        utils.log("WARN: PPACTIONS: 未找到首页搜索图标，尝试直接查找搜索输入框（可能已在搜索页）。");
    }

    let searchBox;
    const searchBoxSelectors = [
        () => safeFindOne(className("android.widget.EditText").descContains("搜索"), 2000, "搜索框(描述)"),
        () => safeFindOne(idContains("search_input").className("android.widget.EditText"), 2000, "搜索框(ID)"),
        () => safeFindOne(focused(true).className("android.widget.EditText"), 1000, "搜索框(焦点)")
    ];

    for (let i = 0; i < searchBoxSelectors.length; i++) {
        utils.log(`PPACTIONS: 尝试查找搜索框 (方法 ${i + 1})...`);
        try {
            searchBox = searchBoxSelectors[i]();
            if (searchBox) {
                utils.log(`PPACTIONS: 找到搜索框 (方法 ${i + 1})。Bounds: ${searchBox.bounds()}`);
                break;
            }
        } catch (e) {
            utils.log(`PPACTIONS: 查找搜索框 (方法 ${i + 1}) 出错: ${e.toString()}`);
        }
    }

    if (!searchBox) {
        utils.log("ERROR: PPACTIONS: 未能通过任何方法找到搜索输入框。");
        return { success: false, message: "未找到搜索输入框" };
    }

    utils.log("PPACTIONS: 准备向搜索框输入文本...");
    if (searchBox.setText(keyword)) {
        utils.log(`PPACTIONS: 已调用 setText 输入关键字: ${keyword}`);
        sleep(1500);
    } else {
        utils.log(`ERROR: PPACTIONS: setText 向搜索框输入关键字 '${keyword}' 失败。`);
        return { success: false, message: "输入关键字失败" };
    }

    utils.log("PPACTIONS: 准备触发搜索。");
    let searchConfirmButton;
    let searchTriggered = false;

    utils.log("PPACTIONS: 步骤A - 尝试确保搜索框或其父控件有焦点...");
    let focusTarget = searchBox;
    if (!focusTarget.clickable()) {
        let parent = focusTarget.parent();
        for (let k = 0; k < 3 && parent; k++) {
            if (parent.clickable()) {
                focusTarget = parent;
                utils.log(`PPACTIONS: 使用可点击的父控件 (层级 ${k + 1}) 作为焦点目标。`);
                break;
            }
            parent = parent.parent();
        }
    }
    if (focusTarget && focusTarget.clickable()) {
        utils.log(`PPACTIONS: 点击焦点目标: ${focusTarget.bounds()} 以确保焦点。`);
        focusTarget.click();
        sleep(1000);
    } else {
        utils.log("WARN: PPACTIONS: 搜索框及其父控件均不可直接点击以设置焦点。");
    }

    utils.log("PPACTIONS: 步骤B - 尝试查找并点击页面上的搜索按钮...");
    const confirmButtonSelectors = [
        { name: "Exact ID 'com.xingin.xhs:id/fce', Text='搜索', ClassName=Button, Clickable", selector: () => id("com.xingin.xhs:id/fce").text("搜索").className("android.widget.Button").clickable(true).findOne(2000) },
        { name: "Text='搜索', ClassName=TextView, Clickable", selector: () => text("搜索").className("android.widget.TextView").clickable(true).findOne(2000) },
        { name: "Desc='搜索', Clickable", selector: () => desc("搜索").clickable(true).findOne(2000) },
        { name: "ID contains 'search_btn', Clickable", selector: () => idContains("search_btn").clickable(true).findOne(1500) },
        { name: "ID contains 'search_confirm_button', Clickable", selector: () => idContains("search_confirm_button").clickable(true).findOne(1500) },
        { name: "ID contains 'search_text_layout', Clickable", selector: () => idContains("search_text_layout").clickable(true).findOne(1500) },
        { name: "ID contains 'txt_search', Clickable", selector: () => idContains("txt_search").clickable(true).findOne(1500) },
        {
            name: "General Filter: Text or Desc='搜索', Clickable, Small, Visible", selector: () => {
                let btns = clickable(true).visibleToUser(true).filter(w =>
                    (w.text() && w.text().toString().trim() === "搜索") ||
                    (w.desc() && w.desc().toString().trim() === "搜索")
                );
                btns = btns.filter(w => {
                    const bounds = w.boundsInScreen();
                    return bounds.height() < device.height / 6 && bounds.width() < device.width / 2 && w.className() !== "android.widget.EditText";
                });
                return btns.nonEmpty() ? btns.orderBy("depth", "desc").findOnce() : null;
            }
        }
    ];

    for (let i = 0; i < confirmButtonSelectors.length; i++) {
        const currentSelector = confirmButtonSelectors[i];
        utils.log(`PPACTIONS: 尝试查找搜索确认按钮 (策略: ${currentSelector.name})...`);
        try {
            searchConfirmButton = currentSelector.selector();
            if (searchConfirmButton) {
                let details = `Text: "${searchConfirmButton.text()}", Desc: "${searchConfirmButton.desc()}", ID: ${searchConfirmButton.id()}, Class: ${searchConfirmButton.className()}, Bounds: ${searchConfirmButton.bounds()}, Clickable: ${searchConfirmButton.clickable()}, Visible: ${searchConfirmButton.visibleToUser()}`;
                utils.log(`PPACTIONS: 找到潜在搜索按钮。详情: ${details}`);

                if (searchConfirmButton.visibleToUser() && searchConfirmButton.clickable()) {
                    utils.log("PPACTIONS: 按钮可见且可点击，尝试执行 click()...");
                    if (searchConfirmButton.click()) {
                        utils.log("PPACTIONS: searchConfirmButton.click() 执行成功 (返回true)。等待页面跳转...");

                        // 动态等待页面跳转到搜索结果页，最多等待15秒
                        let pageTransitioned = false;
                        let waitAttempts = 0;
                        const maxWaitAttempts = 30; // 15秒，每次检查间隔500ms

                        while (waitAttempts < maxWaitAttempts && !pageTransitioned) {
                            sleep(500);
                            waitAttempts++;

                            // 检查应用是否仍在前台（APK环境中的重要检查）
                            try {
                                const currentPackage = currentPackage();
                                if (currentPackage !== "com.xingin.xhs") {
                                    utils.log(`PPACTIONS: 检测到应用切换到后台 (${currentPackage})，可能需要用户手动切换回小红书`);
                                    // 不立即失败，继续等待，用户可能会切换回来
                                }
                            } catch (e) {
                                // 忽略包名检测错误，继续执行
                            }

                            // 检查是否已经跳转到搜索结果页
                            if (isSearchResultsPage()) {
                                pageTransitioned = true;
                                utils.log(`PPACTIONS: 页面跳转成功，用时 ${waitAttempts * 0.5} 秒`);
                                break;
                            }

                            // 每2秒输出一次等待状态
                            if (waitAttempts % 4 === 0) {
                                utils.log(`PPACTIONS: 等待页面跳转中... (${waitAttempts * 0.5}/${maxWaitAttempts * 0.5}秒)`);
                            }
                        }

                        if (pageTransitioned) {
                            searchTriggered = true;
                            break;
                        } else {
                            utils.log("WARN: PPACTIONS: 等待页面跳转超时，但仍继续执行后续操作");
                            searchTriggered = true; // 即使超时也继续，可能页面已经跳转但检测失败
                            break;
                        }
                    } else {
                        utils.log("ERROR: PPACTIONS: searchConfirmButton.click() 执行失败 (返回false)。");
                    }
                } else {
                    utils.log("WARN: PPACTIONS: 按钮虽找到，但不可见或不可点击。");
                }
            }
        } catch (e) {
            utils.log(`PPACTIONS: 查找搜索确认按钮 (策略: ${currentSelector.name}) 时出错: ${e.toString()}`);
        }
        if (searchTriggered) break;
    }

    if (!searchTriggered) {
        utils.log("ERROR: PPACTIONS: 所有UI点击尝试均未能触发搜索。");
        return { success: false, message: "未能通过UI点击触发搜索操作" };
    }

    utils.log("PPACTIONS: 搜索已触发，准备应用筛选条件...");
    const sortOptionsText = ["综合", "最新", "最多点赞", "最多评论", "最多收藏"];
    const publishTimeOptionsText = ["不限", "一天内", "一周内", "半年内"];
    const locationOptionsText = ["不限", "同城", "附近"];

    let mainFilterButton;
    let filterPanelOpened = false;

    if ((sortByOption > 0 && sortByOption < sortOptionsText.length) ||
        (publishTimeOption > 0 && publishTimeOption < publishTimeOptionsText.length) ||
        (locationDistanceOption > 0 && locationDistanceOption < locationOptionsText.length)) {
        utils.log("PPACTIONS: 需要应用排序、发布时间或位置筛选，尝试打开主筛选面板。");
        const filterButtonSelectors = [
            // 优先使用父容器查找方法，因为直接查找经常失败
            {
                name: "FilterButtonInParentContainer_HOY",
                selector: () => {
                    try {
                        utils.log("PPACTIONS: 尝试在父容器 com.xingin.xhs:id/hoy 中查找筛选按钮");
                        const parentContainer = id("com.xingin.xhs:id/hoy").findOne(2000);
                        if (parentContainer) {
                            utils.log("PPACTIONS: 找到父容器 com.xingin.xhs:id/hoy，在其中查找 com.xingin.xhs:id/hpy");
                            const filterButton = parentContainer.findOne(id("com.xingin.xhs:id/hpy"));
                            if (filterButton) {
                                utils.log("PPACTIONS: 在父容器中成功找到筛选按钮");
                                return filterButton;
                            } else {
                                utils.log("PPACTIONS: 在父容器中未找到筛选按钮");
                            }
                        } else {
                            utils.log("PPACTIONS: 未找到父容器 com.xingin.xhs:id/hoy");
                        }
                        return null;
                    } catch (e) {
                        utils.log("PPACTIONS: 在父容器中查找筛选按钮时出错: " + e.toString());
                        return null;
                    }
                }
            },
            {
                name: "FilterButtonByIDHPY_Simple",
                selector: () => {
                    try {
                        utils.log("PPACTIONS: 尝试简单ID查找筛选按钮");
                        return id("com.xingin.xhs:id/hpy").findOne(1000);
                    } catch (e) {
                        utils.log("PPACTIONS: 简单ID查找出错: " + e.toString());
                        return null;
                    }
                }
            },
            {
                name: "FilterButtonByText_Simple",
                selector: () => {
                    try {
                        utils.log("PPACTIONS: 尝试文本查找筛选按钮");
                        return text("筛选").findOne(1000);
                    } catch (e) {
                        utils.log("PPACTIONS: 文本查找出错: " + e.toString());
                        return null;
                    }
                }
            },
            {
                name: "FilterButtonByClickable",
                selector: () => {
                    try {
                        utils.log("PPACTIONS: 尝试查找可点击的筛选相关元素");
                        // 查找所有可点击元素，然后过滤
                        const clickableElements = clickable(true).find();
                        for (let i = 0; i < clickableElements.length; i++) {
                            const element = clickableElements[i];
                            const text = element.text();
                            const desc = element.desc();
                            if ((text && text.includes("筛选")) || (desc && desc.includes("筛选"))) {
                                utils.log("PPACTIONS: 找到包含'筛选'的可点击元素");
                                return element;
                            }
                        }
                        return null;
                    } catch (e) {
                        utils.log("PPACTIONS: 可点击元素查找出错: " + e.toString());
                        return null;
                    }
                }
            }
        ];
        var selectorEntry;
        for (let k = 0; k < filterButtonSelectors.length; k++) {
            selectorEntry = filterButtonSelectors[k];
            try {
                utils.log(`PPACTIONS: 尝试查找主筛选按钮 (策略: ${selectorEntry.name})`);
                mainFilterButton = selectorEntry.selector();
                if (mainFilterButton) {
                    let details = `Text: "${mainFilterButton.text()}", Desc: "${mainFilterButton.desc()}", ID: ${mainFilterButton.id()}, Class: ${mainFilterButton.className()}, Bounds: ${mainFilterButton.bounds()}, Clickable: ${mainFilterButton.clickable()}, Visible: ${mainFilterButton.visibleToUser()}`;
                    utils.log(`PPACTIONS: 找到主筛选按钮候选 (策略: ${selectorEntry.name})。详情: ${details}`);
                    if (mainFilterButton.visibleToUser() && mainFilterButton.clickable()) {
                        utils.log(`PPACTIONS: 主筛选按钮候选可用，将使用此按钮 (策略: ${selectorEntry.name})。`);
                        break;
                    } else {
                        utils.log(`PPACTIONS: 主筛选按钮候选虽找到但不可见或不可点击 (策略: ${selectorEntry.name})。继续尝试其他策略...`);
                        mainFilterButton = null;
                    }
                } else {
                    utils.log(`PPACTIONS: 未通过策略 "${selectorEntry.name}" 找到主筛选按钮。`);
                }
            } catch (e) {
                utils.log(`PPACTIONS: 查找主筛选按钮 (策略: ${selectorEntry.name}) 时发生异常: ${e.toString()}`);
            }
        }

        if (mainFilterButton) {
            utils.log(`PPACTIONS: 点击主筛选按钮: ${mainFilterButton.text() || mainFilterButton.desc()}`);

            let clickResult = false;
            try {
                utils.log("PPACTIONS: 尝试点击筛选按钮...");
                clickResult = mainFilterButton.click();
                utils.log("PPACTIONS: 筛选按钮click()调用完成，结果: " + clickResult);
            } catch (e) {
                utils.log("PPACTIONS: 筛选按钮点击时发生异常: " + e.toString());
                clickResult = false;
            }

            if (clickResult) {
                utils.log("PPACTIONS: 筛选按钮点击成功，等待面板打开...");

                // 避免使用sleep，直接进行面板验证
                utils.log("PPACTIONS: 跳过等待，直接开始验证面板状态...");
                utils.log("PPACTIONS: Validating if filter panel is open after clicking main filter button...");

                // 首先尝试定位筛选面板容器，然后在容器内查找元素
                let foundPanelSortTrigger = null;
                let foundPanelPublishTimeOption = null;
                let foundPanelLocationOption = null;
                let filterPanel = null;

                try {
                    utils.log("PPACTIONS: 尝试定位筛选面板容器...");
                    // 尝试多种可能的筛选面板容器ID（用户确认的正确ID在第一位）
                    const panelSelectors = [
                        "com.xingin.xhs:id/bh5",  // 用户确认的筛选面板容器ID
                        "com.xingin.xhs:id/ht9",
                        "com.xingin.xhs:id/chh",
                        "com.xingin.xhs:id/b5e",
                        "com.xingin.xhs:id/popup_container"
                    ];

                    for (let i = 0; i < panelSelectors.length; i++) {
                        utils.log("PPACTIONS: 尝试查找面板容器: " + panelSelectors[i]);
                        try {
                            filterPanel = id(panelSelectors[i]).findOne(500);
                            if (filterPanel) {
                                utils.log("PPACTIONS: 找到筛选面板容器: " + panelSelectors[i]);
                                break;
                            } else {
                                utils.log("PPACTIONS: 未找到面板容器: " + panelSelectors[i]);
                            }
                        } catch (e) {
                            utils.log("PPACTIONS: 查找面板容器时出错: " + panelSelectors[i] + " - " + e.toString());
                        }
                    }

                    // 如果还是没找到，尝试通用方法查找筛选面板
                    if (!filterPanel) {
                        utils.log("PPACTIONS: 所有预定义容器都未找到，尝试通用方法查找筛选面板...");

                        // 方法1：查找包含"发布时间"或"位置距离"的任何元素的父容器
                        try {
                            utils.log("PPACTIONS: 方法1 - 通过特征文本查找面板...");
                            const featureTextElements = className("android.widget.TextView").find();
                            for (let i = 0; i < featureTextElements.length; i++) {
                                const featureText = featureTextElements[i].text();
                                if (featureText === "发布时间" || featureText === "位置距离" || featureText === "综合" || featureText === "最新") {
                                    utils.log("PPACTIONS: 找到特征文本: " + featureText);
                                    let parent = featureTextElements[i].parent();
                                    let attempts = 0;
                                    while (parent && attempts < 5) {
                                        // 检查父容器是否包含多个筛选选项
                                        const childTexts = parent.find(className("android.widget.TextView"));
                                        let hasMultipleOptions = false;
                                        let optionCount = 0;
                                        for (let j = 0; j < childTexts.length; j++) {
                                            const childText = childTexts[j].text();
                                            if (childText === "发布时间" || childText === "位置距离" ||
                                                childText === "综合" || childText === "最新") {
                                                optionCount++;
                                            }
                                        }
                                        if (optionCount >= 2) {
                                            utils.log("PPACTIONS: 找到包含多个筛选选项的容器，选项数: " + optionCount);
                                            filterPanel = parent;
                                            break;
                                        }
                                        parent = parent.parent();
                                        attempts++;
                                    }
                                    if (filterPanel) break;
                                }
                            }
                        } catch (e) {
                            utils.log("PPACTIONS: 方法1查找出错: " + e.toString());
                        }

                        // 方法2：如果方法1失败，直接使用全局查找
                        if (!filterPanel) {
                            utils.log("PPACTIONS: 方法2 - 使用全局查找，跳过容器定位...");
                            // 设置一个虚拟的filterPanel，让后续代码使用全局查找
                            filterPanel = { isGlobal: true };
                        }
                    }

                    // 如果没有找到特定容器，尝试通过特征元素定位
                    if (!filterPanel) {
                        utils.log("PPACTIONS: 未找到特定面板容器，尝试通过特征元素定位...");
                        // 查找包含筛选选项的任何容器
                        const allElements = className("android.widget.TextView").find();
                        for (let i = 0; i < allElements.length; i++) {
                            const element = allElements[i];
                            const elementText = element.text();
                            if (elementText === "发布时间" || elementText === "位置距离" || elementText === "综合" || elementText === "最新") {
                                // 找到特征元素，获取其父容器
                                filterPanel = element.parent();
                                if (filterPanel) {
                                    utils.log("PPACTIONS: 通过特征元素找到筛选面板容器");
                                    break;
                                }
                            }
                        }
                    }
                } catch (e) {
                    utils.log("PPACTIONS: 定位筛选面板容器出错: " + e.toString());
                }

                if (filterPanel) {
                    if (filterPanel.isGlobal) {
                        utils.log("PPACTIONS: 使用全局查找方式查找筛选选项...");
                    } else {
                        utils.log("PPACTIONS: 在筛选面板容器内查找选项元素...");
                    }

                    try {
                        utils.log("PPACTIONS: 查找排序选项元素...");
                        if (filterPanel.isGlobal) {
                            // 全局查找
                            const allTextElements = className("android.widget.TextView").find();
                            for (let i = 0; i < allTextElements.length; i++) {
                                if (allTextElements[i].text() === sortOptionsText[0]) {
                                    foundPanelSortTrigger = allTextElements[i];
                                    utils.log("PPACTIONS: 全局找到排序选项元素: " + sortOptionsText[0]);
                                    break;
                                }
                            }
                        } else {
                            // 容器内查找
                            const sortElements = filterPanel.find(className("android.widget.TextView"));
                            for (let i = 0; i < sortElements.length; i++) {
                                if (sortElements[i].text() === sortOptionsText[0]) {
                                    foundPanelSortTrigger = sortElements[i];
                                    utils.log("PPACTIONS: 在面板内找到排序选项元素: " + sortOptionsText[0]);
                                    break;
                                }
                            }
                        }
                    } catch (e) {
                        utils.log("PPACTIONS: 查找排序选项元素出错: " + e.toString());
                    }

                    try {
                        utils.log("PPACTIONS: 查找发布时间选项元素...");
                        if (filterPanel.isGlobal) {
                            // 全局查找
                            const allTimeElements = className("android.widget.TextView").find();
                            for (let i = 0; i < allTimeElements.length; i++) {
                                if (allTimeElements[i].text() === "发布时间") {
                                    foundPanelPublishTimeOption = allTimeElements[i];
                                    utils.log("PPACTIONS: 全局找到发布时间选项元素");
                                    break;
                                }
                            }
                        } else {
                            // 容器内查找
                            const timeElements = filterPanel.find(className("android.widget.TextView"));
                            for (let i = 0; i < timeElements.length; i++) {
                                if (timeElements[i].text() === "发布时间") {
                                    foundPanelPublishTimeOption = timeElements[i];
                                    utils.log("PPACTIONS: 在面板内找到发布时间选项元素");
                                    break;
                                }
                            }
                        }
                    } catch (e) {
                        utils.log("PPACTIONS: 查找发布时间选项元素出错: " + e.toString());
                    }

                    try {
                        utils.log("PPACTIONS: 查找位置距离选项元素...");
                        if (filterPanel.isGlobal) {
                            // 全局查找
                            const allLocationElements = className("android.widget.TextView").find();
                            for (let i = 0; i < allLocationElements.length; i++) {
                                if (allLocationElements[i].text() === "位置距离") {
                                    foundPanelLocationOption = allLocationElements[i];
                                    utils.log("PPACTIONS: 全局找到位置距离选项元素");
                                    break;
                                }
                            }
                        } else {
                            // 容器内查找
                            const locationElements = filterPanel.find(className("android.widget.TextView"));
                            for (let i = 0; i < locationElements.length; i++) {
                                if (locationElements[i].text() === "位置距离") {
                                    foundPanelLocationOption = locationElements[i];
                                    utils.log("PPACTIONS: 在面板内找到位置距离选项元素");
                                    break;
                                }
                            }
                        }
                    } catch (e) {
                        utils.log("PPACTIONS: 查找位置距离选项元素出错: " + e.toString());
                    }
                } else {
                    utils.log("PPACTIONS: 未找到筛选面板容器，使用全局查找...");

                    try {
                        utils.log("PPACTIONS: 全局查找排序选项元素...");
                        const allTextViews = className("android.widget.TextView").find();
                        for (let i = 0; i < allTextViews.length; i++) {
                            const globalElementText = allTextViews[i].text();
                            if (globalElementText === sortOptionsText[0]) {
                                foundPanelSortTrigger = allTextViews[i];
                                utils.log("PPACTIONS: 全局找到排序选项元素: " + sortOptionsText[0]);
                                break;
                            } else if (globalElementText === "发布时间") {
                                foundPanelPublishTimeOption = allTextViews[i];
                                utils.log("PPACTIONS: 全局找到发布时间选项元素");
                            } else if (globalElementText === "位置距离") {
                                foundPanelLocationOption = allTextViews[i];
                                utils.log("PPACTIONS: 全局找到位置距离选项元素");
                            }
                        }
                    } catch (e) {
                        utils.log("PPACTIONS: 全局查找选项元素出错: " + e.toString());
                    }
                }

                utils.log(`PPACTIONS: Panel validation details (selectors include id="com.xingin.xhs:id/e1u", checking for visibility):`);
                utils.log(`  - Sort trigger ('${sortOptionsText[0]}'): ${foundPanelSortTrigger ? 'found' : 'not found'}`);
                utils.log(`  - Publish time option ('发布时间'): ${foundPanelPublishTimeOption ? 'found' : 'not found'}`);
                utils.log(`  - Location option ('位置距离'): ${foundPanelLocationOption ? 'found' : 'not found'}`);

                if (foundPanelSortTrigger || foundPanelPublishTimeOption || foundPanelLocationOption) {
                    utils.log("PPACTIONS: Filter panel confirmed open: At least one characteristic element is visible.");
                    filterPanelOpened = true;
                } else {
                    utils.log("WARN: PPACTIONS: Clicked main filter button, but panel validation FAILED.");
                    if (id("com.xingin.xhs:id/e1u").exists()) {
                        utils.log("WARN: PPACTIONS: Fallback activation: Assuming panel is open based on weaker evidence.");
                        filterPanelOpened = true;
                    } else {
                        utils.log("WARN: PPACTIONS: Fallback check: Panel likely did not open.");
                    }
                }
            } else {
                utils.log("ERROR: PPACTIONS: 点击主筛选按钮失败 (click() 返回 false)。");
            }
        } else {
            utils.log("WARN: PPACTIONS: 未找到可见且可点击的主筛选按钮。无法应用任何面板内筛选。");
        }

        if (filterPanelOpened) {
            if (sortByOption > 0 && sortByOption < sortOptionsText.length) {
                utils.log(`PPACTIONS: 在筛选面板内开始应用排序依据: ${sortOptionsText[sortByOption]}`);
                const targetSortOptionText = sortOptionsText[sortByOption];
                utils.log(`PPACTIONS: 尝试直接查找并点击目标排序选项 "${targetSortOptionText}"`);
                try {
                    utils.log(`PPACTIONS: 尝试定位排序选项文本元素: id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text("${targetSortOptionText}")`);
                    let targetSortOptionElement = null;
                    try {
                        targetSortOptionElement = id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text(targetSortOptionText).findOne(2000);
                    } catch (e) {
                        utils.log(`PPACTIONS: 查找排序选项元素时出错: ${e.toString()}`);
                    }

                    if (targetSortOptionElement) {
                        utils.log(`PPACTIONS: 已定位排序选项 "${targetSortOptionText}"，调用 tryClickInPanel。`);
                        if (tryClickInPanel(targetSortOptionElement, `排序选项 "${targetSortOptionText}"`)) {
                            utils.log(`PPACTIONS: 成功处理点击排序选项 "${targetSortOptionText}".`);
                            sleep(2000); // 明确延时1秒
                        } else {
                            utils.log(`WARN: PPACTIONS: 未能成功点击排序选项 "${targetSortOptionText}" 通过任何方法.`);
                        }
                    } else {
                        utils.log(`WARN: PPACTIONS: 排序选项文本 "${targetSortOptionText}" (id="com.xingin.xhs:id/e1u") 未找到.`);
                    }
                } catch (ex) {
                    utils.log(`ERROR: PPACTIONS: 处理排序选项 "${targetSortOptionText}" 时发生异常: ${ex.toString()}`);
                }
            } else if (sortByOption !== 0) {
                utils.log(`WARN: PPACTIONS: 无效的排序选项索引: ${sortByOption}。跳过排序。`);
            }
            // Add delay if sort options were processed, before moving to publish time options
            if (sortByOption > 0 && sortByOption < sortOptionsText.length) {
                utils.log(`PPACTIONS: Delaying after sort option processing (1s).`);
                sleep(2000); // 明确延时1秒
            }

            if (publishTimeOption > 0 && publishTimeOption < publishTimeOptionsText.length) {
                const targetPublishTimeText = publishTimeOptionsText[publishTimeOption];
                utils.log(`PPACTIONS: 尝试应用发布时间: "${targetPublishTimeText}"`);
                try {
                    utils.log(`PPACTIONS: 尝试定位发布时间选项文本元素: id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text("${targetPublishTimeText}")`);
                    let targetPublishTimeElement = id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text(targetPublishTimeText).findOne(2000);
                    if (targetPublishTimeElement) {
                        utils.log(`PPACTIONS: 已定位发布时间选项 "${targetPublishTimeText}"，调用 tryClickInPanel。`);
                        if (tryClickInPanel(targetPublishTimeElement, `发布时间选项 "${targetPublishTimeText}"`)) {
                            utils.log(`PPACTIONS: 成功处理点击发布时间选项 "${targetPublishTimeText}".`);
                            sleep(2000); // 明确延时1秒
                        } else {
                            utils.log(`WARN: PPACTIONS: 未能成功点击发布时间选项 "${targetPublishTimeText}" 通过任何方法.`);
                        }
                    } else {
                        utils.log(`WARN: PPACTIONS: 发布时间选项文本 "${targetPublishTimeText}" (id="com.xingin.xhs:id/e1u") 未找到.`);
                    }
                } catch (e) {
                    utils.log(`ERROR: PPACTIONS: 处理发布时间选项 "${targetPublishTimeText}" 时发生异常: ${e.toString()}`);
                }
            } else if (publishTimeOption === 0) {
                utils.log(`PPACTIONS: 发布时间选项为 "不限" (索引 ${publishTimeOption})，跳过点击。`);
            } else {
                utils.log(`WARN: PPACTIONS: 无效的发布时间选项索引: ${publishTimeOption}，跳过。`);
            }
            // Add delay if publish time options were processed, before moving to location options
            if (publishTimeOption > 0 && publishTimeOption < publishTimeOptionsText.length) {
                utils.log(`PPACTIONS: Delaying after publish time option processing (1s).`);
                sleep(2000); // 明确延时1秒
            }

            if (locationDistanceOption > 0 && locationDistanceOption < locationOptionsText.length) {
                const targetLocationText = locationOptionsText[locationDistanceOption];
                utils.log(`PPACTIONS: 尝试应用位置距离: "${targetLocationText}"`);
                try {
                    utils.log(`PPACTIONS: 尝试定位位置距离选项文本元素: id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text("${targetLocationText}")`);
                    let targetLocationElement = id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text(targetLocationText).findOne(2000);
                    if (targetLocationElement) {
                        utils.log(`PPACTIONS: 已定位位置距离选项 "${targetLocationText}"，调用 tryClickInPanel。`);
                        if (tryClickInPanel(targetLocationElement, `位置距离选项 "${targetLocationText}"`)) {
                            utils.log(`PPACTIONS: 成功处理点击位置距离选项 "${targetLocationText}".`);
                            sleep(2000); // 明确延时1秒
                        } else {
                            utils.log(`WARN: PPACTIONS: 未能成功点击位置距离选项 "${targetLocationText}" 通过任何方法.`);
                        }
                    } else {
                        utils.log(`WARN: PPACTIONS: 位置距离选项文本 "${targetLocationText}" (id="com.xingin.xhs:id/e1u") 未找到.`);
                    }
                } catch (e) {
                    utils.log(`ERROR: PPACTIONS: 处理位置距离选项 "${targetLocationText}" 时发生异常: ${e.toString()}`);
                }
            } else if (locationDistanceOption === 0) {
                utils.log(`PPACTIONS: 位置距离选项为 "不限" (索引 ${locationDistanceOption})，跳过点击。`);
            } else {
                utils.log(`WARN: PPACTIONS: 无效的位置距离选项索引: ${locationDistanceOption}，跳过。`);
            }

            utils.log("PPACTIONS: 所有筛选选项已应用（或尝试应用）。现在关闭筛选面板。");
            let collapseTextElement = id("com.xingin.xhs:id/e1u").text("收起").findOne(1000);
            let collapseClicked = false;

            if (collapseTextElement) {
                utils.log("PPACTIONS: 已定位“收起”文本元素，调用 tryClickInPanel。");
                if (tryClickInPanel(collapseTextElement, '“收起”按钮')) {
                    utils.log("PPACTIONS: 成功处理点击“收起”按钮。");
                    sleep(2000);
                    collapseClicked = true;
                } else {
                    utils.log("WARN: PPACTIONS: 未能成功点击“收起”按钮通过任何方法。");
                }
            } else {
                utils.log("PPACTIONS: “收起”文本元素 (id=\"e1u\" and text=\"收起\") 未找到。");
            }

            if (!collapseClicked) {
                utils.log("PPACTIONS: “收起”按钮交互失败或未找到，将使用 back() 关闭筛选面板。");
                back();
                sleep(2000);
            }
            if (!collapseClicked) {
                try {
                    utils.log("PPACTIONS: 尝试额外方法 - 点击筛选面板外的区域");
                    click(device.width / 2, 100);
                    sleep(1000);
                    if (!id("com.xingin.xhs:id/e1u").exists()) {
                        utils.log("PPACTIONS: 点击屏幕顶部区域后，筛选面板似乎已关闭");
                        collapseClicked = true;
                    } else {
                        utils.log("PPACTIONS: 点击屏幕顶部区域后，筛选面板仍然存在");
                        back();
                        sleep(1000);
                    }
                } catch (e) {
                    utils.log("PPACTIONS: 额外方法点击屏幕区域时出错: " + e);
                }
            }
        } else {
            utils.log("WARN: PPACTIONS: 主筛选面板未能成功打开或确认打开，跳过所有面板内筛选操作。");
        }
    } else {
        utils.log("PPACTIONS: 无需应用排序、发布时间或位置筛选。");
    }
    utils.log("PPACTIONS: 笔记搜索和筛选操作流程结束。");
    return { success: true, message: "搜索和筛选流程已执行" };
}

// isSearchResultsPage (已修正redeclaration错误，并使用新的SELECTORS定义)
function isSearchResultsPage() {
    utils.log("PPACTIONS_DEBUG: isSearchResultsPage: Function called.");
    const indicatorConfig = SELECTORS.SEARCH_RESULTS_PAGE_INDICATOR;
    let found = false;
    let element = null;

    if (!indicatorConfig) {
        utils.log("ERROR: PPACTIONS: isSearchResultsPage: SEARCH_RESULTS_PAGE_INDICATOR is not defined in SELECTORS.");
        return false;
    }

    if (indicatorConfig.id) {
        utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: '${indicatorConfig.id}'`);
        let queryById = id(indicatorConfig.id);
        if (queryById.exists()) {
            let finalQuery = queryById;
            if (indicatorConfig.text) {
                utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: ID exists, now chaining text: '${indicatorConfig.text}'`);
                finalQuery = finalQuery.text(indicatorConfig.text);
            }
            if (indicatorConfig.desc) {
                utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: Also chaining desc: '${indicatorConfig.desc}'`);
                finalQuery = finalQuery.desc(indicatorConfig.desc);
            }
            element = finalQuery.visibleToUser().findOne(500);
            if (element) {
                utils.log(`PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: ${element.bounds()}`);
                found = true;
            } else {
                utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: Element with ID '${indicatorConfig.id}' (and chained text/desc) not found or not visible.`);
            }
        } else {
            utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: Element with ID '${indicatorConfig.id}' does not exist initially.`);
        }
    }

    if (!found && indicatorConfig.desc) {
        utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: ID attempt failed or ID not configured. Attempting with DESC: '${indicatorConfig.desc}'`);
        let queryByDesc = desc(indicatorConfig.desc);
        if (queryByDesc.exists()) {
            let finalQuery = queryByDesc;
            if (indicatorConfig.text) {
                utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: DESC exists, now chaining text: '${indicatorConfig.text}'`);
                finalQuery = finalQuery.text(indicatorConfig.text);
            }
            element = finalQuery.visibleToUser().findOne(500);
            if (element) {
                utils.log(`PPACTIONS_SUCCESS: isSearchResultsPage: Found with DESC (and optional text). Element: ${element.bounds()}`);
                found = true;
            } else {
                utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: Element with DESC '${indicatorConfig.desc}' (and optional text) not found or not visible.`);
            }
        } else {
            utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: Element with DESC '${indicatorConfig.desc}' does not exist initially.`);
        }
    }

    if (!found && indicatorConfig.text && !indicatorConfig.id && !indicatorConfig.desc) {
        utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: ID/DESC attempts failed or not configured. Attempting with TEXT only: '${indicatorConfig.text}'`);
        let queryByText = text(indicatorConfig.text);
        if (queryByText.exists()) {
            element = queryByText.visibleToUser().findOne(500);
            if (element) {
                utils.log(`PPACTIONS_SUCCESS: isSearchResultsPage: Found with TEXT only. Element: ${element.bounds()}`);
                found = true;
            } else {
                utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: Element with TEXT '${indicatorConfig.text}' not found or not visible.`);
            }
        } else {
            utils.log(`PPACTIONS_DEBUG: isSearchResultsPage: Element with TEXT '${indicatorConfig.text}' does not exist initially.`);
        }
    }

    utils.log(`PPACTIONS_RESULT: isSearchResultsPage: Final check result: ${found}`);
    return found;
}

function isNoteDetailPage() {
    utils.log("PPACTIONS: Checking if current page is note detail page.");

    // 检查图文笔记详情页
    const indicator = SELECTORS.NOTE_DETAIL_PAGE_INDICATOR;
    let imageTextPageFound = false;
    if (indicator.id && id(indicator.id).exists()) {
        imageTextPageFound = id(indicator.id).visibleToUser().findOne(500) !== null;
    } else if (indicator.desc && desc(indicator.desc).exists()) {
        imageTextPageFound = desc(indicator.desc).visibleToUser().findOne(500) !== null;
    }

    // 检查图文笔记的评论图标
    const commentIcon = SELECTORS.NOTE_DETAIL_COMMENT_ICON;
    let imageTextCommentIconFound = false;
    if (commentIcon.id && id(commentIcon.id).exists()) {
        imageTextCommentIconFound = id(commentIcon.id).visibleToUser().findOne(500) != null;
    } else if (commentIcon.descContains && descContains(commentIcon.descContains).exists()) {
        imageTextCommentIconFound = descContains(commentIcon.descContains).visibleToUser().findOne(500) != null;
    }

    if (!imageTextCommentIconFound) {
        imageTextCommentIconFound = textContains("评论").visibleToUser().findOne(500) != null ||
            descContains("评论").visibleToUser().findOne(500) != null;
    }

    // 检查笔记详情页（使用 gn_ 元素判断类型）
    let videoPageFound = false;
    let imageTextPageFoundByGn = false;

    try {
        // 使用 gn_ 元素来判断：存在 = 图文笔记，不存在 = 视频笔记
        const gnElement = id("com.xingin.xhs:id/gn_").findOne(1000);
        if (gnElement) {
            imageTextPageFoundByGn = true;
            utils.log("PPACTIONS: 找到 gn_ 元素 - 确认为图文笔记详情页");
        } else {
            // 进一步验证是否真的是视频笔记详情页
            const videoCommentContainer = id("com.xingin.xhs:id/c9y").findOne(1000);
            if (videoCommentContainer) {
                videoPageFound = true;
                utils.log("PPACTIONS: 未找到 gn_ 元素且找到 c9y 容器 - 确认为视频笔记详情页");
            }
        }
    } catch (e) {
        // 忽略检测错误
    }

    const isImageTextPage = imageTextPageFound || imageTextCommentIconFound || imageTextPageFoundByGn;
    const isVideoPage = videoPageFound;

    utils.log(`PPACTIONS: Note detail page check - 图文笔记: (indicator: ${imageTextPageFound}, comment: ${imageTextCommentIconFound}, gn_: ${imageTextPageFoundByGn}) = ${isImageTextPage}`);
    utils.log(`PPACTIONS: Note detail page check - 视频笔记: ${isVideoPage}`);
    utils.log(`PPACTIONS: Note detail page check - 最终结果: ${isImageTextPage || isVideoPage}`);

    return isImageTextPage || isVideoPage;
}

function backToPreviousPage(noteType) {
    utils.log("PPACTIONS: 尝试返回上一页。");
    // 改为同步操作，直接返回boolean结果
    try {
        // 使用传入的笔记类型，如果没有传入则进行检测
        if (noteType) {
            utils.log(`PPACTIONS: 使用传入的笔记类型: ${noteType}`);

            if (noteType === noteTypes.NOTE_TYPES.IMAGE_TEXT) {
                utils.log("PPACTIONS: 根据传入类型，使用图文笔记返回逻辑。");
                return handleImageTextNoteBackSync();
            } else if (noteType === noteTypes.NOTE_TYPES.VIDEO) {
                utils.log("PPACTIONS: 根据传入类型，使用视频笔记返回逻辑。");
                return handleVideoNoteBackSync();
            } else {
                utils.log("PPACTIONS: 传入类型未知，默认使用图文笔记返回逻辑。");
                return handleImageTextNoteBackSync();
            }
        } else {
            // 兼容性：如果没有传入笔记类型，则进行检测（用于其他调用场景）
            utils.log("PPACTIONS: 未传入笔记类型，进行实时检测...");

            const gnElement = id("com.xingin.xhs:id/gn_").findOne(1000);
            if (gnElement) {
                utils.log("PPACTIONS: 检测到图文笔记，使用图文笔记返回逻辑。");
                return handleImageTextNoteBackSync();
            } else {
                utils.log("PPACTIONS: 未检测到图文笔记标识，使用视频笔记返回逻辑。");
                return handleVideoNoteBackSync();
            }
        }
    } catch (e) {
        utils.log(`ERROR: PPACTIONS: 返回上一页时发生错误: ${e.toString()}`);
        return false;
    }
}

// 处理图文笔记返回 - 同步版本
function handleImageTextNoteBackSync() {
    try {
        // 图文笔记专用返回逻辑
        let clicked = false;

        // 方法1: 使用标准返回按钮
        const standardBackButton = id("com.xingin.xhs:id/a2q").desc("返回").findOne(3000);
        if (standardBackButton && standardBackButton.click()) {
            utils.log("PPACTIONS: 图文笔记 - 标准返回按钮点击成功");
            sleep(1500);
            return true;
        }

        // 方法2: 使用全局返回键
        utils.log("PPACTIONS: 图文笔记 - 标准返回按钮失败，尝试全局返回键");
        back();
        sleep(1500);
        return true;

    } catch (e) {
        utils.log(`ERROR: PPACTIONS: 图文笔记返回时发生错误: ${e.toString()}`);
        return false;
    }
}

// 处理图文笔记返回 - 原异步版本（保留兼容性）
function handleImageTextNoteBack(resolve) {
    try {
        // 图文笔记专用返回逻辑
        let clicked = false;

        // 方法1: 等待页面稳定后查找标准返回按钮
        utils.log("PPACTIONS: 等待页面稳定，然后查找图文笔记标准返回按钮...");
        sleep(1000); // 等待页面稳定

        // 进行详细的元素查找测试，就像测试文件一样
        utils.log("PPACTIONS: === 开始详细元素查找测试 ===");

        // 测试1: 标准查找（ID + desc）
        const test1 = id("com.xingin.xhs:id/a2q").desc("返回").findOne(1000);
        utils.log(`PPACTIONS: 测试1 (ID+desc): ${test1 ? '找到' : '未找到'}`);
        if (test1) {
            utils.log(`PPACTIONS: 测试1详情 - ID: ${test1.id()}, 描述: ${test1.desc()}, 边界: ${test1.bounds()}`);
        }

        // 测试2: 仅ID查找
        const test2 = id("com.xingin.xhs:id/a2q").findOne(1000);
        utils.log(`PPACTIONS: 测试2 (仅ID): ${test2 ? '找到' : '未找到'}`);
        if (test2) {
            utils.log(`PPACTIONS: 测试2详情 - ID: ${test2.id()}, 描述: ${test2.desc()}, 边界: ${test2.bounds()}`);
        }

        // 测试3: 仅描述查找
        const test3 = desc("返回").findOne(1000);
        utils.log(`PPACTIONS: 测试3 (仅desc): ${test3 ? '找到' : '未找到'}`);
        if (test3) {
            utils.log(`PPACTIONS: 测试3详情 - ID: ${test3.id()}, 描述: ${test3.desc()}, 边界: ${test3.bounds()}`);
        }

        utils.log("PPACTIONS: === 详细元素查找测试完成 ===");

        // 使用测试结果中最可靠的方法
        let standardBackButton = test1 || test2 || test3;
        if (!standardBackButton) {
            utils.log("PPACTIONS: 所有测试方法都失败，尝试更长时间查找...");
            standardBackButton = id("com.xingin.xhs:id/a2q").desc("返回").findOne(3000);
        }
        if (standardBackButton) {
            utils.log("PPACTIONS: 找到图文笔记标准返回按钮，检查元素状态...");
            utils.log(`PPACTIONS: 按钮边界: ${standardBackButton.bounds()}`);
            utils.log(`PPACTIONS: 按钮可见: ${standardBackButton.visibleToUser()}`);
            utils.log(`PPACTIONS: 按钮可点击: ${standardBackButton.clickable()}`);

            if (standardBackButton.visibleToUser() && standardBackButton.clickable()) {
                utils.log("PPACTIONS: 按钮状态正常，尝试点击...");
                clicked = standardBackButton.click();
                if (clicked) {
                    utils.log("PPACTIONS: 图文笔记标准返回按钮点击成功。");
                    sleep(2000);
                    resolve(true);
                    return;
                } else {
                    utils.log("PPACTIONS: 图文笔记标准返回按钮点击失败（click()返回false）。");
                }
            } else {
                utils.log(`PPACTIONS: 按钮状态异常 - 可见:${standardBackButton.visibleToUser()}, 可点击:${standardBackButton.clickable()}`);
            }
        } else {
            utils.log("PPACTIONS: 未找到图文笔记标准返回按钮。");
        }

        // 方法2: 使用全局返回键
        if (!clicked) {
            utils.log("PPACTIONS: 图文笔记标准返回按钮未找到或点击失败，尝试全局返回键。");
            try {
                clicked = back();
                if (clicked) {
                    utils.log("PPACTIONS: 图文笔记全局返回键执行成功。");
                    sleep(2000);
                    resolve(true);
                    return;
                }
            } catch (e) {
                utils.log(`PPACTIONS: 图文笔记全局返回键执行失败: ${e}`);
            }
        }

        // 方法3: 备用返回按钮查找
        if (!clicked) {
            utils.log("PPACTIONS: 图文笔记尝试备用返回按钮查找方法。");
            const backupButtons = [
                () => desc("返回").clickable(true).findOne(1000),
                () => idContains("back").clickable(true).findOne(1000),
                () => textContains("返回").clickable(true).findOne(1000)
            ];

            for (let i = 0; i < backupButtons.length; i++) {
                try {
                    const button = backupButtons[i]();
                    if (button) {
                        utils.log(`PPACTIONS: 图文笔记找到备用返回按钮 (方法${i + 1})，尝试点击。`);
                        clicked = button.click();
                        if (clicked) {
                            utils.log(`PPACTIONS: 图文笔记备用返回按钮 (方法${i + 1}) 点击成功。`);
                            sleep(2000);
                            resolve(true);
                            return;
                        }
                    }
                } catch (e) {
                    utils.log(`PPACTIONS: 图文笔记备用返回方法${i + 1}失败: ${e}`);
                }
            }
        }

        utils.log("WARN: PPACTIONS: 图文笔记所有返回方法都失败了。");
        resolve(false);
    } catch (e) {
        utils.log(`ERROR: PPACTIONS: 图文笔记返回时发生错误: ${e.toString()}`);
        resolve(false);
    }
}

// 处理视频笔记返回 - 同步版本
function handleVideoNoteBackSync() {
    try {
        // 首先检查是否在视频评论区（需要先关闭评论弹窗）
        const videoCommentCloseButton = id("com.xingin.xhs:id/b5b").findOne(1000);
        if (videoCommentCloseButton) {
            utils.log("PPACTIONS: 检测到视频评论弹窗，先关闭弹窗...");
            videoCommentCloseButton.click();
            sleep(1500);

            // 关闭弹窗后，使用视频笔记专用的返回按钮
            const videoBackButtonAfterClose = id("com.xingin.xhs:id/g0r").desc("返回").findOne(2000);
            if (videoBackButtonAfterClose) {
                utils.log("PPACTIONS: 找到视频笔记返回按钮，点击返回。");
                videoBackButtonAfterClose.click();
                sleep(2000);
                return true;
            } else {
                utils.log("WARN: PPACTIONS: 未找到视频笔记返回按钮，尝试通用返回。");
            }
        }

        // 如果没有评论弹窗，直接尝试视频笔记返回按钮
        const videoBackButtonDirect = id("com.xingin.xhs:id/g0r").desc("返回").findOne(2000);
        if (videoBackButtonDirect) {
            utils.log("PPACTIONS: 找到视频笔记返回按钮，点击返回。");
            videoBackButtonDirect.click();
            sleep(2000);
            return true;
        }

        // 备用方案：使用全局返回键
        utils.log("PPACTIONS: 视频笔记返回按钮未找到，尝试全局返回键。");
        back();
        sleep(2000);
        return true;

    } catch (e) {
        utils.log(`ERROR: PPACTIONS: 视频笔记返回时发生错误: ${e.toString()}`);
        return false;
    }
}

// 处理视频笔记返回 - 原异步版本（保留兼容性）
function handleVideoNoteBack(resolve) {
    try {
        // 首先检查是否在视频评论区（需要先关闭评论弹窗）
        const videoCommentCloseButton = id("com.xingin.xhs:id/b5b").findOne(1000);
        if (videoCommentCloseButton) {
            utils.log("PPACTIONS: 检测到视频评论弹窗，先关闭弹窗...");
            videoCommentCloseButton.click();
            sleep(1500);

            // 关闭弹窗后，使用视频笔记专用的返回按钮
            const videoBackButtonAfterClose = id("com.xingin.xhs:id/g0r").desc("返回").findOne(2000);
            if (videoBackButtonAfterClose) {
                utils.log("PPACTIONS: 找到视频笔记返回按钮，点击返回。");
                videoBackButtonAfterClose.click();
                sleep(2000);
                resolve(true);
                return;
            } else {
                utils.log("WARN: PPACTIONS: 未找到视频笔记返回按钮，尝试通用返回。");
            }
        }

        // 如果没有评论弹窗，直接尝试视频笔记返回按钮
        const videoBackButtonDirect = id("com.xingin.xhs:id/g0r").desc("返回").findOne(2000);
        if (videoBackButtonDirect) {
            utils.log("PPACTIONS: 找到视频笔记返回按钮，点击返回。");
            videoBackButtonDirect.click();
            sleep(2000);
            resolve(true);
            return;
        }

        // 备用方案：使用全局返回键
        utils.log("PPACTIONS: 视频笔记返回按钮未找到，尝试全局返回键。");
        try {
            const clicked = back();
            if (clicked) {
                utils.log("PPACTIONS: 视频笔记全局返回键执行成功。");
                sleep(2000);
                resolve(true);
                return;
            }
        } catch (e) {
            utils.log(`PPACTIONS: 视频笔记全局返回键执行失败: ${e}`);
        }

        utils.log("WARN: PPACTIONS: 视频笔记所有返回方法都失败了。");
        resolve(false);
    } catch (e) {
        utils.log(`ERROR: PPACTIONS: 视频笔记返回时发生错误: ${e.toString()}`);
        resolve(false);
    }
}

function isSearchResultsPageAtBottom() {
    try {
        // 主要检测方法：使用您提供的准确元素ID和文本
        const noMoreContentElement = id("com.xingin.xhs:id/eiz").text("无更多内容").findOne(1000);
        if (noMoreContentElement && noMoreContentElement.visibleToUser()) {
            utils.log("PPACTIONS: 检测到'无更多内容'提示 (ID: com.xingin.xhs:id/eiz)，已到达搜索结果底部。");
            return true;
        }

        // 备用检测方法
        const bottomIndicators = [
            () => text("无更多内容").findOne(500),
            () => text("- 到底了 -").findOne(500),
            () => text("沒有更多了").findOne(500),
            () => text("没有更多内容").findOne(500)
        ];

        for (let i = 0; i < bottomIndicators.length; i++) {
            try {
                const indicator = bottomIndicators[i]();
                if (indicator && indicator.visibleToUser()) {
                    utils.log(`PPACTIONS: 检测到底部提示 (备用方法 ${i + 1}: "${indicator.text()}")，已到达搜索结果底部。`);
                    return true;
                }
            } catch (e) { /*忽略单个检测方法的错误*/ }
        }

        utils.log("PPACTIONS: 未检测到底部提示，页面可能还有更多内容。");
        return false;
    } catch (e) {
        utils.log(`PPACTIONS: 检查搜索结果页面底部时出错: ${e}`);
        return false;
    }
}

function scrollSearchResultsPage() {
    // 改为同步操作，直接返回boolean结果
    try {
        utils.log("PPACTIONS: 在搜索结果页面下滚加载更多笔记...");
        swipe(device.width / 2, device.height * 0.75, device.width / 2, device.height * 0.25, 800);
        sleep(2500);
        utils.log("PPACTIONS: 下滚完成，等待新内容加载...");
        return true;
    } catch (e) {
        utils.log(`PPACTIONS: 搜索结果页面下滚时出错: ${e}`);
        return false;
    }
}

// 笔记类型相关功能已移至 xhs_note_types.js 模块

// 笔记提取功能 - 直接实现，不依赖外部模块
function extractVisibleNotes_AdaptedOfficial() {
    utils.log("PPACTIONS: 开始提取当前屏幕可见的笔记...");
    let notesOnScreen = [];

    // 选择器定义
    const NOTE_CARD_ITEM_ID = "com.xingin.xhs:id/hpx";
    const NOTE_CARD_TITLE_ID = "com.xingin.xhs:id/g_q";
    const NOTE_CARD_USER_NICKNAME_ID = "com.xingin.xhs:id/zb";
    const NOTE_CARD_COMMENT_COUNT_ID = "com.xingin.xhs:id/dyq"; // 评论数元素ID

    const containers = id(NOTE_CARD_ITEM_ID).find();

    if (containers.empty()) {
        utils.log("PPACTIONS: 当前屏幕未找到笔记容器");
        return notesOnScreen;
    }

    utils.log("PPACTIONS: 找到 " + containers.size() + " 个笔记容器");

    for (let i = 0; i < containers.size(); i++) {
        let container = containers.get(i);
        utils.log("PPACTIONS: 处理容器 " + (i + 1) + "/" + containers.size());

        if (!container || typeof container.bounds !== 'function') {
            utils.log("PPACTIONS: 无效的容器对象，跳过");
            continue;
        }

        // 检查是否为直播笔记（跳过）
        try {
            const liveElement = container.findOne(id("com.xingin.xhs:id/ikj").text("直播中"));
            if (liveElement && liveElement.visibleToUser()) {
                utils.log("PPACTIONS: 跳过直播笔记 (容器 " + (i + 1) + ")");
                continue;
            }
        } catch (e) {
            // 忽略直播检测错误
        }

        // 提取标题、作者和评论数信息 - 使用立即执行函数确保变量作用域独立
        (function (containerIndex, currentContainer) {
            let titleElement = currentContainer.findOne(id(NOTE_CARD_TITLE_ID));
            let authorElement = currentContainer.findOne(id(NOTE_CARD_USER_NICKNAME_ID));
            let commentCountElement = currentContainer.findOne(id(NOTE_CARD_COMMENT_COUNT_ID));

            // 创建独立的变量副本
            var currentTitle = titleElement ? (titleElement.text() || "").replace(/\n/g, ' ').trim() : "[No Title]";
            var currentAuthor = authorElement ? (authorElement.text() || "").replace(/\n/g, ' ').trim() : "[No Author]";

            // 提取评论数并转换为数字
            var currentCommentCount = 0;
            if (commentCountElement) {
                var commentCountText = (commentCountElement.text() || "").trim();
                // 解析评论数，可能包含"万"等单位
                if (commentCountText) {
                    if (commentCountText.includes('万')) {
                        var numPart = parseFloat(commentCountText.replace('万', ''));
                        currentCommentCount = Math.floor(numPart * 10000);
                    } else if (commentCountText.includes('k') || commentCountText.includes('K')) {
                        var numPart = parseFloat(commentCountText.replace(/[kK]/g, ''));
                        currentCommentCount = Math.floor(numPart * 1000);
                    } else {
                        currentCommentCount = parseInt(commentCountText) || 0;
                    }
                }
            }

            utils.log("PPACTIONS: 标题: \"" + currentTitle.substring(0, 30) + "...\"");
            utils.log("PPACTIONS: 作者: \"" + currentAuthor + "\"");
            utils.log("PPACTIONS: 评论数: " + currentCommentCount);

            if (currentTitle !== "[No Title]" || currentAuthor !== "[No Author]") {
                // 创建独立的signature
                var currentSignature = currentAuthor + "::" + currentTitle;

                // 创建笔记对象 - 使用独立的变量
                var noteObject = {
                    title: String(currentTitle), // 强制创建字符串副本
                    author: String(currentAuthor), // 强制创建字符串副本
                    signature: String(currentSignature), // 强制创建字符串副本
                    commentCount: currentCommentCount, // 评论数
                    noteType: noteTypes.NOTE_TYPES.UNKNOWN, // 在搜索页面暂不检测类型
                    clickableElement: currentContainer,
                    rawContainerInfo: {
                        boundsTop: currentContainer.bounds().top,
                        indexInParent: currentContainer.indexInParent(),
                        drawingOrder: currentContainer.drawingOrder(),
                        id: currentContainer.id(),
                        className: currentContainer.className()
                    }
                };

                // 添加调试日志
                utils.log("PPACTIONS: 创建笔记对象 - 标题: \"" + noteObject.title + "\", 作者: \"" + noteObject.author + "\", 评论数: " + noteObject.commentCount + ", signature: \"" + noteObject.signature + "\"");

                notesOnScreen.push(noteObject);
            } else {
                utils.log("PPACTIONS: 容器 " + (containerIndex + 1) + " 未能提取到标题或作者信息");
            }
        })(i, container);
    }

    utils.log("PPACTIONS: 提取完成，找到 " + notesOnScreen.length + " 个有效笔记");

    // 返回前再次验证数组内容
    utils.log("PPACTIONS: 返回前验证数组内容:");
    for (let j = 0; j < notesOnScreen.length; j++) {
        var note = notesOnScreen[j];
        utils.log("PPACTIONS: 数组[" + j + "] - 标题: \"" + note.title + "\", 作者: \"" + note.author + "\", 评论数: " + note.commentCount + ", signature: \"" + note.signature + "\"");
    }

    return notesOnScreen;
}


function navigateToNextNoteFromSearchResults(processedNoteIds) {
    utils.log("PPACTIONS_NAV_START: navigateToNextNoteFromSearchResults called.");
    utils.log(`PPACTIONS_NAV: Initial processedNoteIds size: ${processedNoteIds ? processedNoteIds.size : 'N/A'}`);

    return new Promise(function (resolve) {
        if (!isSearchResultsPage()) {
            utils.log("ERROR: PPACTIONS_NAV: 当前不在搜索结果页面，无法导航。");
            resolve({ success: false, reason: "当前不在搜索结果页面" });
            return;
        }

        try {
            const notesCurrentlyVisible = extractVisibleNotes_AdaptedOfficial();
            let foundUnprocessedNoteAndClicked = false;

            if (notesCurrentlyVisible.length > 0) {
                utils.log(`PPACTIONS_NAV: Found ${notesCurrentlyVisible.length} notes via adapted logic. Checking against processedNoteIds...`);
                for (let i = 0; i < notesCurrentlyVisible.length; i++) {
                    const noteInfo = notesCurrentlyVisible[i];
                    utils.log(`PPACTIONS_NAV: Checking note ${i + 1}: Sig="${noteInfo.signature.substring(0, 50)}..." RawTop=${noteInfo.rawContainerInfo.boundsTop}, RawDrawingOrder=${noteInfo.rawContainerInfo.drawingOrder}`);

                    if (processedNoteIds && processedNoteIds.has(noteInfo.signature)) {
                        utils.log(`  PPACTIONS_NAV: Note already processed (Signature: "${noteInfo.signature.substring(0, 50)}..."). Skipping.`);
                        continue;
                    }

                    utils.log(`PPACTIONS_NAV_CLICK_ATTEMPT: Found unprocessed note. Attempting to click: "${noteInfo.signature.substring(0, 50)}..."`);
                    if (noteInfo.clickableElement && typeof noteInfo.clickableElement.click === 'function' && noteInfo.clickableElement.click()) {
                        utils.log(`PPACTIONS_NAV_CLICK_SUCCESS: Click successful. Waiting for note detail page...`);
                        sleep(3500);
                        if (isNoteDetailPage()) {
                            utils.log(`PPACTIONS_NAV_SUCCESS: Successfully navigated to note detail page.`);
                            resolve({ success: true, noteId: noteInfo.signature, noteTitle: noteInfo.title, uniqueIdentifier: noteInfo.signature });
                            foundUnprocessedNoteAndClicked = true;
                            return;
                        } else {
                            utils.log(`WARN: PPACTIONS_NAV_FAIL_DETAIL_PAGE: Clicked note but failed to reach detail page. Attempting to go back...`);
                            backToPreviousPage();
                            sleep(1000);
                        }
                    } else {
                        utils.log(`WARN: PPACTIONS_NAV_CLICK_FAIL: Failed to click note: "${noteInfo.signature.substring(0, 50)}..." (clickableElement might be invalid or click returned false)`);
                    }
                }
            } else {
                utils.log("PPACTIONS_NAV: No notes extracted from current screen using adapted official logic.");
            }

            if (foundUnprocessedNoteAndClicked) {
                return;
            }

            // 恢复原来的下滚策略
            utils.log("PPACTIONS_NAV: No unprocessed note clicked on current screen. Checking if at bottom or need to scroll.");
            if (isSearchResultsPageAtBottom()) {
                utils.log("PPACTIONS_NAV_END_BOTTOM: Reached bottom of search results. No more notes.");
                resolve({ success: false, reason: "已到达搜索结果页面底部" });
                return;
            }

            utils.log("PPACTIONS_NAV_SCROLL_ATTEMPT: Attempting to scroll for more notes...");
            var scrollSuccess = scrollSearchResultsPage();
            if (scrollSuccess) {
                utils.log("PPACTIONS_NAV_SCROLL_SUCCESS: Scroll successful. Retrying navigation after delay...");
                sleep(1500); // 等待UI稳定
                navigateToNextNoteFromSearchResults(processedNoteIds)
                    .then(resolve)
                    .catch(function (retryError) {
                        utils.log(`PPACTIONS_NAV_RETRY_ERROR: Scroll retry failed: ${retryError.toString()}`);
                        resolve({ success: false, reason: `Scroll retry failed: ${retryError.toString()}` });
                    });
            } else {
                utils.log("PPACTIONS_NAV_SCROLL_FAIL: Scroll failed.");
                resolve({ success: false, reason: "下滚失败" });
            }

        } catch (e) {
            utils.log(`ERROR: PPACTIONS_NAV_EXCEPTION: navigateToNextNoteFromSearchResults main try-catch: ${e.toString()}\nStack: ${e.stack}`);
            resolve({ success: false, reason: `Main exception: ${e.toString()}` });
        }
    });
}


// processCurrentScreenNotes 函数已移除
// 现在由 main.js 直接处理搜索结果页逻辑，各个笔记类型由专门模块处理

// 评论采集函数已移除 - 避免循环依赖
// 现在由各个专门模块直接处理评论采集功能

// Auto.js兼容的导出方式
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        tryClickInPanel: tryClickInPanel,
        ensureAppReadyForSearch: ensureAppReadyForSearch,
        searchNotes: searchNotes,
        isSearchResultsPage: isSearchResultsPage,
        isNoteDetailPage: isNoteDetailPage,
        backToPreviousPage: backToPreviousPage,
        navigateToNextNoteFromSearchResults: navigateToNextNoteFromSearchResults,
        isSearchResultsPageAtBottom: isSearchResultsPageAtBottom,
        scrollSearchResultsPage: scrollSearchResultsPage,
        openCommentsSection: commentActions.openCommentsSection,
        extractCommentDataFromElement: commentActions.extractCommentDataFromElement,
        extractVisibleComments: commentActions.extractVisibleComments,
        scrollAndLoadMoreComments: commentActions.scrollAndLoadMoreComments,
        scrapeCommentsFromNote: commentActions.scrapeCommentsFromNote,
        extractVisibleNotes_AdaptedOfficial: extractVisibleNotes_AdaptedOfficial,
        // 评论采集函数已移除 - 避免循环依赖
        NOTE_TYPES: noteTypes.NOTE_TYPES,
        detectNoteType: noteTypes.detectNoteType
    };
} else {
    // Auto.js环境 - 使用全局导出
    this.tryClickInPanel = tryClickInPanel;
    this.ensureAppReadyForSearch = ensureAppReadyForSearch;
    this.searchNotes = searchNotes;
    this.isSearchResultsPage = isSearchResultsPage;
    this.isNoteDetailPage = isNoteDetailPage;
    this.backToPreviousPage = backToPreviousPage;
    this.navigateToNextNoteFromSearchResults = navigateToNextNoteFromSearchResults;
    this.isSearchResultsPageAtBottom = isSearchResultsPageAtBottom;
    this.scrollSearchResultsPage = scrollSearchResultsPage;
    this.openCommentsSection = commentActions.openCommentsSection;
    this.extractCommentDataFromElement = commentActions.extractCommentDataFromElement;
    this.extractVisibleComments = commentActions.extractVisibleComments;
    this.scrollAndLoadMoreComments = commentActions.scrollAndLoadMoreComments;
    this.scrapeCommentsFromNote = commentActions.scrapeCommentsFromNote;
    this.extractVisibleNotes_AdaptedOfficial = extractVisibleNotes_AdaptedOfficial;
    // 评论采集函数已移除 - 避免循环依赖
    this.NOTE_TYPES = noteTypes.NOTE_TYPES;
    this.detectNoteType = noteTypes.detectNoteType;
}

utils.log("PPACTIONS: 小红书操作模块 (xhs_actions.js) 加载完毕 (with adapted official extraction logic)。");
// ======================= xhs_actions.js END =======================