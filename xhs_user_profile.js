/**
 * 小红书用户信息采集模块
 * 通用模块，可供图文笔记和视频笔记评论采集功能调用
 * 主要功能：点击评论者昵称，进入用户页面获取小红书号
 */

const utils = require('./utils.js');

/**
 * 从评论者昵称获取用户信息（昵称 + 小红书号）
 * @param {UiObject} nicknameElement - 昵称元素对象
 * @param {string} commentText - 评论内容（用于日志记录）
 * @returns {Promise<Object>} 返回用户信息对象 {success: boolean, nickname: string, xhsId: string, error?: string}
 */
function getUserInfoFromNickname(nicknameElement, commentText) {
    return new Promise(function (resolve) {
        utils.log("USER_PROFILE: 开始获取用户信息...");

        try {
            // 处理默认参数（Auto.js不支持ES6默认参数语法）
            if (typeof commentText === 'undefined') {
                commentText = "";
            }

            // 验证昵称元素
            if (!nicknameElement) {
                utils.log("ERROR: USER_PROFILE: 昵称元素为空");
                resolve({ success: false, error: "昵称元素为空" });
                return;
            }

            const nickname = nicknameElement.text() || "未知用户";
            utils.log(`USER_PROFILE: 准备获取用户 "${nickname}" 的信息`);
            utils.log(`USER_PROFILE: 关联评论: "${commentText.substring(0, 30)}${commentText.length > 30 ? '...' : ''}"`);

            // 检查昵称元素是否可点击
            if (!nicknameElement.clickable()) {
                utils.log("ERROR: USER_PROFILE: 昵称元素不可点击");
                resolve({ success: false, nickname: nickname, error: "昵称元素不可点击" });
                return;
            }

            // 点击昵称进入用户页面
            utils.log("USER_PROFILE: 点击昵称进入用户页面...");
            const clickResult = nicknameElement.click();

            if (!clickResult) {
                utils.log("ERROR: USER_PROFILE: 点击昵称失败");
                resolve({ success: false, nickname: nickname, error: "点击昵称失败" });
                return;
            }

            utils.log("USER_PROFILE: ✓ 成功点击昵称，等待页面加载...");

            // 等待用户页面加载
            sleep(3000);

            // 查找用户信息容器
            utils.log("USER_PROFILE: 查找用户信息容器...");
            const userInfoContainer = id("com.xingin.xhs:id/flh").findOne(5000);

            if (!userInfoContainer) {
                utils.log("ERROR: USER_PROFILE: 未找到用户信息容器 (com.xingin.xhs:id/flh)");

                // 尝试查找其他可能的容器
                const alternativeResult = findAlternativeUserInfo();
                if (alternativeResult.success) {
                    utils.log("USER_PROFILE: ✓ 通过备用方法找到用户信息");

                    // 返回评论区
                    returnToCommentSection();

                    resolve({
                        success: true,
                        nickname: nickname,
                        xhsId: alternativeResult.xhsId
                    });
                    return;
                }

                // 返回评论区
                returnToCommentSection();

                resolve({ success: false, nickname: nickname, error: "未找到用户信息容器" });
                return;
            }

            utils.log("USER_PROFILE: ✓ 找到用户信息容器");

            // 在容器内查找小红书号
            utils.log("USER_PROFILE: 在容器内查找小红书号...");
            const xhsIdElement = userInfoContainer.findOne(id("com.xingin.xhs:id/h00"));

            if (!xhsIdElement) {
                utils.log("ERROR: USER_PROFILE: 在容器内未找到小红书号元素 (com.xingin.xhs:id/h00)");

                // 尝试在容器内查找其他可能包含小红书号的元素
                const alternativeXhsId = findAlternativeXhsIdInContainer(userInfoContainer);
                if (alternativeXhsId) {
                    utils.log(`USER_PROFILE: ✓ 通过备用方法找到小红书号: "${alternativeXhsId}"`);

                    // 返回评论区
                    returnToCommentSection();

                    resolve({
                        success: true,
                        nickname: nickname,
                        xhsId: alternativeXhsId
                    });
                    return;
                }

                // 返回评论区
                returnToCommentSection();

                resolve({ success: false, nickname: nickname, error: "未找到小红书号元素" });
                return;
            }

            const xhsId = xhsIdElement.text() || "";
            utils.log(`USER_PROFILE: ✓ 成功获取小红书号: "${xhsId}"`);

            // 返回评论区
            const returnResult = returnToCommentSection();

            if (!returnResult) {
                utils.log("WARNING: USER_PROFILE: 返回评论区可能失败，但用户信息已获取");
            }

            // 返回成功结果
            resolve({
                success: true,
                nickname: nickname,
                xhsId: xhsId
            });

        } catch (e) {
            utils.log(`ERROR: USER_PROFILE: 获取用户信息时发生异常: ${e}`);
            utils.log(`ERROR: USER_PROFILE: 异常堆栈: ${e.stack}`);

            // 尝试返回评论区
            try {
                returnToCommentSection();
            } catch (returnError) {
                utils.log(`ERROR: USER_PROFILE: 返回评论区时也发生异常: ${returnError}`);
            }

            resolve({
                success: false,
                error: e.toString()
            });
        }
    });
}

/**
 * 返回评论区
 * @returns {boolean} 是否成功返回
 */
function returnToCommentSection() {
    utils.log("USER_PROFILE: 准备返回评论区...");

    try {
        // 方法1: 使用返回键
        const backResult = back();
        if (backResult) {
            utils.log("USER_PROFILE: ✓ 使用返回键返回");
            sleep(2000);

            // 验证是否成功返回评论区（支持图文和视频笔记）
            if (isBackToCommentSection()) {
                utils.log("USER_PROFILE: ✓ 确认成功返回评论区");
                return true;
            }
        }

        // 方法2: 查找并点击返回按钮
        utils.log("USER_PROFILE: 尝试查找返回按钮...");
        const backButton = id("com.xingin.xhs:id/a2q").desc("返回").findOne(2000);
        if (backButton && backButton.clickable()) {
            const clickResult = backButton.click();
            if (clickResult) {
                utils.log("USER_PROFILE: ✓ 使用返回按钮返回");
                sleep(2000);

                // 验证是否成功返回评论区（支持图文和视频笔记）
                if (isBackToCommentSection()) {
                    utils.log("USER_PROFILE: ✓ 确认成功返回评论区");
                    return true;
                }
            }
        }

        // 方法3: 多次返回键
        utils.log("USER_PROFILE: 尝试多次返回键...");
        for (let i = 0; i < 3; i++) {
            back();
            sleep(1000);

            if (isBackToCommentSection()) {
                utils.log(`USER_PROFILE: ✓ 第${i + 1}次返回键成功返回评论区`);
                return true;
            }
        }

        utils.log("WARNING: USER_PROFILE: 所有返回方法都可能失败");
        return false;

    } catch (e) {
        utils.log(`ERROR: USER_PROFILE: 返回评论区时发生异常: ${e}`);
        return false;
    }
}

/**
 * 检查是否已经返回到评论区（支持图文和视频笔记）
 * @returns {boolean} 是否在评论区
 */
function isBackToCommentSection() {
    try {
        // 检查视频笔记评论区特征元素
        const videoCommentSection = id("com.xingin.xhs:id/iwv").findOne(1000);
        if (videoCommentSection) {
            utils.log("USER_PROFILE: 检测到视频笔记评论区");
            return true;
        }

        // 检查图文笔记评论区特征元素
        // 图文笔记的评论区通常包含评论容器和评论元素
        const imageTextCommentContainer = id("com.xingin.xhs:id/gks").findOne(1000);
        if (imageTextCommentContainer) {
            utils.log("USER_PROFILE: 检测到图文笔记评论区");
            return true;
        }

        // 检查评论相关的其他元素
        const commentElement = id("com.xingin.xhs:id/jfh").findOne(1000);
        if (commentElement) {
            utils.log("USER_PROFILE: 检测到评论内容元素");
            return true;
        }

        // 检查笔记详情页的特征元素（图文笔记）
        const noteDetailElement = id("com.xingin.xhs:id/gn_").findOne(1000);
        if (noteDetailElement) {
            utils.log("USER_PROFILE: 检测到图文笔记详情页");
            return true;
        }

        utils.log("USER_PROFILE: 未检测到评论区特征元素");
        return false;

    } catch (e) {
        utils.log(`ERROR: USER_PROFILE: 检查评论区时异常: ${e}`);
        return false;
    }
}

/**
 * 查找备用的用户信息（当主要容器未找到时）
 * @returns {Object} {success: boolean, xhsId?: string}
 */
function findAlternativeUserInfo() {
    utils.log("USER_PROFILE: 尝试备用方法查找用户信息...");

    try {
        // 直接查找小红书号元素（不依赖容器）
        const xhsIdElement = id("com.xingin.xhs:id/h00").findOne(3000);
        if (xhsIdElement) {
            const xhsId = xhsIdElement.text() || "";
            if (xhsId.length > 0) {
                utils.log(`USER_PROFILE: ✓ 直接找到小红书号: "${xhsId}"`);
                return { success: true, xhsId: xhsId };
            }
        }

        // 查找可能包含小红书号的文本元素
        const textViews = className("android.widget.TextView").find();
        for (let i = 0; i < textViews.length; i++) {
            const textContent = textViews[i].text() || "";
            // 小红书号通常以特定格式出现，比如包含数字和字母的组合
            if (textContent.match(/^[a-zA-Z0-9_-]{4,20}$/) && textContent.length >= 4) {
                utils.log(`USER_PROFILE: ✓ 可能找到小红书号: "${textContent}"`);
                return { success: true, xhsId: textContent };
            }
        }

        utils.log("USER_PROFILE: 备用方法未找到用户信息");
        return { success: false };

    } catch (e) {
        utils.log(`ERROR: USER_PROFILE: 备用查找方法异常: ${e}`);
        return { success: false };
    }
}

/**
 * 在指定容器内查找备用的小红书号
 * @param {UiObject} container - 容器对象
 * @returns {string|null} 小红书号或null
 */
function findAlternativeXhsIdInContainer(container) {
    utils.log("USER_PROFILE: 在容器内查找备用小红书号...");

    try {
        // 查找容器内的所有文本元素
        const textElements = container.find(className("android.widget.TextView"));

        for (let i = 0; i < textElements.length; i++) {
            const elementText = textElements[i].text() || "";
            // 匹配可能的小红书号格式
            if (elementText.match(/^[a-zA-Z0-9_-]{4,20}$/) && elementText.length >= 4) {
                utils.log(`USER_PROFILE: 在容器内找到可能的小红书号: "${elementText}"`);
                return elementText;
            }
        }

        utils.log("USER_PROFILE: 在容器内未找到备用小红书号");
        return null;

    } catch (e) {
        utils.log(`ERROR: USER_PROFILE: 在容器内查找备用小红书号时异常: ${e}`);
        return null;
    }
}

// 导出模块
module.exports = {
    getUserInfoFromNickname: getUserInfoFromNickname,
    returnToCommentSection: returnToCommentSection,
    isBackToCommentSection: isBackToCommentSection,
    findAlternativeUserInfo: findAlternativeUserInfo,
    findAlternativeXhsIdInContainer: findAlternativeXhsIdInContainer
};

utils.log("USER_PROFILE: 小红书用户信息采集模块加载完毕");
