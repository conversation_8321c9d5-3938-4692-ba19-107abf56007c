// note_navigation.js
const utils = require('./utils.js');

/**
 * Gets the current note's detailed information, focusing on extracting its text content.
 * This function assumes the script is currently on a note's detail page.
 * @returns {object} An object like { extractedText: "content" } or { extractedText: null }
 */
function getCurrentNoteInfo() {
    utils.log("NOTE_NAV_DETAIL: Attempting to extract note content.");
    let extractedText = null;

    // Try extracting as image-text note first
    const imageTextContainerId = "com.xingin.xhs:id/drg";
    let mainContentContainer = id(imageTextContainerId).findOne(2000); // Timeout for finding container

    if (mainContentContainer) {
        utils.log("NOTE_NAV_DETAIL: Found image-text note container (drg). Extracting TextViews.");
        let textParts = [];
        let textViews = mainContentContainer.find(className("android.widget.TextView"));
        if (textViews && !textViews.empty()) {
            textViews.forEach(tv => {
                let text = tv.text();
                if (text && text.trim() !== "") {
                    textParts.push(text.trim());
                }
            });
            if (textParts.length > 0) {
                extractedText = textParts.join("\n").trim();
                utils.log("NOTE_NAV_DETAIL: Extracted text from image-text note: " + (extractedText ? extractedText.substring(0, 100) : "[empty]") + "...");
                // If image-text content is found, we assume this is the primary content and return.
                return { extractedText: extractedText };
            } else {
                utils.log("NOTE_NAV_DETAIL: Image-text container (drg) found, but no text in child TextViews.");
            }
        } else {
            utils.log("NOTE_NAV_DETAIL: Image-text container (drg) found, but no TextView children.");
        }
    } else {
        utils.log("NOTE_NAV_DETAIL: Image-text note container (drg) not found. Checking for video note description.");
    }

    // If not extracted as image-text (or if mainContentContainer was found but yielded no text), try video note description
    // This means extractedText is still null at this point if we proceed here.
    const expandButtonId = "com.xingin.xhs:id/noteContentText";
    const descriptionTextId = "com.xingin.xhs:id/ba8";
    let expandButton = id(expandButtonId).findOne(1000);
    let clickedExpandButton = false;

    if (expandButton && expandButton.visibleToUser()) {
        utils.log("NOTE_NAV_DETAIL: Found video description expand button. Clicking...");
        if (expandButton.click()) {
            utils.log("NOTE_NAV_DETAIL: Clicked expand button. Waiting for content to load...");
            clickedExpandButton = true;
            sleep(1500); // Wait for content loading

            let descriptionElement = id(descriptionTextId).findOne(2000);
            if (descriptionElement) {
                let descText = descriptionElement.text();
                if (descText && descText.trim() !== "") {
                    extractedText = descText.trim();
                    utils.log("NOTE_NAV_DETAIL: Extracted video description: " + extractedText.substring(0, 100) + "...");
                } else {
                    utils.log("NOTE_NAV_DETAIL: Video description element (ba8) found but text is empty.");
                }
            } else {
                utils.log("NOTE_NAV_DETAIL: Video description element (ba8) not found after expand.");
            }
        } else {
            utils.log("NOTE_NAV_DETAIL: Failed to click video description expand button.");
        }
    } else {
        utils.log("NOTE_NAV_DETAIL: Video description expand button (noteContentText) not found or not visible.");
        // Fallback: Try to find descriptionTextId directly if expand button wasn't found/clicked.
        // This handles cases where the description might be short and not expandable, or already expanded.
        let descriptionElementDirect = id(descriptionTextId).findOne(1000);
        if (descriptionElementDirect) {
            let descText = descriptionElementDirect.text();
            if (descText && descText.trim() !== "") {
                extractedText = descText.trim(); // Assign if found
                utils.log("NOTE_NAV_DETAIL: Extracted video description (ba8 directly): " + extractedText.substring(0, 100) + "...");
            } else {
                utils.log("NOTE_NAV_DETAIL: Video description element (ba8) found directly, but text is empty.");
            }
        } else {
            utils.log("NOTE_NAV_DETAIL: Video description element (ba8) not found directly either.");
        }
    }

    if (clickedExpandButton) {
        utils.log("NOTE_NAV_DETAIL: Executing back() after attempting video description expansion.");
        back();
        utils.log("NOTE_NAV_DETAIL: Waiting after back()...");
        sleep(1000); // Delay for page state to restore
    }

    if (extractedText === null) {
        utils.log("NOTE_NAV_DETAIL: Failed to extract any note content after all attempts.");
    }

    return { extractedText: extractedText };
}

module.exports = {
    getCurrentNoteInfo
};