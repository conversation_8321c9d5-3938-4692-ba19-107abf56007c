/**
 * 小红书笔记类型检测模块
 * 负责识别不同类型的笔记（图文、视频、直播）
 */

const utils = require('./utils.js');

// 笔记类型枚举
const NOTE_TYPES = {
    IMAGE_TEXT: "image_text",    // 图文笔记
    VIDEO: "video",              // 视频笔记
    LIVE: "live",                // 直播笔记（需要跳过）
    UNKNOWN: "unknown"           // 未知类型
};

/**
 * 判断笔记类型的函数
 * @param {UiObject} container - 笔记容器元素
 * @returns {string} 笔记类型
 */
function detectNoteType(container) {
    try {
        // 确保我们在正确的容器内进行检测
        if (!container || typeof container.findOne !== 'function') {
            utils.log("NOTE_TYPE: 无效的容器对象，默认为图文笔记");
            return NOTE_TYPES.IMAGE_TEXT;
        }

        // 获取容器的边界信息，用于验证容器有效性
        let containerBounds;
        try {
            containerBounds = container.bounds();
            if (!containerBounds || containerBounds.width() <= 0 || containerBounds.height() <= 0) {
                utils.log("NOTE_TYPE: 容器边界无效，默认为图文笔记");
                return NOTE_TYPES.IMAGE_TEXT;
            }
        } catch (e) {
            utils.log("NOTE_TYPE: 无法获取容器边界，默认为图文笔记");
            return NOTE_TYPES.IMAGE_TEXT;
        }

        utils.log(`NOTE_TYPE: 开始检测笔记类型，容器边界: Top=${containerBounds.top}, Width=${containerBounds.width()}, Height=${containerBounds.height()}`);

        // 检查是否为直播笔记（在搜索结果页面可以准确检测）
        try {
            const liveElement = container.findOne(id("com.xingin.xhs:id/ikj").text("直播中"));
            if (liveElement && liveElement.visibleToUser()) {
                utils.log("NOTE_TYPE: 检测到直播笔记，需要跳过");
                return NOTE_TYPES.LIVE;
            }
        } catch (e) {
            utils.log(`NOTE_TYPE: 直播检测出错: ${e}`);
        }

        // 在搜索结果页面，视频笔记检测比较困难，暂时跳过
        // 将在详情页进行准确的类型检测
        utils.log("NOTE_TYPE: 搜索结果页面暂不检测视频笔记，将在详情页检测");

        // 默认为图文笔记
        utils.log("NOTE_TYPE: 检测结果为图文笔记");
        return NOTE_TYPES.IMAGE_TEXT;
    } catch (e) {
        utils.log(`NOTE_TYPE: 检测笔记类型时出错: ${e}, 默认为图文笔记`);
        return NOTE_TYPES.IMAGE_TEXT;
    }
}

/**
 * 获取笔记类型的中文描述
 * @param {string} noteType - 笔记类型
 * @returns {string} 中文描述
 */
function getNoteTypeDescription(noteType) {
    switch (noteType) {
        case NOTE_TYPES.IMAGE_TEXT:
            return "图文笔记";
        case NOTE_TYPES.VIDEO:
            return "视频笔记";
        case NOTE_TYPES.LIVE:
            return "直播笔记";
        case NOTE_TYPES.UNKNOWN:
        default:
            return "未知类型";
    }
}

/**
 * 在详情页检测笔记类型（更准确的检测方法）
 * @returns {string} 笔记类型
 */
function detectNoteTypeInDetailPage() {
    utils.log("NOTE_TYPE: 开始在详情页检测笔记类型...");

    try {
        // 使用 gn_ 元素来判断笔记类型
        // 存在 gn_ = 图文笔记，不存在 = 视频笔记
        const gnElement = id("com.xingin.xhs:id/gn_").findOne(1000);

        if (gnElement) {
            utils.log("NOTE_TYPE: ✓ 找到 gn_ 元素 - 确认为图文笔记");
            return NOTE_TYPES.IMAGE_TEXT;
        } else {
            utils.log("NOTE_TYPE: ✓ 未找到 gn_ 元素 - 确认为视频笔记");
            return NOTE_TYPES.VIDEO;
        }

    } catch (e) {
        utils.log(`NOTE_TYPE: 详情页检测出错: ${e}, 默认为图文笔记`);
        return NOTE_TYPES.IMAGE_TEXT;
    }
}

/**
 * 检查笔记类型是否应该被跳过
 * @param {string} noteType - 笔记类型
 * @returns {boolean} 是否应该跳过
 */
function shouldSkipNoteType(noteType) {
    return noteType === NOTE_TYPES.LIVE;
}

// Auto.js兼容的导出方式
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        NOTE_TYPES,
        detectNoteType,
        detectNoteTypeInDetailPage,
        getNoteTypeDescription,
        shouldSkipNoteType
    };
} else {
    // Auto.js环境 - 使用全局导出
    this.NOTE_TYPES = NOTE_TYPES;
    this.detectNoteType = detectNoteType;
    this.detectNoteTypeInDetailPage = detectNoteTypeInDetailPage;
    this.getNoteTypeDescription = getNoteTypeDescription;
    this.shouldSkipNoteType = shouldSkipNoteType;
}

utils.log("NOTE_TYPE: 小红书笔记类型检测模块 (xhs_note_types.js) 加载完毕。");
