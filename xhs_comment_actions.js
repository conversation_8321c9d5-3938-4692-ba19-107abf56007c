// xhs_comment_actions.js - 小红书App评论区交互操作
// 包含评论区用户采集功能相关的函数

const utils = require('./utils.js'); // 引入通用工具函数

// --- 选择器占位符 ---
// 评论区相关选择器 - 使用完整ID（包含包名前缀）
const COMMENT_SELECTORS = {
    // 评论区相关
    COMMENT_SECTION_INDICATOR: { id: "com.xingin.xhs:id/g8b", className: "android.widget.TextView", textMatches: "共\\d+条评论" }, // 整个评论区加载完成的标志 (e.g., "共123条评论")
    COMMENT_CONTAINER: { id: "com.xingin.xhs:id/g98", className: "androidx.recyclerview.widget.RecyclerView" }, // 评论区容器
    COMMENT_ITEM_ROOT: { id: "com.xingin.xhs:id/eud", className: "android.widget.LinearLayout" }, // 评论项根元素
    COMMENT_ITEM_CONTAINER: { id: "com.xingin.xhs:id/gks", className: "android.widget.LinearLayout" }, // 评论内容容器
    COMMENT_PARENT: { id: "com.xingin.xhs:id/bhd", className: "android.widget.LinearLayout" }, // 评论父容器
    COMMENT_USER_NICKNAME: { id: "com.xingin.xhs:id/jmt", className: "android.widget.TextView" }, // 评论者用户昵称
    COMMENT_TEXT_CONTENT: { id: "com.xingin.xhs:id/jfh", className: "android.widget.TextView" }, // 评论内容
    COMMENT_USER_AVATAR: { id: "com.xingin.xhs:id/e_d", className: "android.view.View" }, // 评论者头像
    COMMENT_LIKE_BUTTON: { id: "com.xingin.xhs:id/euy", className: "android.widget.Button" }, // 点赞按钮
    COMMENT_LIKE_ICON: { id: "com.xingin.xhs:id/f29", className: "android.widget.ImageView" }, // 点赞图标
    COMMENT_LIKE_COUNT: { id: "com.xingin.xhs:id/jia", className: "android.widget.TextView" }, // 点赞数
    COMMENT_SCROLL_VIEW: { scrollable: true }, // 评论列表滚动区域 (不指定ID，使用通用滚动区域)
    NO_MORE_COMMENTS_INDICATOR: { id: "com.xingin.xhs:id/evn", className: "android.widget.TextView", text: "- 到底了 -" }, // "没有更多评论"提示
    BOTTOM_INDICATOR: { className: "android.widget.TextView", text: "- 到底了 -" }, // 底部提示文本（严格匹配）
    NOTE_DETAIL_COMMENT_ICON: { id: "com.xingin.xhs:id/dwu", className: "android.widget.TextView" } // 笔记详情页评论入口/图标
};

// 不再需要额外的属性定义

// 不再使用的函数已被移除，直接使用scrapeCommentsFromNote函数

/**
 * 从当前打开的笔记详情页采集评论。
 * 简化版本，直接在函数内部实现评论提取逻辑。
 * @param {string} noteId - 当前笔记的ID。
 * @param {string} noteTitle - 当前笔记的标题。
 * @param {string} keywordsString - 逗号分隔的评论区关键字。
 * @returns {Promise<Array<object>>} 采集到的用户数据数组。
 */
function scrapeCommentsFromNote(noteId, noteTitle, keywordsString) {
    utils.log("PPACTIONS: 开始从笔记采集评论，ID: " + noteId + " (标题: " + noteTitle + ")");

    return new Promise(function (resolve) {
        let collectedComments = [];

        // 设置最大滚动次数 - 增加到50次，确保能加载更多评论
        let maxScrolls = 50;
        let currentScroll = 0;

        // 记录所有已采集的评论内容，用于调试
        let allExtractedCommentTexts = [];

        // 打开评论区
        utils.log("PPACTIONS: 尝试打开评论区...");

        // 直接使用坐标滚动到评论区
        for (let i = 0; i < 3; i++) {
            utils.log(`PPACTIONS: 执行第 ${i + 1}/3 次滚动到评论区...`);
            swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.2, 500);
            sleep(800);
        }

        utils.log("PPACTIONS: 评论区已打开。开始采集评论。");

        // 使用递归函数处理评论采集
        function collectComments() {
            // 检查是否达到最大滚动次数
            if (currentScroll >= maxScrolls) {
                finishCollection("已达到最大滚动次数");
                return;
            }

            // 提取当前屏幕上的评论
            utils.log("PPACTIONS: 提取当前屏幕上的评论...");
            let commentsData = [];

            try {
                // 使用更简单、更直接的方法查找评论
                utils.log("PPACTIONS: 使用直接方法查找评论内容...");

                // 直接查找所有评论文本元素
                let commentTextElements = id("com.xingin.xhs:id/jfh").find();
                utils.log(`PPACTIONS: 找到 ${commentTextElements ? commentTextElements.length : 0} 个评论文本元素。`);

                // 直接查找所有昵称元素
                let nicknameElements = id("com.xingin.xhs:id/jmt").find();
                utils.log(`PPACTIONS: 找到 ${nicknameElements ? nicknameElements.length : 0} 个昵称元素。`);

                // 检查是否找到了评论
                if (commentTextElements && commentTextElements.length > 0 &&
                    nicknameElements && nicknameElements.length > 0) {

                    // 确保两个数组长度相同
                    const minLength = Math.min(commentTextElements.length, nicknameElements.length);
                    utils.log(`PPACTIONS: 将处理 ${minLength} 条评论。`);

                    // 遍历所有找到的评论
                    for (let i = 0; i < minLength; i++) {
                        try {
                            // 获取评论文本和昵称
                            let commentElement = commentTextElements[i];
                            let nicknameElement = nicknameElements[i];

                            // 检查元素是否可见
                            if (!commentElement.visibleToUser() || !nicknameElement.visibleToUser()) {
                                utils.log(`PPACTIONS: 跳过不可见的评论 #${i + 1}`);
                                continue;
                            }

                            // 获取文本内容
                            let commentText = commentElement.text() || "";
                            let nickname = nicknameElement.text() || "未知用户";

                            // 获取元素位置信息，用于去重
                            let commentBounds = commentElement.bounds();
                            let commentBoundsStr = `${commentBounds.left},${commentBounds.top},${commentBounds.right},${commentBounds.bottom}`;

                            // 检查是否有有效内容
                            if (commentText && commentText.length > 0) {
                                // 创建评论数据对象
                                let commentData = {
                                    uid: "unavailable",
                                    nickname: nickname,
                                    commentText: commentText,
                                    bounds: commentBoundsStr,
                                    index: i  // 保存索引，用于匹配
                                };

                                // 添加到评论数据数组
                                commentsData.push(commentData);
                                utils.log(`PPACTIONS: 提取到评论 #${commentsData.length}: ${nickname} - "${commentText.substring(0, 20)}${commentText.length > 20 ? '...' : ''}"`);
                            } else {
                                utils.log(`PPACTIONS: 跳过空评论 #${i + 1}`);
                            }
                        } catch (e) {
                            utils.log(`PPACTIONS: 处理评论 #${i + 1} 时出错: ${e}`);
                        }
                    }

                    utils.log(`PPACTIONS: 成功提取 ${commentsData.length} 条评论。`);
                } else {
                    utils.log("PPACTIONS: 未找到评论元素，尝试备用方法...");

                    // 备用方法：查找所有文本元素，尝试识别评论
                    let allTextElements = className("android.widget.TextView").find();
                    utils.log(`PPACTIONS: 找到 ${allTextElements ? allTextElements.length : 0} 个文本元素。`);

                    // 过滤可能是评论的文本元素
                    let possibleComments = [];
                    for (let i = 0; i < allTextElements.length; i++) {
                        try {
                            let element = allTextElements[i];
                            let text = element.text() || "";

                            // 评论通常较长，且不是UI元素
                            if (text.length > 10 && element.visibleToUser() &&
                                !text.includes("点赞") && !text.includes("评论") &&
                                !text.includes("分享") && !text.includes("收藏")) {

                                possibleComments.push({
                                    element: element,
                                    text: text,
                                    bounds: element.bounds()
                                });
                            }
                        } catch (e) {
                            // 忽略错误
                        }
                    }

                    utils.log(`PPACTIONS: 找到 ${possibleComments.length} 个可能的评论。`);

                    // 处理可能的评论
                    for (let i = 0; i < possibleComments.length; i++) {
                        let item = possibleComments[i];

                        // 创建评论数据对象
                        let commentData = {
                            uid: "unavailable",
                            nickname: "未知用户", // 备用方法无法获取昵称
                            commentText: item.text,
                            bounds: `${item.bounds.left},${item.bounds.top},${item.bounds.right},${item.bounds.bottom}`,
                            index: i
                        };

                        // 添加到评论数据数组
                        commentsData.push(commentData);
                        utils.log(`PPACTIONS: 提取到可能的评论 #${commentsData.length}: "${item.text.substring(0, 20)}${item.text.length > 20 ? '...' : ''}"`);
                    }
                }

                // 不再使用父容器ID方法，我们已经有了两种方法

                utils.log(`PPACTIONS: 本次共提取到 ${commentsData.length} 条评论。`);

                // 处理提取到的评论
                let newCommentsCount = 0;
                let newMatchesCount = 0;

                // 清晰地记录评论处理过程
                utils.log(`---------- 开始处理评论 ----------`);
                utils.log(`待处理评论数: ${commentsData.length} 条`);

                // 调试：输出所有提取到的评论内容
                utils.log(`---------- 调试：所有提取到的评论 ----------`);
                for (let i = 0; i < commentsData.length; i++) {
                    if (commentsData[i] && commentsData[i].commentText) {
                        utils.log(`评论 #${i + 1}: "${commentsData[i].commentText.substring(0, 30)}${commentsData[i].commentText.length > 30 ? '...' : ''}"`);
                    } else {
                        utils.log(`评论 #${i + 1}: 无效评论`);
                    }
                }
                utils.log(`---------- 调试结束 ----------`);

                // 创建一个新的数组，用于存储已处理的评论
                let processedCommentsArray = [];

                // 遍历所有评论
                for (let i = 0; i < commentsData.length; i++) {
                    const comment = commentsData[i];

                    // 检查评论是否有效
                    if (!comment || !comment.commentText || comment.commentText.trim() === "") {
                        utils.log(`PPACTIONS: 跳过无效评论 #${i + 1}`);
                        continue;
                    }

                    // 创建评论的唯一标识（使用评论内容+昵称的组合）
                    // 使用完整的评论内容和昵称，确保唯一性
                    const commentKey = comment.nickname + ":" + comment.commentText;

                    // 调试输出完整的评论内容
                    utils.log(`PPACTIONS: 评论内容: "${comment.commentText}"`);
                    utils.log(`PPACTIONS: 评论昵称: "${comment.nickname}"`);
                    utils.log(`PPACTIONS: 评论唯一标识: "${commentKey}"`);


                    // 检查是否已经处理过这条评论
                    let isDuplicate = false;
                    let duplicateIndex = -1;

                    for (let j = 0; j < processedCommentsArray.length; j++) {
                        if (processedCommentsArray[j] === commentKey) {
                            isDuplicate = true;
                            duplicateIndex = j;
                            break;
                        }
                    }

                    if (isDuplicate) {
                        utils.log(`PPACTIONS: 跳过重复评论 #${i + 1}: "${commentKey.substring(0, 20)}..." (与评论 #${duplicateIndex + 1} 重复)`);
                        continue;
                    }

                    // 调试：输出正在处理的评论
                    utils.log(`PPACTIONS: 处理评论 #${i + 1}: "${commentKey.substring(0, 30)}..."`);

                    // 记录评论的唯一标识，用于后续去重
                    processedCommentsArray.push(commentKey);

                    // 增加新评论计数
                    newCommentsCount++;

                    // 检查是否匹配关键字
                    const matchesKeyword = utils.matchKeywords(comment.commentText, keywordsString);

                    // 创建用户数据对象
                    const userData = {
                        uid: comment.uid || "unavailable",
                        nickname: comment.nickname || "未知用户",
                        commentText: comment.commentText,
                        noteId: noteId,
                        noteTitle: noteTitle,
                        timestamp: Date.now(),
                        matchesKeyword: matchesKeyword
                    };

                    // 添加到采集结果
                    collectedComments.push(userData);

                    // 如果匹配关键字，增加匹配计数
                    if (matchesKeyword) {
                        newMatchesCount++;
                        utils.log(`PPACTIONS: 匹配并采集评论 #${collectedComments.length}: "${comment.commentText.substring(0, 20)}..." (匹配关键字)`);
                    } else {
                        utils.log(`PPACTIONS: 采集评论 #${collectedComments.length}: "${comment.commentText.substring(0, 20)}..." (未匹配关键字)`);
                    }
                }

                // 记录本批次新增的评论数量
                utils.log(`---------- 本批次评论采集 ----------`);
                utils.log(`新增评论: ${newCommentsCount} 条`);
                utils.log(`匹配关键字: ${newMatchesCount} 条`);
                utils.log(`累计采集: ${collectedComments.length} 条评论`);
                utils.log(`------------------------------------`);

                // 检查是否到达底部
                let bottomIndicator = text("- 到底了 -").findOne(500);
                let isAtBottom = bottomIndicator && bottomIndicator.visibleToUser();

                // 如果检测到底部提示，进行最后一次评论采集确认
                if (isAtBottom) {
                    utils.log("PPACTIONS: 检测到底部提示 '- 到底了 -'，进行最后一次评论采集确认...");

                    // 如果本次有新评论，说明底部还有内容，继续滚动一次
                    if (newCommentsCount > 0) {
                        utils.log(`PPACTIONS: 底部检测时发现 ${newCommentsCount} 条新评论，继续滚动一次确保完全采集...`);
                        // 继续滚动，不立即停止
                    } else {
                        // 没有新评论且到达底部，确认结束
                        utils.log("PPACTIONS: 底部检测时没有新评论，确认已到达评论区底部。");
                        finishCollection("已到达评论区底部");
                        return;
                    }
                } else {
                    // 未到达底部的情况
                    if (newCommentsCount === 0) {
                        // 如果已经滚动了至少5次且没有新评论，结束采集
                        if (currentScroll >= 5) {
                            utils.log("PPACTIONS: 连续滚动未发现新评论，可能已到达评论末尾。");
                            finishCollection("连续滚动未发现新评论");
                            return;
                        }
                    } else {
                        // 如果有新评论，记录但继续滚动
                        utils.log(`PPACTIONS: 本次采集到 ${newCommentsCount} 条新评论，继续滚动寻找更多评论...`);
                    }
                }

                // 滚动加载更多评论
                currentScroll++;
                utils.log(`PPACTIONS: 滚动加载更多评论 (${currentScroll}/${maxScrolls})...`);

                // 执行滚动
                swipe(
                    device.width / 2,
                    device.height * 0.7,
                    device.width / 2,
                    device.height * 0.3,
                    500
                );

                // 增加等待时间，确保评论完全加载
                utils.log("PPACTIONS: 等待新评论加载...");
                sleep(2500);  // 增加到2.5秒，确保评论完全加载

                // 继续采集
                collectComments();
            } catch (e) {
                utils.log(`ERROR: PPACTIONS: 提取评论时发生错误: ${e}`);
                finishCollection("发生错误: " + e);
            }
        }

        // 完成采集并保存结果
        function finishCollection(reason) {
            utils.log(`PPACTIONS: 完成评论采集，原因: ${reason}`);

            // 统计匹配关键字的评论数量
            let matchedCount = 0;
            for (let i = 0; i < collectedComments.length; i++) {
                if (collectedComments[i].matchesKeyword) {
                    matchedCount++;
                }
            }

            // 使用更清晰的日志格式
            utils.log(`========== 评论采集结果 ==========`);
            utils.log(`笔记标题: "${noteTitle}"`);
            utils.log(`总评论数: ${collectedComments.length} 条`);
            utils.log(`匹配关键字: ${matchedCount} 条`);
            utils.log(`未匹配关键字: ${collectedComments.length - matchedCount} 条`);
            utils.log(`关键字: "${keywordsString}"`);
            utils.log(`================================`);

            toast(`共采集 ${collectedComments.length} 条评论，其中 ${matchedCount} 条匹配关键字`);

            // 直接在这里保存评论到文件，不再调用utils.saveCommentsToFile
            try {
                utils.log("PPACTIONS: 开始保存评论到文件...");

                // 尝试多个可能的目录路径
                let logDir;
                let dirCreated = false;

                // 尝试在SD卡根目录创建
                try {
                    logDir = files.join(files.getSdcardPath(), "XHS_Comments");
                    if (!files.exists(logDir)) {
                        files.mkdir(logDir);
                        utils.log(`PPACTIONS: 创建目录成功: ${logDir}`);
                        dirCreated = true;
                    } else {
                        utils.log(`PPACTIONS: 目录已存在: ${logDir}`);
                        dirCreated = true;
                    }
                } catch (dirError1) {
                    utils.log(`PPACTIONS: 在SD卡根目录创建文件夹失败: ${dirError1}`);
                }

                // 如果第一个尝试失败，尝试在Download目录创建
                if (!dirCreated) {
                    try {
                        logDir = files.join(files.getSdcardPath(), "Download/XHS_Comments");
                        if (!files.exists(logDir)) {
                            files.mkdir(logDir);
                            utils.log(`PPACTIONS: 在Download目录创建成功: ${logDir}`);
                            dirCreated = true;
                        } else {
                            utils.log(`PPACTIONS: Download目录已存在: ${logDir}`);
                            dirCreated = true;
                        }
                    } catch (dirError2) {
                        utils.log(`PPACTIONS: 在Download目录创建文件夹失败: ${dirError2}`);
                    }
                }

                // 如果仍然失败，使用当前工作目录
                if (!dirCreated) {
                    try {
                        logDir = files.cwd();
                        utils.log(`PPACTIONS: 使用当前工作目录: ${logDir}`);
                        dirCreated = true;
                    } catch (dirError3) {
                        utils.log(`PPACTIONS: 获取当前工作目录失败: ${dirError3}`);
                        throw new Error("无法创建或访问任何目录，无法保存评论");
                    }
                }

                // 创建文件名，使用日期和时间
                const timestamp = new Date().getTime(); // 使用时间戳而不是ISO字符串，避免特殊字符
                const safeTitle = noteTitle.replace(/[\\/:*?"<>|]/g, "_").substring(0, 20); // 安全的文件名
                const commentsLogFilePath = files.join(logDir, `XHS_Comments_${safeTitle}_${timestamp}.txt`);

                utils.log(`PPACTIONS: 尝试创建文件: ${commentsLogFilePath}`);

                // 创建文件
                try {
                    files.create(commentsLogFilePath);
                    utils.log(`PPACTIONS: 文件创建成功: ${commentsLogFilePath}`);
                } catch (fileCreateError) {
                    utils.log(`PPACTIONS: 创建文件失败: ${fileCreateError}`);
                    throw fileCreateError;
                }

                // 写入标题和基本信息
                try {
                    const headerTimestamp = new Date().toLocaleString();
                    let header = `\n\n===== 笔记标题: "${noteTitle}" | 时间: ${headerTimestamp} | 共 ${collectedComments.length} 条评论 (${matchedCount} 条匹配关键字) =====\n\n`;
                    header += `关键字: "${keywordsString}"\n\n`;

                    utils.log("PPACTIONS: 写入文件头...");
                    files.write(commentsLogFilePath, header);
                    utils.log("PPACTIONS: 文件头写入成功");

                    // 先统计匹配关键字的评论
                    let matchedComments = [];
                    let otherComments = [];

                    // 调试：输出所有收集到的评论
                    utils.log(`---------- 调试：所有收集到的评论 (共 ${collectedComments.length} 条) ----------`);
                    for (let i = 0; i < collectedComments.length; i++) {
                        const comment = collectedComments[i];
                        if (comment && comment.commentText) {
                            utils.log(`评论 #${i + 1}: "${comment.commentText}" (${comment.matchesKeyword ? '匹配关键字' : '不匹配关键字'})`);
                            utils.log(`  昵称: "${comment.nickname}"`);

                            // 再次检查关键字匹配
                            const matchesKeyword = utils.matchKeywords(comment.commentText, keywordsString);
                            if (matchesKeyword !== comment.matchesKeyword) {
                                utils.log(`  警告: 关键字匹配状态不一致! 原始: ${comment.matchesKeyword}, 重新检查: ${matchesKeyword}`);
                                // 更新匹配状态
                                comment.matchesKeyword = matchesKeyword;
                            }
                        } else {
                            utils.log(`评论 #${i + 1}: 无效评论`);
                        }
                    }
                    utils.log(`---------- 调试结束 ----------`);

                    // 分类评论 - 使用不同的循环变量k，避免与前面的循环变量i冲突
                    for (let k = 0; k < collectedComments.length; k++) {
                        const commentItem = collectedComments[k];
                        if (commentItem) {
                            if (commentItem.matchesKeyword) {
                                matchedComments.push(commentItem);
                            } else {
                                otherComments.push(commentItem);
                            }
                        }
                    }

                    utils.log(`PPACTIONS: 评论分类完成，匹配关键字: ${matchedComments.length} 条，其他: ${otherComments.length} 条`);

                    // 写入匹配关键字的评论
                    utils.log("PPACTIONS: 写入匹配关键字的评论...");
                    files.append(commentsLogFilePath, "【匹配关键字的评论】\n\n");
                    let matchedWritten = 0;

                    if (matchedComments.length > 0) {
                        for (let m = 0; m < matchedComments.length; m++) {
                            try {
                                const matchedComment = matchedComments[m];
                                let commentLine = `${m + 1}. 用户: ${matchedComment.nickname || '未知用户'}\n`;
                                commentLine += `   评论: ${matchedComment.commentText || '无内容'}\n`;
                                commentLine += `   [✓ 匹配关键字: ${keywordsString}]\n`;
                                commentLine += `   ----------------------------\n`;

                                files.append(commentsLogFilePath, commentLine);
                                matchedWritten++;

                                // 每写入5条评论，记录一次日志
                                if (matchedWritten % 5 === 0) {
                                    utils.log(`PPACTIONS: 已写入 ${matchedWritten}/${matchedComments.length} 条匹配关键字的评论`);
                                }
                            } catch (matchedWriteError) {
                                utils.log(`PPACTIONS: 写入第 ${m + 1} 条匹配评论时出错: ${matchedWriteError}`);
                            }
                        }
                    } else {
                        files.append(commentsLogFilePath, "没有找到匹配关键字的评论\n\n");
                    }

                    utils.log(`PPACTIONS: 成功写入 ${matchedWritten} 条匹配关键字的评论`);

                    // 写入其他评论
                    utils.log("PPACTIONS: 写入其他评论...");
                    files.append(commentsLogFilePath, "\n【其他评论】\n\n");
                    let otherWritten = 0;

                    if (otherComments.length > 0) {
                        for (let n = 0; n < otherComments.length; n++) {
                            try {
                                const otherComment = otherComments[n];
                                let commentLine = `${n + 1}. 用户: ${otherComment.nickname || '未知用户'}\n`;
                                commentLine += `   评论: ${otherComment.commentText || '无内容'}\n`;
                                commentLine += `   ----------------------------\n`;

                                files.append(commentsLogFilePath, commentLine);
                                otherWritten++;

                                // 每写入10条评论，记录一次日志
                                if (otherWritten % 10 === 0) {
                                    utils.log(`PPACTIONS: 已写入 ${otherWritten}/${otherComments.length} 条其他评论`);
                                }
                            } catch (otherWriteError) {
                                utils.log(`PPACTIONS: 写入第 ${n + 1} 条其他评论时出错: ${otherWriteError}`);
                            }
                        }
                    } else {
                        files.append(commentsLogFilePath, "没有找到其他评论\n\n");
                    }

                    utils.log(`PPACTIONS: 成功写入 ${otherWritten} 条其他评论`);
                } catch (writeError) {
                    utils.log(`PPACTIONS: 写入评论内容时出错: ${writeError}`);
                    throw writeError;
                }

                utils.log(`PPACTIONS: 已保存 ${collectedComments.length} 条评论数据到文件: ${commentsLogFilePath}`);
                toast(`已保存 ${collectedComments.length} 条评论到文件，其中 ${matchedCount} 条匹配关键字`);

                // 尝试打开文件所在目录
                try {
                    app.viewFile(commentsLogFilePath);
                    utils.log(`PPACTIONS: 已尝试打开文件: ${commentsLogFilePath}`);
                } catch (openError) {
                    utils.log(`PPACTIONS: 尝试打开文件时出错: ${openError}`);
                }
            } catch (saveError) {
                utils.log(`PPACTIONS: 保存评论数据时出错: ${saveError}`);
                utils.log(`PPACTIONS: 错误详情: ${saveError.stack || saveError}`);

                // 尝试保存到备用位置
                try {
                    const backupPath = files.join(files.cwd(), `XHS_Comments_Backup_${new Date().getTime()}.txt`);
                    utils.log(`PPACTIONS: 尝试保存到备用位置: ${backupPath}`);

                    let backupContent = `===== 备份的评论数据 =====\n`;
                    backupContent += `笔记标题: ${noteTitle}\n`;
                    backupContent += `评论数量: ${collectedComments.length}\n`;
                    backupContent += `关键字: ${keywordsString}\n\n`;

                    // 简单地保存评论内容，不做太多格式化
                    for (let b = 0; b < collectedComments.length; b++) {
                        if (collectedComments[b]) {
                            backupContent += `${b + 1}. ${collectedComments[b].nickname}: ${collectedComments[b].commentText}\n`;
                        }
                    }

                    files.write(backupPath, backupContent);
                    utils.log(`PPACTIONS: 已成功保存到备用位置: ${backupPath}`);
                    toast(`保存到原位置失败，已保存到: ${backupPath}`);
                } catch (backupError) {
                    utils.log(`PPACTIONS: 保存到备用位置也失败了: ${backupError}`);
                    toast(`保存评论数据完全失败，请检查存储权限`);
                }
            }

            resolve(collectedComments);
        }

        // 开始采集评论
        collectComments();
    });
}

// --- Module Exports ---
module.exports = {
    // 评论区相关函数
    scrapeCommentsFromNote: scrapeCommentsFromNote,

    // 导出选择器以便调试
    COMMENT_SELECTORS: COMMENT_SELECTORS
};

utils.log("PPACTIONS: 小红书评论操作模块 (xhs_comment_actions.js) 加载完毕。");