// 工具模块：提供通用辅助函数

var logFilePath = files.join(files.getSdcardPath(), "XHS_Automation_Log.txt");
var commentsLogFilePath = files.join(files.getSdcardPath(), "XHS_Comments_Log.txt");

// 确保日志文件存在
if (!files.exists(logFilePath)) {
    files.create(logFilePath);
    console.log("UTILS: 创建日志文件: " + logFilePath);
}

// 确保评论日志文件存在
if (!files.exists(commentsLogFilePath)) {
    files.create(commentsLogFilePath);
    console.log("UTILS: 创建评论日志文件: " + commentsLogFilePath);
}

// 基础日志功能，后续可以接入UI的日志显示
function originalLog(message) { // Renamed to avoid confusion if uiManagerRef is not set
    console.log("UTILS_FULL_LOG: " + message); // 输出到Auto.js的控制台
    var timestamp = new Date().toLocaleString();
    try {
        files.append(logFilePath, timestamp + ": " + message + "\n");
    } catch (e) {
        console.error("UTILS_FULL_ERROR: 写入日志文件失败: " + e);
    }
}

function checkAndRequestAccessibility() {
    originalLog("检查无障碍服务状态...");
    if (!auto.service) {
        originalLog("无障碍服务未开启。");
        toast("请先开启无障碍服务");
        try {
            app.startActivity({
                action: "android.settings.ACCESSIBILITY_SETTINGS"
            });
            toast("请在设置中找到 [小红书自动化] (或您打包的应用名) 并开启无障碍服务");
            originalLog("已尝试跳转到无障碍设置页面。");
        } catch (e) {
            originalLog("无法自动跳转到无障碍设置: " + e);
            toast("无法自动跳转，请手动前往系统设置开启无障碍服务");
        }
        return false;
    }
    originalLog("无障碍服务已开启。");
    return true;
}

function randomDelay(minMs, maxMs) {
    var delay = Math.floor(Math.random() * (maxMs - minMs + 1)) + minMs;
    originalLog("执行随机延时: " + delay + "ms");
    sleep(delay);
}

var uiManagerRef;
var logManagerRef;

function setUiManager(manager) {
    originalLog("UTILS_FULL: setUiManager called.");
    uiManagerRef = manager;
}

/**
 * 设置日志管理器引用
 * @param {Object} logManager - 日志管理器对象
 */
function setLogManager(logManager) {
    originalLog("UTILS_FULL: setLogManager called.");
    logManagerRef = logManager;
}

function advancedLog(message) {
    console.log("UTILS_FULL_ADV_LOG: " + message);
    var timestamp = new Date().toLocaleString();
    try {
        files.append(logFilePath, timestamp + ": " + message + "\n");
    } catch (e) {
        console.error("UTILS_FULL_ADV_ERROR: 写入日志文件失败: " + e);
    }

    // 发送到UI日志管理器
    if (logManagerRef && typeof logManagerRef.addLog === 'function') {
        try {
            logManagerRef.addLog('INFO', message, 'UTILS');
        } catch (e) {
            console.error("UTILS_FULL_ADV_ERROR: 发送日志到日志管理器失败: " + e);
        }
    }

    // 保持对旧UI管理器的兼容性
    if (uiManagerRef && typeof uiManagerRef.updateLog === 'function') {
        originalLog("UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.");
        uiManagerRef.updateLog(message);
    } else {
        originalLog("UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.");
    }
}

/**
 * 记录警告级别的日志
 * @param {string} message - 警告信息
 */
function logWarn(message) {
    var warnMessage = "WARN: " + message;
    console.warn(warnMessage);

    // 写入文件日志
    var timestamp = new Date().toLocaleString();
    try {
        files.append(logFilePath, timestamp + ": " + warnMessage + "\n");
    } catch (e) {
        console.error("UTILS_WARN_ERROR: 写入警告日志文件失败: " + e);
    }

    // 发送到UI日志管理器
    if (logManagerRef && typeof logManagerRef.addLog === 'function') {
        try {
            logManagerRef.addLog('WARN', message, 'UTILS');
        } catch (e) {
            console.error("UTILS_WARN_ERROR: 发送警告日志到日志管理器失败: " + e);
        }
    }

    // 保持对旧UI管理器的兼容性
    if (uiManagerRef && typeof uiManagerRef.updateLog === 'function') {
        uiManagerRef.updateLog(warnMessage);
    }
}

/**
 * 记录错误级别的日志
 * @param {string} message - 错误信息
 */
function logError(message) {
    var errorMessage = "ERROR: " + message;
    console.error(errorMessage);

    // 写入文件日志
    var timestamp = new Date().toLocaleString();
    try {
        files.append(logFilePath, timestamp + ": " + errorMessage + "\n");
    } catch (e) {
        console.error("UTILS_ERROR_ERROR: 写入错误日志文件失败: " + e);
    }

    // 发送到UI日志管理器
    if (logManagerRef && typeof logManagerRef.addLog === 'function') {
        try {
            logManagerRef.addLog('ERROR', message, 'UTILS');
        } catch (e) {
            console.error("UTILS_ERROR_ERROR: 发送错误日志到日志管理器失败: " + e);
        }
    }

    // 保持对旧UI管理器的兼容性
    if (uiManagerRef && typeof uiManagerRef.updateLog === 'function') {
        uiManagerRef.updateLog(errorMessage);
    }
}

/**
 * 简单检查App是否在前台（已废弃，保留兼容性）
 * @param {string} appName - App的名称，例如 "小红书"
 * @returns {boolean} - 总是返回false，因为我们采用强制重启策略
 * @deprecated 已采用强制重启策略，不再需要状态检测
 */
function isAppRunning(appName) {
    advancedLog("UTILS: isAppRunning已废弃，请手动关闭小红书app");
    return false;
}

/**
 * 强制关闭App（已废弃，保留兼容性）
 * @param {string} appName - App的名称，例如 "小红书"
 * @returns {boolean} - 总是返回false
 * @deprecated 已改为手动关闭策略
 */
function forceCloseApp(appName) {
    advancedLog("UTILS: forceCloseApp已废弃，请手动关闭小红书app");
    return false;
}

/**
 * 确保指定的App已打开并切换到前台（增强版，支持强制重启）
 * @param {string} appName - App的名称，例如 "小红书"
 * @param {boolean} forceRestart - 是否强制重启App，默认false
 * @returns {boolean} - 如果成功打开或已在前台则返回true，否则false
 */
function ensureAppOpen(appName, forceRestart) {
    if (typeof forceRestart === 'undefined') {
        forceRestart = false;
    }

    advancedLog("UTILS: 尝试确保App '" + appName + "' 打开并切换到前台，强制重启: " + forceRestart);
    var packageName = app.getPackageName(appName);
    if (!packageName) {
        advancedLog("UTILS_ERROR: 未找到App '" + appName + "' 的包名。请确认应用名称是否正确或已安装。");
        toast("无法找到应用 '" + appName + "'");
        return false;
    }

    // 如果需要强制重启，先检查并关闭App
    if (forceRestart) {
        advancedLog("UTILS: 强制重启模式，检查App是否正在运行...");
        if (isAppRunning(appName)) {
            advancedLog("UTILS: App正在运行，正在关闭...");
            toast("正在重启 " + appName + "...");

            if (forceCloseApp(appName)) {
                advancedLog("UTILS: App关闭成功，等待3秒后重新启动");
                sleep(3000);
            } else {
                advancedLog("UTILS_WARN: App关闭可能不完全，继续尝试启动");
                sleep(2000);
            }
        } else {
            advancedLog("UTILS: App未在运行，直接启动");
        }
    }

    // 检查是否已在前台
    if (!forceRestart && currentPackage() === packageName) {
        advancedLog("UTILS: App '" + appName + "' (" + packageName + ") 已在前台。");
        return true;
    }

    // 启动App
    advancedLog("UTILS: 启动App '" + appName + "' (" + packageName + ")...");
    if (app.launch(packageName)) {
        advancedLog("UTILS: App '" + appName + "' 启动命令执行成功。等待应用响应...");
        sleep(3000); // 等待应用加载

        // 等待App切换到前台
        for (var i = 0; i < 8; i++) { // 等待最多8秒
            if (currentPackage() === packageName) {
                advancedLog("UTILS: App '" + appName + "' 已成功切换到前台。");
                toast(appName + " 启动成功");
                return true;
            }
            sleep(1000);
        }
        advancedLog("UTILS_WARN: App '" + appName + "' 已启动但未能确认切换到前台。");
        return false;
    } else {
        advancedLog("UTILS_ERROR: 启动App '" + appName + "' (" + packageName + ") 失败。");
        toast("启动应用 '" + appName + "' 失败");
        return false;
    }
}

// 文本处理工具函数

/**
 * 规范化文本，转换为小写，移除首尾及中间多余空格
 * @param {string} text - 需要规范化的文本
 * @returns {string} - 规范化后的文本，如果输入非字符串则返回空字符串
 */
function normalizeText(text) {
    if (typeof text !== 'string') {
        return "";
    }
    return text.toLowerCase().trim().replace(/\s+/g, ' ');
}

/**
 * 检查文本是否匹配关键字列表中的任何一个（“或”逻辑）
 * 关键字以逗号分隔，匹配时不区分大小写
 * @param {string} text - 要检查的文本
 * @param {string} keywordsString - 逗号分隔的关键字字符串
 * @returns {boolean} - 如果匹配到任何一个关键字则返回true，否则false
 */
function matchKeywords(text, keywordsString) {
    if (typeof text !== 'string' || typeof keywordsString !== 'string' || keywordsString.trim() === "") {
        return false;
    }
    var normalizedText = normalizeText(text);
    // 拆分关键字，对每个关键字也进行规范化，并过滤掉空关键字
    var keywords = keywordsString.split(',')
        .map(function (kw) { return normalizeText(kw.trim()); })
        .filter(function (kw) { return kw !== ""; });

    if (keywords.length === 0) {
        advancedLog("UTILS: matchKeywords - 未提供有效关键字用于匹配。");
        return false; // 没有有效的关键字可供匹配
    }

    // 使用传统的for循环代替for...of循环
    for (var i = 0; i < keywords.length; i++) {
        var keyword = keywords[i];
        if (normalizedText.includes(keyword)) {
            advancedLog("UTILS: matchKeywords - 文本 \"" + normalizedText + "\" 匹配到关键字 \"" + keyword + "\"");
            return true;
        }
    }
    advancedLog("UTILS: matchKeywords - 文本 \"" + normalizedText + "\" 未匹配到任何关键字: [" + keywords.join(', ') + "]");
    return false;
}

/**
 * 从评论文本中提取用户属地信息
 * 小红书评论格式通常为：用户昵称 + 属地信息（如：IP属地：北京）
 * @param {string} commentText - 评论文本内容
 * @returns {string} - 提取到的属地信息，如果未找到则返回空字符串
 */
function extractUserLocation(commentText) {
    if (typeof commentText !== 'string' || commentText.trim() === "") {
        return "";
    }

    // 直接找"回复"前面的词，这是最可靠的方法
    // 评论格式：求推荐 昨天 11:55 广东 回复
    var match = commentText.match(/(\S+)\s+回复$/);

    if (match && match[1]) {
        var location = match[1].trim();

        // 检查是否是省份名称
        var provinces = [
            "北京", "上海", "天津", "重庆", "河北", "山西", "内蒙古", "辽宁", "吉林",
            "黑龙江", "江苏", "浙江", "安徽", "福建", "江西", "山东", "河南", "湖北", "湖南",
            "广东", "广西", "海南", "四川", "贵州", "云南", "西藏", "陕西", "甘肃", "青海",
            "宁夏", "新疆"
        ];

        for (var i = 0; i < provinces.length; i++) {
            if (location === provinces[i]) {
                var result = location; // 创建一个新的变量来返回
                advancedLog("UTILS: extractUserLocation - 从评论中提取到属地: \"" + result + "\"");
                return result;
            }
        }
    }

    advancedLog("UTILS: extractUserLocation - 未从评论中找到属地信息: \"" + commentText.substring(0, 50) + "...\"");
    return "";
}

/**
 * 获取区域选择器对应的区域名称
 * @param {number} regionIndex - 区域选择器的索引
 * @returns {string} - 对应的区域名称
 */
function getRegionNameByIndex(regionIndex) {
    var regions = [
        "不限", "北京", "上海", "天津", "重庆", "河北", "山西", "内蒙古", "辽宁", "吉林",
        "黑龙江", "江苏", "浙江", "安徽", "福建", "江西", "山东", "河南", "湖北", "湖南",
        "广东", "广西", "海南", "四川", "贵州", "云南", "西藏", "陕西", "甘肃", "青海",
        "宁夏", "新疆"
    ];

    if (regionIndex >= 0 && regionIndex < regions.length) {
        return regions[regionIndex];
    }
    return "不限";
}

/**
 * 检查用户属地是否匹配目标区域
 * @param {string} userLocation - 用户属地
 * @param {number} targetRegionIndex - 目标区域索引（0表示不限）
 * @returns {boolean} - 是否匹配
 */
function matchUserRegion(userLocation, targetRegionIndex) {
    // 如果目标区域为"不限"（索引0），则匹配所有
    if (targetRegionIndex === 0) {
        advancedLog("UTILS: matchUserRegion - 目标区域为\"不限\"，匹配所有用户");
        return true;
    }

    // 如果用户属地为空，则不匹配任何特定区域
    if (!userLocation || userLocation.trim() === "") {
        advancedLog("UTILS: matchUserRegion - 用户属地为空，不匹配特定区域");
        return false;
    }

    var targetRegion = getRegionNameByIndex(targetRegionIndex);
    var normalizedUserLocation = normalizeText(userLocation);
    var normalizedTargetRegion = normalizeText(targetRegion);

    // 现在区域数据已经去掉了"省"和"市"字，可以直接匹配
    if (normalizedUserLocation.includes(normalizedTargetRegion)) {
        advancedLog("UTILS: matchUserRegion - 用户属地\"" + userLocation + "\" 匹配目标区域\"" + targetRegion + "\"");
        return true;
    }

    advancedLog("UTILS: matchUserRegion - 用户属地\"" + userLocation + "\" 不匹配目标区域\"" + targetRegion + "\"");
    return false;
}

/**
 * [已移除] 保存评论数据到文件 - 此函数已被移除，请使用xhs_simple_comments.js中的saveCommentsToFile函数
 * @deprecated 此函数已被移除，请使用xhs_simple_comments.js中的saveCommentsToFile函数
 */
function saveCommentsToFile(comments, noteTitle, keywordsString) {
    advancedLog("UTILS: saveCommentsToFile - 此函数已被移除，请使用xhs_simple_comments.js中的saveCommentsToFile函数");
    console.log("UTILS: saveCommentsToFile - 此函数已被移除，请使用xhs_simple_comments.js中的saveCommentsToFile函数");
    toast("评论保存功能已移至xhs_simple_comments.js，请更新您的代码");
    return {
        success: false,
        error: "此函数已被移除",
        message: "请使用xhs_simple_comments.js中的saveCommentsToFile函数"
    };
}

// Auto.js兼容的导出方式
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        log: advancedLog,
        logWarn: logWarn,
        logError: logError,
        checkAndRequestAccessibility: checkAndRequestAccessibility,
        randomDelay: randomDelay,
        setUiManager: setUiManager,
        setLogManager: setLogManager,
        ensureAppOpen: ensureAppOpen,
        isAppRunning: isAppRunning,
        forceCloseApp: forceCloseApp,
        normalizeText: normalizeText,
        matchKeywords: matchKeywords,
        extractUserLocation: extractUserLocation,
        matchUserRegion: matchUserRegion,
        getRegionNameByIndex: getRegionNameByIndex
    };
} else {
    // Auto.js环境 - 使用全局导出
    this.log = advancedLog;
    this.logWarn = logWarn;
    this.logError = logError;
    this.checkAndRequestAccessibility = checkAndRequestAccessibility;
    this.randomDelay = randomDelay;
    this.setUiManager = setUiManager;
    this.setLogManager = setLogManager;
    this.ensureAppOpen = ensureAppOpen;
    this.isAppRunning = isAppRunning;
    this.forceCloseApp = forceCloseApp;
    this.normalizeText = normalizeText;
    this.matchKeywords = matchKeywords;
    this.extractUserLocation = extractUserLocation;
    this.matchUserRegion = matchUserRegion;
    this.getRegionNameByIndex = getRegionNameByIndex;
}

console.log("UTILS_FULL: utils.js module (full version with text utils) parsed.");
originalLog("UTILS_FULL: utils.js (full version) initialized.");

// 测试文件写入功能
(function testFileWriting() {
    try {
        console.log("UTILS: 测试文件写入功能...");

        // 测试日志文件
        var testLogPath = files.join(files.getSdcardPath(), "XHS_Test_Log.txt");
        console.log("UTILS: 测试日志文件路径: " + testLogPath);

        // 创建测试文件
        if (!files.exists(testLogPath)) {
            files.create(testLogPath);
            console.log("UTILS: 创建测试文件: " + testLogPath + ", 结果: " + files.exists(testLogPath));
        }

        // 写入测试内容
        files.write(testLogPath, "这是一个测试文件，用于测试文件写入功能。\n");
        files.append(testLogPath, "这是追加的内容，时间: " + new Date().toLocaleString() + "\n");

        console.log("UTILS: 文件写入测试完成。");

        // 不再测试已废弃的评论保存功能
        console.log("UTILS: 跳过评论保存测试，该功能已移至xhs_comment_actions.js");

        // 显示toast通知
        toast("文件写入测试完成，请检查日志");
    } catch (e) {
        console.error("UTILS_ERROR: 文件写入测试失败: " + e);
        toast("文件写入测试失败: " + e);
    }
})();