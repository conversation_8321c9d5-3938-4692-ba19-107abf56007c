# 小红书自动化项目配置说明文档

## 配置文件概述

项目使用 `config.js` 模块管理所有配置项，配置文件以JSON格式存储在本地，支持动态加载和保存。

## 主要配置项详解

### 1. 功能开关配置

#### enableCommenting (boolean)
- **功能**: 是否启用评论功能
- **默认值**: `false`
- **说明**: 控制是否执行评论发布功能，包括AI评论和预设评论
- **影响模块**: `main.js`, `xhs_simple_comments.js`, `xhs_video_comments.js`

#### enableLlmComments (boolean)
- **功能**: 是否启用AI评论
- **默认值**: `false`
- **说明**: 控制是否使用AI生成评论内容
- **前置条件**: `enableCommenting` 必须为 `true`
- **影响模块**: `xhs_note_commenting.js`, `llm_service.js`

#### enableUserCollection (boolean)
- **功能**: 是否启用用户信息采集
- **默认值**: `false`
- **说明**: 控制是否采集笔记评论区的用户信息
- **影响模块**: `xhs_simple_comments.js`, `xhs_video_comments.js`

#### enableLiking (boolean)
- **功能**: 是否启用点赞功能
- **默认值**: `false`
- **说明**: 控制是否对匹配关键词的评论进行点赞
- **影响模块**: `xhs_simple_comments.js`, `xhs_video_comments.js`

#### enableDeduplication (boolean)
- **功能**: 是否启用去重功能
- **默认值**: `true`
- **说明**: 控制是否跳过已经评论过的笔记
- **影响模块**: `xhs_note_commenting.js`

### 2. AI服务配置

#### useDoubaoProxy (boolean)
- **功能**: 是否使用豆包AI服务
- **默认值**: `false`
- **说明**: 控制AI评论生成使用豆包AI还是其他API服务
- **影响模块**: `llm_service.js`, `doubao_webview.js`

#### llmUseShareLink (boolean)
- **功能**: AI评论是否使用分享链接
- **默认值**: `true`
- **说明**: 控制AI生成评论时是否包含笔记的分享链接内容
- **影响模块**: `xhs_note_commenting.js`, `xhs_share_link.js`

#### doubaoAccount (string)
- **功能**: 豆包AI登录账号
- **默认值**: `""`
- **说明**: 豆包AI自动登录使用的手机号
- **安全性**: 敏感信息，建议加密存储
- **影响模块**: `doubao_login.js`

#### llmApiUrl (string)
- **功能**: AI API服务地址
- **默认值**: `""`
- **说明**: 当不使用豆包AI时的API服务地址
- **影响模块**: `llm_service.js`

#### llmApiKey (string)
- **功能**: AI API密钥
- **默认值**: `""`
- **说明**: API服务的认证密钥
- **安全性**: 敏感信息，建议加密存储
- **影响模块**: `llm_service.js`

### 3. 搜索和筛选配置

#### searchKeyword (string)
- **功能**: 搜索关键词
- **默认值**: `""`
- **说明**: 用于搜索小红书笔记的关键词
- **影响模块**: `main.js`, `xhs_actions.js`

#### keywords (string)
- **功能**: 评论筛选关键词
- **默认值**: `""`
- **说明**: 用于筛选和匹配评论的关键词，多个关键词用逗号分隔
- **影响模块**: `xhs_simple_comments.js`, `xhs_video_comments.js`

#### sortByOption (number)
- **功能**: 搜索结果排序选项
- **默认值**: `0`
- **可选值**: 
  - `0`: 综合
  - `1`: 最新
  - `2`: 最多点赞
  - `3`: 最多评论
  - `4`: 最多收藏
- **影响模块**: `xhs_actions.js`

#### publishTimeOption (number)
- **功能**: 发布时间筛选选项
- **默认值**: `0`
- **可选值**:
  - `0`: 不限
  - `1`: 一天内
  - `2`: 一周内
  - `3`: 半年内
- **影响模块**: `xhs_actions.js`

#### locationDistanceOption (number)
- **功能**: 位置距离筛选选项
- **默认值**: `0`
- **可选值**:
  - `0`: 不限
  - `1`: 同城
  - `2`: 附近
- **影响模块**: `xhs_actions.js`

#### targetRegion (number)
- **功能**: 目标区域索引
- **默认值**: `0`
- **说明**: 用于地域筛选的区域索引
- **影响模块**: `xhs_simple_comments.js`, `xhs_video_comments.js`

### 4. 评论内容配置

#### customComments (Array<string>)
- **功能**: 预设评论列表
- **默认值**: `[]`
- **说明**: 预设的评论内容数组，用于非AI评论模式
- **格式**: 字符串数组，每个元素是一条评论
- **影响模块**: `xhs_note_commenting.js`

#### commentSelectionMode (string)
- **功能**: 评论选择模式
- **默认值**: `"random"`
- **可选值**:
  - `"random"`: 随机选择
  - `"sequential"`: 顺序选择
- **说明**: 控制从预设评论列表中选择评论的方式
- **影响模块**: `xhs_note_commenting.js`

#### llmPromptTemplates (Array<Object>)
- **功能**: AI提示词模板列表
- **默认值**: `[]`
- **格式**: 
```javascript
[
  {
    id: "template1",
    name: "模板名称",
    content: "提示词内容",
    isDefault: false
  }
]
```
- **影响模块**: `llm_service.js`

### 5. 系统和性能配置

#### maxNotesToProcess (number)
- **功能**: 最大处理笔记数量
- **默认值**: `50`
- **说明**: 单次任务最多处理的笔记数量
- **影响模块**: `main.js`

#### delayBetweenNotes (number)
- **功能**: 笔记间处理延迟
- **默认值**: `3000`
- **单位**: 毫秒
- **说明**: 处理完一个笔记后等待的时间
- **影响模块**: `main.js`

#### scrollDelay (number)
- **功能**: 滚动延迟时间
- **默认值**: `2000`
- **单位**: 毫秒
- **说明**: 滚动页面后等待加载的时间
- **影响模块**: `xhs_actions.js`

#### webviewTimeout (number)
- **功能**: WebView操作超时时间
- **默认值**: `30000`
- **单位**: 毫秒
- **说明**: WebView操作的最大等待时间
- **影响模块**: `doubao_webview.js`

#### logLevel (string)
- **功能**: 日志级别
- **默认值**: `"INFO"`
- **可选值**: `"DEBUG"`, `"INFO"`, `"WARN"`, `"ERROR"`
- **说明**: 控制日志输出的详细程度
- **影响模块**: `utils.js`

### 6. 文件路径配置

#### configFilePath (string)
- **功能**: 配置文件存储路径
- **默认值**: `"/sdcard/xhs_automation_config.json"`
- **说明**: 配置文件在设备上的存储位置
- **影响模块**: `config.js`

#### logFilePath (string)
- **功能**: 日志文件存储路径
- **默认值**: `"/sdcard/xhs_automation_logs.txt"`
- **说明**: 日志文件在设备上的存储位置
- **影响模块**: `utils.js`

#### dataExportPath (string)
- **功能**: 数据导出路径
- **默认值**: `"/sdcard/XHS_Comments/"`
- **说明**: 采集的用户数据导出目录
- **影响模块**: `data_manager.js`

## 配置文件示例

```json
{
  "enableCommenting": true,
  "enableLlmComments": true,
  "enableUserCollection": false,
  "enableLiking": false,
  "enableDeduplication": true,
  "useDoubaoProxy": true,
  "llmUseShareLink": true,
  "doubaoAccount": "***********",
  "llmApiUrl": "",
  "llmApiKey": "",
  "searchKeyword": "美食推荐",
  "keywords": "好吃,推荐,美味",
  "sortByOption": 1,
  "publishTimeOption": 2,
  "locationDistanceOption": 0,
  "targetRegion": 0,
  "customComments": [
    "看起来很不错呢！",
    "谢谢分享，学到了！",
    "这个真的很实用"
  ],
  "commentSelectionMode": "random",
  "llmPromptTemplates": [
    {
      "id": "default",
      "name": "默认模板",
      "content": "请根据这篇笔记内容，生成一条自然、友好的评论",
      "isDefault": true
    }
  ],
  "maxNotesToProcess": 50,
  "delayBetweenNotes": 3000,
  "scrollDelay": 2000,
  "webviewTimeout": 30000,
  "logLevel": "INFO",
  "configFilePath": "/sdcard/xhs_automation_config.json",
  "logFilePath": "/sdcard/xhs_automation_logs.txt",
  "dataExportPath": "/sdcard/XHS_Comments/"
}
```

## 配置管理API

### 加载配置
```javascript
const config = require('./config.js');
const currentConfig = config.loadConfig();
```

### 保存配置
```javascript
const config = require('./config.js');
const newConfig = {
  enableCommenting: true,
  enableLlmComments: false
  // ... 其他配置项
};
config.saveConfig(newConfig);
```

### 获取默认配置
```javascript
const config = require('./config.js');
const defaultConfig = config.getDefaultConfig();
```

### 重置配置
```javascript
const config = require('./config.js');
config.resetToDefault();
```

## 配置验证

### 必需配置项检查
在程序启动时，系统会检查以下必需配置项：
- 如果启用AI评论，必须配置AI服务相关参数
- 如果启用用户采集，必须配置关键词
- 如果启用豆包AI，必须配置豆包账号

### 配置项类型验证
系统会验证配置项的数据类型：
- 布尔值配置项必须是 `true` 或 `false`
- 数值配置项必须是有效数字
- 字符串配置项不能为 `null` 或 `undefined`
- 数组配置项必须是有效数组

### 配置项范围验证
系统会验证配置项的取值范围：
- 选项类配置项必须在有效范围内
- 时间延迟配置项必须大于0
- 数量限制配置项必须在合理范围内

## 配置迁移

### 版本升级配置迁移
当项目版本升级时，配置迁移机制会：
1. 保留现有有效配置项
2. 添加新增配置项的默认值
3. 移除已废弃的配置项
4. 转换格式变更的配置项

### 配置备份和恢复
系统提供配置备份和恢复功能：
- 自动备份配置文件
- 支持手动导出配置
- 支持从备份恢复配置
- 配置文件损坏时自动恢复默认配置

## 安全考虑

### 敏感信息保护
- 账号密码等敏感信息建议加密存储
- 避免在日志中输出敏感配置项
- 定期更换API密钥等认证信息

### 配置文件权限
- 设置适当的文件访问权限
- 避免配置文件被其他应用读取
- 定期检查配置文件完整性

---

**文档版本**: 1.0  
**最后更新**: 2024年1月  
**维护者**: 项目开发团队

> 配置项的修改可能影响程序行为，请在充分理解各配置项作用的基础上进行调整。
