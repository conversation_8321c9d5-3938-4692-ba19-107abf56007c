// xhs_account_manager.js - 小红书多账号管理模块
// 负责账号切换、登录、退出等功能

const utils = require('./utils.js');

// Auto.js兼容的存储创建
var storage;
if (typeof storages !== 'undefined') {
    storage = storages.create("xhs_account_manager");
} else {
    // 如果storages不可用，使用备用方案
    storage = {
        put: function (key, value) { console.log("Account Storage put:", key, value); },
        get: function (key, defaultValue) { console.log("Account Storage get:", key); return defaultValue; }
    };
}

// 账号管理状态
let accountList = []; // 账号列表
let currentAccountIndex = 0; // 当前账号索引
let isAccountSwitching = false; // 是否正在切换账号

// 初始化账号管理器
function initAccountManager() {
    try {
        // 从存储中读取账号列表
        const storedAccounts = storage.get("account_list", "");
        const storedIndex = storage.get("current_account_index", 0);

        if (storedAccounts) {
            accountList = parseAccountList(storedAccounts);
        }
        currentAccountIndex = storedIndex;

        utils.log("ACCOUNT: 账号管理器初始化完成 - 账号数量: " + accountList.length + ", 当前索引: " + currentAccountIndex);
        return true;
    } catch (e) {
        utils.log("ERROR: ACCOUNT: 账号管理器初始化失败: " + e.toString());
        return false;
    }
}

// 解析账号列表字符串
function parseAccountList(accountString) {
    try {
        const accounts = [];
        const lines = accountString.split('\n');

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line && line.includes(',')) {
                const parts = line.split(',');
                if (parts.length >= 2) {
                    accounts.push({
                        username: parts[0].trim(),
                        password: parts[1].trim(),
                        index: i
                    });
                }
            }
        }

        utils.log("ACCOUNT: 解析账号列表完成 - 有效账号数: " + accounts.length);
        return accounts;
    } catch (e) {
        utils.log("ERROR: ACCOUNT: 解析账号列表失败: " + e.toString());
        return [];
    }
}

// 设置账号列表
function setAccountList(accountString) {
    try {
        accountList = parseAccountList(accountString);
        currentAccountIndex = 0;

        // 保存到存储
        storage.put("account_list", accountString);
        storage.put("current_account_index", 0);

        utils.log("ACCOUNT: 账号列表已更新 - 账号数量: " + accountList.length);
        return true;
    } catch (e) {
        utils.log("ERROR: ACCOUNT: 设置账号列表失败: " + e.toString());
        return false;
    }
}

// 获取当前账号信息
function getCurrentAccount() {
    if (accountList.length === 0 || currentAccountIndex >= accountList.length) {
        return null;
    }
    return accountList[currentAccountIndex];
}

// 获取下一个账号
function getNextAccount() {
    if (accountList.length === 0) {
        return null;
    }

    const nextIndex = (currentAccountIndex + 1) % accountList.length;
    return accountList[nextIndex];
}

// 切换到下一个账号
function switchToNextAccount() {
    if (accountList.length === 0) {
        utils.log("ACCOUNT: 没有可用的账号进行切换");
        return false;
    }

    if (isAccountSwitching) {
        utils.log("ACCOUNT: 正在切换账号中，请稍候...");
        return false;
    }

    isAccountSwitching = true;

    try {
        const oldIndex = currentAccountIndex;
        currentAccountIndex = (currentAccountIndex + 1) % accountList.length;

        // 保存当前索引
        storage.put("current_account_index", currentAccountIndex);

        const oldAccount = accountList[oldIndex];
        const newAccount = accountList[currentAccountIndex];

        utils.log("ACCOUNT: 准备切换账号 - 从 " + oldAccount.username + " 切换到 " + newAccount.username);

        // 执行账号切换流程
        const switchResult = performAccountSwitch(newAccount);

        if (switchResult) {
            utils.log("ACCOUNT: 账号切换成功 - 当前账号: " + newAccount.username);
            return true;
        } else {
            // 切换失败，回滚索引
            currentAccountIndex = oldIndex;
            storage.put("current_account_index", oldIndex);
            utils.log("ACCOUNT: 账号切换失败，已回滚到原账号");
            return false;
        }
    } catch (e) {
        utils.log("ERROR: ACCOUNT: 账号切换过程中发生错误: " + e.toString());
        return false;
    } finally {
        isAccountSwitching = false;
    }
}

// 执行账号切换流程
function performAccountSwitch(newAccount) {
    try {
        utils.log("ACCOUNT: 开始执行账号切换流程...");

        // 1. 关闭小红书app
        utils.log("ACCOUNT: 步骤1 - 关闭小红书app");
        if (!closeXhsApp()) {
            throw new Error("关闭小红书app失败");
        }

        sleep(2000); // 等待app完全关闭

        // 2. 重新启动小红书app
        utils.log("ACCOUNT: 步骤2 - 重新启动小红书app");
        if (!startXhsApp()) {
            throw new Error("启动小红书app失败");
        }

        sleep(3000); // 等待app启动

        // 3. 执行退出登录
        utils.log("ACCOUNT: 步骤3 - 执行退出登录");
        if (!performLogout()) {
            throw new Error("退出登录失败");
        }

        // 4. 执行新账号登录
        utils.log("ACCOUNT: 步骤4 - 登录新账号: " + newAccount.username);
        if (!performLogin(newAccount)) {
            throw new Error("登录新账号失败");
        }

        utils.log("ACCOUNT: 账号切换流程完成");
        return true;

    } catch (e) {
        utils.log("ERROR: ACCOUNT: 账号切换流程失败: " + e.toString());
        return false;
    }
}

// 关闭小红书app
function closeXhsApp() {
    try {
        utils.log("ACCOUNT: 正在关闭小红书app...");

        // 使用shell命令强制关闭小红书
        shell("am force-stop com.xingin.xhs", true);
        sleep(1000);

        // 验证是否关闭成功
        const isRunning = shell("pidof com.xingin.xhs", true).code === 0;
        if (isRunning) {
            utils.log("ACCOUNT: 使用备用方法关闭app...");
            // 备用方法：使用home键然后清理后台
            home();
            sleep(500);
            recents();
            sleep(1000);
            // 尝试清理小红书
            swipe(device.width / 2, device.height / 2, device.width / 2, 0, 300);
            sleep(500);
        }

        utils.log("ACCOUNT: 小红书app已关闭");
        return true;
    } catch (e) {
        utils.log("ERROR: ACCOUNT: 关闭小红书app失败: " + e.toString());
        return false;
    }
}

// 启动小红书app
function startXhsApp() {
    try {
        utils.log("ACCOUNT: 正在启动小红书app...");

        // 启动小红书
        launch("com.xingin.xhs");
        sleep(3000);

        // 等待app完全加载
        const maxWaitTime = 10000; // 最大等待10秒
        const startTime = Date.now();

        while (Date.now() - startTime < maxWaitTime) {
            if (currentPackage() === "com.xingin.xhs") {
                utils.log("ACCOUNT: 小红书app启动成功");
                return true;
            }
            sleep(500);
        }

        throw new Error("小红书app启动超时");
    } catch (e) {
        utils.log("ERROR: ACCOUNT: 启动小红书app失败: " + e.toString());
        return false;
    }
}

// 执行退出登录
function performLogout() {
    try {
        utils.log("ACCOUNT: 开始执行退出登录流程...");

        // 等待页面加载完成
        sleep(2000);

        // 1. 点击左上角设置按钮
        utils.log("ACCOUNT: 点击左上角设置按钮");
        const settingsBtn = id("com.xingin.xhs:id/i48").findOne(5000);
        if (!settingsBtn) {
            throw new Error("找不到设置按钮");
        }
        settingsBtn.click();
        sleep(1000);

        // 2. 在弹出的设置弹出层中查找并点击底部的"设置"按钮
        utils.log("ACCOUNT: 在设置弹出层中查找底部设置按钮");

        // 等待弹出层完全显示
        sleep(1000);

        // 查找desc="设置"的可点击父容器
        let bottomSettingsBtn = null;

        utils.log("ACCOUNT: 查找desc='设置'的可点击元素");
        bottomSettingsBtn = desc("设置").clickable(true).findOne(3000);

        if (bottomSettingsBtn) {
            utils.log("ACCOUNT: 找到desc='设置'的可点击元素");
        } else {
            // 备用方案：查找所有desc="设置"的元素，找到可点击的
            utils.log("ACCOUNT: 备用方案：查找所有desc='设置'的元素");
            const allSettingsElements = desc("设置").find();
            utils.log("ACCOUNT: 找到 " + allSettingsElements.length + " 个desc='设置'的元素");

            for (let i = 0; i < allSettingsElements.length; i++) {
                const element = allSettingsElements[i];
                if (element.clickable()) {
                    bottomSettingsBtn = element;
                    utils.log("ACCOUNT: 找到第" + (i + 1) + "个可点击的设置元素");
                    break;
                }
            }
        }

        if (!bottomSettingsBtn) {
            throw new Error("在设置弹出层中找不到底部设置按钮");
        }

        utils.log("ACCOUNT: 点击底部设置按钮进入设置页面");
        bottomSettingsBtn.click();
        sleep(2000);

        // 验证是否真正进入了设置页面
        utils.log("ACCOUNT: 验证是否成功进入设置页面");
        let isInSettingsPage = false;

        // 等待页面加载并验证
        for (let i = 0; i < 5; i++) {
            sleep(1000);

            // 检查是否还在弹出层（如果还能找到弹出层元素，说明没有进入设置页面）
            const stillInPopup = id("com.xingin.xhs:id/g10").exists();
            if (stillInPopup) {
                utils.log("ACCOUNT: 第" + (i + 1) + "次检查：仍在弹出层，未进入设置页面");
                continue;
            }

            // 检查是否在设置页面（通过查找设置页面特有的元素）
            const settingsPageIndicator = textContains("账号与安全").exists() ||
                textContains("隐私设置").exists() ||
                textContains("通用设置").exists() ||
                textContains("关于小红书").exists();

            if (settingsPageIndicator) {
                utils.log("ACCOUNT: 第" + (i + 1) + "次检查：成功进入设置页面");
                isInSettingsPage = true;
                break;
            }

            utils.log("ACCOUNT: 第" + (i + 1) + "次检查：页面状态不明确，继续等待");
        }

        if (!isInSettingsPage) {
            throw new Error("点击设置按钮后未能成功进入设置页面，可能点击失败或页面加载异常");
        }

        // 3. 在设置页面滑动到底部查找退出登录按钮
        utils.log("ACCOUNT: 确认在设置页面，开始滑动查找退出登录按钮");

        // 直接开始滑动查找退出登录按钮，不在顶部查找
        utils.log("ACCOUNT: 开始滑动到底部查找退出登录按钮");
        let logoutBtn = null;

        // 多次滑动到底部查找"退出登录"文本
        for (let i = 0; i < 8; i++) {
            utils.log("ACCOUNT: 第" + (i + 1) + "次滑动");

            // 向下滑动，滑动距离更大
            swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.3, 800);
            sleep(800);

            // 通过文本查找"退出登录"
            const logoutText = text("退出登录").findOne(1000);
            if (logoutText) {
                utils.log("ACCOUNT: 第" + (i + 1) + "次滑动后找到'退出登录'文本");

                // 方法1: 尝试直接点击文本元素
                if (logoutText.clickable()) {
                    utils.log("ACCOUNT: 退出登录文本可点击，直接使用");
                    logoutBtn = logoutText;
                    break;
                }

                // 方法2: 查找文本的父元素
                let parent = logoutText.parent();
                while (parent && !parent.clickable()) {
                    parent = parent.parent();
                    if (!parent) break;
                }

                if (parent && parent.clickable()) {
                    utils.log("ACCOUNT: 找到退出登录文本的可点击父元素");
                    logoutBtn = parent;
                    break;
                }

                // 方法3: 通过文本位置查找附近的可点击元素
                const textBounds = logoutText.bounds();
                utils.log("ACCOUNT: 退出登录文本位置: " + textBounds.centerX() + "," + textBounds.centerY());

                // 查找所有ebt元素，找到位置匹配的
                const allEbtElements = id("com.xingin.xhs:id/ebt").find();
                for (let j = 0; j < allEbtElements.length; j++) {
                    const btn = allEbtElements[j];
                    const btnBounds = btn.bounds();
                    if (Math.abs(btnBounds.centerY() - textBounds.centerY()) < 100) {
                        utils.log("ACCOUNT: 找到位置匹配的ebt按钮");
                        logoutBtn = btn;
                        break;
                    }
                }

                if (logoutBtn) break;
            }

            // 检查是否已经滑动到底部
            if (textContains("已经到底了").exists() || textContains("没有更多").exists()) {
                utils.log("ACCOUNT: 已滑动到底部，但未找到退出登录按钮");
                break;
            }
        }

        // 最终检查并点击退出登录按钮
        if (logoutBtn) {
            utils.log("ACCOUNT: 找到退出登录按钮，点击");
            logoutBtn.click();
            sleep(1500);
        } else {
            // 备用方案：通过文本查找退出登录
            utils.log("ACCOUNT: 通过ID未找到，尝试通过文本查找退出登录按钮");

            // 再次滑动并通过文本查找
            for (let i = 0; i < 3; i++) {
                const logoutTextBtn = textContains("退出登录").findOne(1000);
                if (logoutTextBtn) {
                    utils.log("ACCOUNT: 通过文本找到退出登录按钮，点击");
                    logoutTextBtn.click();
                    sleep(1500);
                    logoutBtn = true; // 标记为找到
                    break;
                }

                // 继续滑动
                swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.3, 800);
                sleep(800);
            }

            if (!logoutBtn) {
                throw new Error("通过ID和文本都未找到退出登录按钮");
            }
        }

        // 4. 确认退出登录
        utils.log("ACCOUNT: 确认退出登录");
        const confirmLogoutBtn = id("com.xingin.xhs:id/h0g").findOne(3000);
        if (!confirmLogoutBtn) {
            throw new Error("找不到确认退出按钮");
        }
        confirmLogoutBtn.click();
        sleep(2000);

        utils.log("ACCOUNT: 退出登录完成");
        return true;

    } catch (e) {
        utils.log("ERROR: ACCOUNT: 退出登录失败: " + e.toString());
        return false;
    }
}

// 执行新账号登录
function performLogin(account) {
    try {
        utils.log("ACCOUNT: 开始登录账号: " + account.username);

        // 等待登录页面加载
        sleep(2000);

        // 1. 点击其它方式登录按钮
        utils.log("ACCOUNT: 点击其它方式登录按钮");
        const otherLoginBtn = id("com.xingin.xhs:id/gil").findOne(5000);
        if (!otherLoginBtn) {
            throw new Error("找不到其它方式登录按钮");
        }
        otherLoginBtn.click();
        sleep(1000);

        // 2. 点击手机号登录按钮
        utils.log("ACCOUNT: 点击手机号登录按钮");
        const phoneLoginBtn = id("com.xingin.xhs:id/axu").findOne(3000);
        if (!phoneLoginBtn) {
            throw new Error("找不到手机号登录按钮");
        }
        phoneLoginBtn.click();
        sleep(2000);

        // 2.1. 检查是否进入了一键登录页面
        utils.log("ACCOUNT: 检查是否进入一键登录页面");
        const oneClickLoginIndicator = text("本机号码可一键登录").findOne(2000);
        if (oneClickLoginIndicator) {
            utils.log("ACCOUNT: 检测到一键登录页面，点击其它号码登录");
            const otherNumberBtn = id("com.xingin.xhs:id/gil").findOne(3000);
            if (!otherNumberBtn) {
                throw new Error("在一键登录页面找不到其它号码登录按钮");
            }
            otherNumberBtn.click();
            sleep(2000);
            utils.log("ACCOUNT: 已从一键登录页面跳转到手机号密码登录页面");
        } else {
            utils.log("ACCOUNT: 直接进入手机号密码登录页面");
        }

        // 3. 点击用户名输入框并输入手机号
        utils.log("ACCOUNT: 点击用户名输入框并输入手机号: " + account.username);
        const usernameInput = id("com.xingin.xhs:id/f9j").findOne(3000);
        if (!usernameInput) {
            throw new Error("找不到用户名输入框");
        }
        usernameInput.click();
        sleep(500);
        usernameInput.setText(account.username);
        sleep(500);

        // 4. 点击密码登录按钮
        utils.log("ACCOUNT: 点击密码登录按钮");
        const passwordLoginBtn = id("com.xingin.xhs:id/iho").findOne(3000);
        if (!passwordLoginBtn) {
            throw new Error("找不到密码登录按钮");
        }
        passwordLoginBtn.click();
        sleep(2000); // 等待密码输入框出现

        // 5. 点击密码输入框并输入密码
        utils.log("ACCOUNT: 点击密码输入框并输入密码");
        const passwordInput = id("com.xingin.xhs:id/f_5").findOne(3000);
        if (!passwordInput) {
            throw new Error("找不到密码输入框");
        }
        passwordInput.click();
        sleep(500);
        passwordInput.setText(account.password);
        sleep(3000); // 等待3秒确保密码输入完成

        // 6. 勾选用户协议
        utils.log("ACCOUNT: 勾选用户协议");
        const agreementCheckbox = id("com.xingin.xhs:id/gws").findOne(3000);
        if (agreementCheckbox && !agreementCheckbox.checked()) {
            agreementCheckbox.click();
            sleep(500);
        }

        // 7. 点击登录按钮
        utils.log("ACCOUNT: 点击登录按钮");
        const loginBtn = id("com.xingin.xhs:id/f9l").findOne(3000);
        if (!loginBtn) {
            throw new Error("找不到登录按钮");
        }
        loginBtn.click();

        // 8. 等待登录完成
        utils.log("ACCOUNT: 等待登录完成...");
        const maxWaitTime = 15000; // 最大等待15秒
        const startTime = Date.now();

        while (Date.now() - startTime < maxWaitTime) {
            // 检查是否登录成功（通过检查是否回到主页面）
            if (id("com.xingin.xhs:id/i48").exists()) {
                utils.log("ACCOUNT: 登录成功");
                return true;
            }

            // 检查是否有错误提示
            const errorText = textContains("密码错误").findOne(500);
            if (errorText) {
                throw new Error("密码错误");
            }

            const accountErrorText = textContains("账号不存在").findOne(500);
            if (accountErrorText) {
                throw new Error("账号不存在");
            }

            sleep(1000);
        }

        throw new Error("登录超时");

    } catch (e) {
        utils.log("ERROR: ACCOUNT: 登录失败: " + e.toString());
        return false;
    }
}

// 获取账号状态信息
function getAccountStatus() {
    const current = getCurrentAccount();
    const next = getNextAccount();

    return {
        totalAccounts: accountList.length,
        currentIndex: currentAccountIndex,
        currentAccount: current ? current.username : "无",
        nextAccount: next ? next.username : "无",
        isAccountSwitching: isAccountSwitching
    };
}

// 检查是否需要切换账号
function shouldSwitchAccount(currentCommentCount, maxCommentsPerAccount) {
    if (accountList.length <= 1) {
        // 只有一个或没有账号，不需要切换
        return false;
    }

    if (maxCommentsPerAccount <= 0) {
        // 没有评论限制，不需要切换
        return false;
    }

    return currentCommentCount >= maxCommentsPerAccount;
}

// 重置当前账号的评论计数（用于账号切换后）
function resetCurrentAccountCommentCount() {
    try {
        const safetyControl = require('./xhs_safety_control.js');
        if (safetyControl && typeof safetyControl.resetDailyCount === 'function') {
            safetyControl.resetDailyCount();
            utils.log("ACCOUNT: 已重置当前账号的评论计数");
            return true;
        }
    } catch (e) {
        utils.log("ERROR: ACCOUNT: 重置评论计数失败: " + e.toString());
    }
    return false;
}

// Auto.js兼容的导出方式
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        initAccountManager: initAccountManager,
        setAccountList: setAccountList,
        getCurrentAccount: getCurrentAccount,
        getNextAccount: getNextAccount,
        switchToNextAccount: switchToNextAccount,
        closeXhsApp: closeXhsApp,
        startXhsApp: startXhsApp,
        performLogout: performLogout,
        performLogin: performLogin,
        getAccountStatus: getAccountStatus,
        shouldSwitchAccount: shouldSwitchAccount,
        resetCurrentAccountCommentCount: resetCurrentAccountCommentCount
    };
} else {
    // Auto.js环境 - 使用全局导出
    this.initAccountManager = initAccountManager;
    this.setAccountList = setAccountList;
    this.getCurrentAccount = getCurrentAccount;
    this.getNextAccount = getNextAccount;
    this.switchToNextAccount = switchToNextAccount;
    this.closeXhsApp = closeXhsApp;
    this.startXhsApp = startXhsApp;
    this.performLogout = performLogout;
    this.performLogin = performLogin;
    this.getAccountStatus = getAccountStatus;
    this.shouldSwitchAccount = shouldSwitchAccount;
    this.resetCurrentAccountCommentCount = resetCurrentAccountCommentCount;
}

utils.log("ACCOUNT: 小红书账号管理模块 (xhs_account_manager.js) 加载完毕");
