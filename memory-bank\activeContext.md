# 任务：增强LLM评论功能 - 实现提示词模板管理 (第七轮迭代)

## 父任务背景 (由NexusCore定义)
先前已完成LLM评论生成的基础功能集成（第六轮迭代），包括UI配置（API URL, 模型, 单一提示词, Temperature, Max Tokens）及核心调用逻辑。
当前任务是在此基础上，进一步增强LLM提示词的配置灵活性，允许用户管理和选择多个提示词模板。

---
## 当前子任务（LLM提示词模板管理功能实现 - 委托给“自动编码器”）

**总体目标：**
在现有的“AI模型设置”UI区域内，实现一个提示词模板管理系统，允许用户创建、选择和使用多个不同的提示词模板。

**详细需求与实现指令：**

**I. 配置数据结构变更 (<code>[`config.js`](config.js:0)</code>)**

1.  **修改配置项以支持模板列表：**
    *   分析现有的 `config.js` 中 `llmPrompt` (原用于存储单一提示词字符串) 的加载和保存方式。
    *   将其修改为一个**对象数组**，命名为 `llmPromptTemplates`。
    *   每个对象代表一个模板，至少包含 `name` (字符串，模板名称，用户自定义) 和 `content` (字符串，提示词实际内容) 两个字段。
        *   示例：`[{name: "通用友好型", content: "请针对以下内容生成一条友好且相关的评论：\n{笔记内容}"}, {name: "专业分析型", content: "请针对以下内容进行专业分析并给出评论要点：\n{笔记内容}"}]`
    *   添加一个新的配置项 `selectedLlmPromptTemplateName` (字符串)，用于存储当前选中的模板的 `name`。
    *   **默认值处理：**
        *   `llmPromptTemplates`: 在 `loadConfig` 中，如果 `storage.get("llmPromptTemplates")` 不存在或解析失败，应初始化为一个包含至少一个默认模板的数组，例如 `[{name: "默认模板", content: "请针对以下内容生成评论：\n{笔记内容}"}]`。
        *   `selectedLlmPromptTemplateName`: 在 `loadConfig` 中，如果 `storage.get("selectedLlmPromptTemplateName")` 不存在，或者其值在加载的 `llmPromptTemplates` 中找不到对应的 `name`，则应默认设置为第一个可用模板的 `name` (如果 `llmPromptTemplates` 不为空)。
    *   更新 <code>[`config.js`](config.js:0)</code> 中的 `loadConfig` 和 `saveConfig` (或等效函数) 逻辑：
        *   `llmPromptTemplates` 数组在存入 `storage` 前应使用 `JSON.stringify()`，在从 `storage` 取出后应使用 `JSON.parse()`。添加必要的 `try-catch` 以处理可能的解析错误，并在出错时回退到默认值。
        *   `selectedLlmPromptTemplateName` 作为普通字符串存取。

**II. UI界面修改 (<code>[`ui.js`](ui.js:0)</code> - “AI模型设置”区域)**

1.  **替换原单一提示词输入框：**
    *   找到并移除之前用于单一 `llmPrompt` 的多行文本输入框。
2.  **新增提示词模板选择UI：**
    *   在该区域（或其他合适位置）添加一个ID为 `llmPromptTemplateSpinner` 的 `<spinner/>` 控件。此Spinner将用于显示和选择可用的提示词模板名称。
    *   在Spinner下方或旁边，添加一个ID为 `selectedLlmPromptContent` 的多行文本显示区域（例如，使用 `<text multiline="true" textSize="14sp" h="100dp" scrollbars="vertical"/>` 或一个 `editable="false"` 的 `<input/>`），用于只读显示当前Spinner选中模板的完整 `content`。
3.  **新增“管理提示词模板”功能区 (实现方案A - 简单方案)：**
    *   在上述模板选择UI之后，添加以下控件：
        *   `<text text="管理模板:" style="sectionHeader"/>` (或类似标题)
        *   `<input id="newPromptTemplateName" hint="新模板名称"/>`
        *   `<input id="newPromptTemplateContent" hint="新模板内容 (使用 {笔记内容} 代表笔记正文)" inputType="textMultiLine" lines="3" h="80dp"/>`
        *   `<button id="addLlmPromptTemplateBtn" text="添加/更新此模板"/>`
        *   `<button id="deleteLlmPromptTemplateBtn" text="删除选中模板" style="Widget.AppCompat.Button.Colored" color="#FF0000"/>` (可选颜色)

4.  **UI交互逻辑实现：**
    *   **`populateTemplateSpinner()` 函数：**
        *   创建一个名为 `populateTemplateSpinner()` 的函数，或在现有UI更新函数中集成此逻辑。
        *   该函数读取 `currentConfig.llmPromptTemplates` (从 <code>[`config.js`](config.js:0)</code> 加载的配置)。
        *   提取所有模板对象的 `name` 属性，生成一个名称数组。
        *   使用 `ui.llmPromptTemplateSpinner.setEntries(templateNamesArray);` 更新Spinner的选项。
        *   尝试找到 `currentConfig.selectedLlmPromptTemplateName` 在 `templateNamesArray` 中的索引，并设置Spinner的选中项 (`ui.llmPromptTemplateSpinner.setSelection(index);`)。如果找不到或 `selectedLlmPromptTemplateName` 为空，则默认选中第一个模板（如果列表不为空）。
    *   **`updateSelectedPromptContentDisplay()` 函数：**
        *   创建一个名为 `updateSelectedPromptContentDisplay()` 的函数。
        *   获取 `ui.llmPromptTemplateSpinner.getSelectedItemPosition()` 以及 `currentConfig.llmPromptTemplates`。
        *   根据选中的位置，从 `llmPromptTemplates` 数组中找到对应的模板对象。
        *   将该模板对象的 `content` 设置到 `ui.selectedLlmPromptContent.setText()`。如果模板列表为空或未选中任何项，则清空 `selectedLlmPromptContent`。
    *   **Spinner `onItemSelected` 监听器 (附加到 `ui.llmPromptTemplateSpinner`):**
        *   当用户在Spinner中选择一个项目时：
            1.  调用 `updateSelectedPromptContentDisplay()` 来更新内容显示。
            2.  获取当前选中的模板名称。
            3.  更新 `currentConfig.selectedLlmPromptTemplateName` 为这个选中的名称。
            4.  调用 `config.saveCurrentConfig(currentConfig);` (或通过事件触发全局保存)。
    *   **`addLlmPromptTemplateBtn` 点击监听器：**
        *   获取 `ui.newPromptTemplateName.getText().toString().trim()` 和 `ui.newPromptTemplateContent.getText().toString().trim()`。
        *   校验模板名称 (`newName`) 是否为空。如果为空，用 `toast` 提示用户“模板名称不能为空”并返回。
        *   在 `currentConfig.llmPromptTemplates` 中查找是否已存在同名模板。
            *   如果存在，则更新该模板对象的 `content` 为 `newContent`。
            *   如果不存在，则创建一个新的模板对象 `{name: newName, content: newContent}` 并将其 `push` 到 `currentConfig.llmPromptTemplates` 数组中。
        *   更新 `currentConfig.selectedLlmPromptTemplateName = newName;` (使新增/更新的模板成为当前选中)。
        *   调用 `populateTemplateSpinner()` 刷新Spinner并自动选中新项。
        *   (由于Spinner选中会自动触发 `onItemSelected`，`updateSelectedPromptContentDisplay` 会被调用)。
        *   清空 `ui.newPromptTemplateName` 和 `ui.newPromptTemplateContent` 的文本。
        *   调用 `config.saveCurrentConfig(currentConfig);`。
    *   **`deleteLlmPromptTemplateBtn` 点击监听器：**
        *   获取 `ui.llmPromptTemplateSpinner.getSelectedItemPosition()` 和选中的模板名称。
        *   如果Spinner中没有选中项 (例如列表为空或位置为-1)，则用 `toast` 提示“请先选择一个模板以删除”并返回。
        *   从 `currentConfig.llmPromptTemplates` 数组中移除与选中名称匹配的模板对象。
        *   如果删除后 `currentConfig.llmPromptTemplates` 为空，则将 `currentConfig.selectedLlmPromptTemplateName` 设置为空字符串。否则，将 `currentConfig.selectedLlmPromptTemplateName` 设置为剩余模板中的第一个模板的名称（如果有）。
        *   调用 `populateTemplateSpinner()` 刷新Spinner。
        *   (Spinner刷新后，`onItemSelected` 会自动触发，进而调用 `updateSelectedPromptContentDisplay`)。
        *   调用 `config.saveCurrentConfig(currentConfig);`。

**III. UI与配置同步更新 (<code>[`ui.js`](ui.js:0)</code>, <code>[`config.js`](config.js:0)</code>)**

1.  **`updateUiFromConfig(loadedConfig)` (或等效函数) 修改：**
    *   在函数开始部分，确保 `loadedConfig.llmPromptTemplates` 如果不存在或不是数组，则初始化为一个包含默认模板的数组（如 <code>[`config.js`](config.js:0)</code> 中的定义）。
    *   确保 `loadedConfig.selectedLlmPromptTemplateName` 有合理的初始值（如默认模板名称或列表第一项）。
    *   在设置完其他LLM配置（API URL, Model, Temp, Max Tokens）后，调用 `populateTemplateSpinner()`。 （由于Spinner的`onItemSelected`会触发`updateSelectedPromptContentDisplay`，所以不需要在这里直接调用后者，除非Spinner初始化时没有选中项）。
2.  **`buildTaskConfig()` (或等效函数，在启动任务时从UI收集配置) 修改：**
    *   移除对旧的 `llmPrompt` 的读取。
    *   收集 `currentConfig.llmPromptTemplates` (应从 <code>[`config.js`](config.js:0)</code> 的 `currentConfig` 对象获取，该对象由UI操作实时更新并保存) 并存入传递给 `main.js` 的任务配置对象中，键名为 `llmPromptTemplates`。
    *   收集 `currentConfig.selectedLlmPromptTemplateName` (同样从 <code>[`config.js`](config.js:0)</code> 的 `currentConfig` 获取) 并存入任务配置对象，键名为 `selectedLlmPromptTemplateName`。

**IV. 主流程逻辑调整 (<code>[`main.js`](main.js:0)</code>)**

1.  **使用选定的提示词模板：**
    *   在 `runSearchAndCommentTask` 和 `runCommentSingleNoteTask` 函数中，当需要调用 `llmService.generateCommentWithLLM` 时：
        *   从 `currentTaskConfig` (由 `buildTaskConfig` 构建) 中获取 `selectedLlmPromptTemplateName` (字符串) 和 `llmPromptTemplates` (模板对象数组)。
        *   在 `llmPromptTemplates` 数组中查找 `name` 属性与 `selectedLlmPromptTemplateName` 匹配的模板对象。
        *   **如果找到模板对象：**
            *   则使用该模板对象的 `content` 属性作为 `userPrompt` 参数传递给 `llmService.generateCommentWithLLM`。
        *   **如果未找到模板对象** (例如 `selectedLlmPromptTemplateName` 无效，或 `llmPromptTemplates` 数组为空或不包含该名称)：
            *   记录一条警告日志，例如 "MAIN: 未找到名为 '...' 的LLM提示词模板，或模板列表为空。将跳过AI评论。"
            *   将 `commentTextsToUse` 设置为空数组 `[]`，以确保后续逻辑跳过对此笔记的评论。
    *   (原有的关于内容提取成功与否、LLM调用成功与否的逻辑保持不变，但现在它们操作的前提是首先要成功获取到 `userPrompt` จาก模板。)

**V. LLM服务模块 (<code>[`llm_service.js`](llm_service.js:0)</code>)**
*   `generateCommentWithLLM` 函数的签名 (接收 `userPrompt` 参数) 和核心API调用逻辑保持不变，因为它已经设计为接收提示词内容。

**请严格按照以上所有详细指令进行开发。务必确保UI的交互顺畅，配置的保存和加载正确无误，以及主流程能正确使用选定的提示词模板。请在此 <code>[`memory-bank/activeContext.md`](memory-bank/activeContext.md:0)</code> 文件中详细记录所有代码创建和修改的步骤、遇到的问题及解决方案。**