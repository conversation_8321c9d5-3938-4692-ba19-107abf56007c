/**
 * 小红书视频笔记处理模块
 * 负责视频笔记的业务统一处理：评论发布、用户信息采集、点赞、返回搜索结果页
 */

const utils = require('./utils.js');
const simpleComments = require('./xhs_simple_comments.js');
const userProfile = require('./xhs_user_profile.js');
const configManager = require('./config.js');
const noteCommentingModule = require('./xhs_note_commenting.js');
const xhsActions = require('./xhs_actions.js');

/**
 * 视频笔记业务统一处理函数
 * @param {string} noteTitle - 笔记标题
 * @param {string} noteAuthor - 笔记作者
 * @returns {boolean} - 处理是否成功
 */
function processVideoNote(noteTitle, noteAuthor) {
    utils.log("VIDEO_PROCESS: 开始处理视频笔记: " + noteTitle);

    try {
        const config = configManager.loadConfig();
        var processSuccess = false;

        // 调试：输出配置信息
        utils.log("VIDEO_PROCESS: 配置信息 - task_comment_notes: " + config.task_comment_notes);
        utils.log("VIDEO_PROCESS: 配置信息 - enableLlmComments: " + config.enableLlmComments);
        utils.log("VIDEO_PROCESS: 配置信息 - task_collect_users: " + config.task_collect_users);
        utils.log("VIDEO_PROCESS: 配置信息 - task_like_users: " + config.task_like_users);

        // 1. 评论发布（预设评论或AI评论）- 与图文笔记保持一致的判断逻辑
        if (config.task_comment_notes || config.enableLlmComments) {
            utils.log("VIDEO_PROCESS: 开始评论发布...");
            var commentSuccess = noteCommentingModule.publishCommentInCurrentNote(
                noteTitle,
                noteAuthor || "Unknown Author",
                [] // 空数组，让评论模块根据配置决定使用AI评论还是预设评论
            );

            if (commentSuccess) {
                utils.log("VIDEO_PROCESS: ✓ 评论发布成功");
                processSuccess = true;
            } else {
                utils.log("VIDEO_PROCESS: ✗ 评论发布失败或跳过");
            }
        } else {
            utils.log("VIDEO_PROCESS: 评论功能未启用 (task_comment_notes: " + config.task_comment_notes + ", enableLlmComments: " + config.enableLlmComments + ")，跳过评论发布");
        }

        // 2. 用户信息采集（如果启用）- 与图文笔记保持一致的判断逻辑
        if (config.task_collect_users) {
            utils.log("VIDEO_PROCESS: 开始用户信息采集...");
            try {
                var collectedCount = collectVideoNoteComments(
                    noteTitle,
                    config.commentKeywords || "",
                    config.targetRegion || 0,
                    config.task_like_users || false
                );
                utils.log("VIDEO_PROCESS: ✓ 用户信息采集完成，采集到 " + collectedCount + " 条评论");
                processSuccess = true;
            } catch (collectionError) {
                utils.log("VIDEO_PROCESS: ✗ 用户信息采集失败: " + collectionError);
            }
        } else {
            utils.log("VIDEO_PROCESS: 用户信息采集功能未启用 (task_collect_users: " + config.task_collect_users + ")，跳过");
        }

        // 3. 点赞功能（如果启用且未在采集中处理）- 与图文笔记保持一致的判断逻辑
        if (config.task_like_users && !config.task_collect_users) {
            utils.log("VIDEO_PROCESS: 开始独立点赞操作...");
            // 这里可以添加独立的点赞逻辑
            // 目前点赞功能集成在用户信息采集中
        }

        // 4. 返回搜索结果页
        utils.log("VIDEO_PROCESS: 返回搜索结果页...");
        const noteTypes = require('./xhs_note_types.js');
        const currentNoteType = noteTypes.NOTE_TYPE_VIDEO;

        var backSuccess = xhsActions.backToPreviousPage(currentNoteType);
        if (!backSuccess) {
            utils.log("VIDEO_PROCESS: 标准返回失败，使用备用方法");
            back();
            sleep(1000);
        }

        utils.log("VIDEO_PROCESS: 视频笔记处理完成，成功: " + processSuccess);
        return processSuccess;

    } catch (error) {
        utils.log("VIDEO_PROCESS: 视频笔记处理异常: " + error);

        // 异常情况下也要尝试返回
        try {
            back();
            sleep(1000);
        } catch (backError) {
            utils.log("VIDEO_PROCESS: 异常返回也失败: " + backError);
        }

        return false;
    }
}

/**
 * 采集视频笔记的评论
 * @param {string} noteTitle - 笔记标题
 * @param {string} keywordsString - 关键词字符串
 * @param {number} targetRegionIndex - 目标区域索引（0表示不限）
 * @param {boolean} enableLiking - 是否启用点赞功能
 * @returns {Promise<number>} 采集到的评论数量
 */
function collectVideoNoteComments(noteTitle, keywordsString, targetRegionIndex, enableLiking) {
    // 设置默认值
    if (typeof targetRegionIndex === 'undefined') {
        targetRegionIndex = 0;
    }
    if (typeof enableLiking === 'undefined') {
        enableLiking = false;
    }

    utils.log("VIDEO_COMMENTS: 开始采集视频笔记评论...");
    utils.log("VIDEO_COMMENTS: 目标区域: " + utils.getRegionNameByIndex(targetRegionIndex));

    return new Promise(function (resolve) {
        try {
            utils.log("VIDEO_COMMENTS: 尝试点击进入视频评论区...");

            const c9yContainerId = "com.xingin.xhs:id/c9y";
            const commentLayerIndexInC9y = 1; // 假设评论层是 c9y 的第二个子元素 (索引0是点赞，1是评论等)
            // !!! 这个索引值你需要通过布局分析工具精确确定 !!!

            var c9yContainer = id(c9yContainerId).findOne(3000);
            if (!c9yContainer) {
                utils.log("ERROR: VIDEO_COMMENTS: 未找到评论按钮的父容器 (ID: " + c9yContainerId + ")");
                resolve(0);
                return;
            }
            utils.log("VIDEO_COMMENTS: 成功定位到容器 ID: " + c9yContainerId);

            // 直接获取 c9y 容器的特定子元素
            // 使用 UiObject.child(index) 或 UiObject.children().get(index)
            let commentButton = null;
            const childCount = c9yContainer.childCount();
            utils.log("VIDEO_COMMENTS: 容器 " + c9yContainerId + " 有 " + childCount + " 个直接子元素。");

            if (childCount > commentLayerIndexInC9y) {
                // 获取 c9y 的第 commentLayerIndexInC9y 个直接子元素
                // 这个子元素应该就是代表“评论”操作的那个 LinearLayout 层
                commentButton = c9yContainer.child(commentLayerIndexInC9y);

                if (commentButton) {
                    utils.log("VIDEO_COMMENTS: ✓ 成功获取索引为 " + commentLayerIndexInC9y + " 的直接子元素。 ClassName: " + commentButton.className() + ", Clickable: " + commentButton.clickable() + ", BoundsTop: " + commentButton.bounds().top);
                    // 这里可以加一步验证，比如检查这个 commentButton 的 className 是否是 LinearLayout
                    if (commentButton.className() !== "android.widget.LinearLayout") {
                        utils.log("WARN: VIDEO_COMMENTS: 索引 " + commentLayerIndexInC9y + " 的子元素 ClassName 不是预期的 LinearLayout，而是 " + commentButton.className());
                        // 如果不是预期的，可能索引错了，或者布局变了
                    }
                } else {
                    // child() 返回 null 的情况比较少见，除非索引越界，但我们已经检查了 childCount
                    utils.log("ERROR: VIDEO_COMMENTS: c9yContainer.child(" + commentLayerIndexInC9y + ") 返回了 null。");
                }
            } else {
                utils.log("ERROR: VIDEO_COMMENTS: 容器 " + c9yContainerId + " 的子元素数量 (" + childCount + ") 不足以获取索引 " + commentLayerIndexInC9y + "。");
            }


            if (!commentButton) {
                utils.log("ERROR: VIDEO_COMMENTS: 未能通过直接子元素索引 " + commentLayerIndexInC9y + " 找到评论按钮层。");
                resolve(0);
                return;
            }

            utils.log("VIDEO_COMMENTS: 找到评论按钮层，尝试点击...");
            // 通常，整个 LinearLayout 层是可点击的
            if (commentButton.clickable()) {
                if (!commentButton.click()) {
                    utils.log("ERROR: VIDEO_COMMENTS: 点击评论按钮层失败 (click() 返回 false)。");
                    resolve(0);
                    return;
                }
            } else {
                // 如果 LinearLayout 层本身不可点击，尝试点击它内部的 ImageView (更可靠) 或第一个可点击子元素
                utils.log("WARN: VIDEO_COMMENTS: 评论按钮层 (index " + commentLayerIndexInC9y + ") 本身不可点击。尝试查找其内部可点击元素...");
                let clickableInnerElement = commentButton.findOne(className("android.widget.ImageView").clickable(true)); // 优先找可点击的图片
                if (!clickableInnerElement) {
                    clickableInnerElement = commentButton.findOne(clickable(true)); // 再找任意可点击子元素
                }

                if (clickableInnerElement) {
                    utils.log("VIDEO_COMMENTS: 找到内部可点击元素: " + clickableInnerElement.className() + ", 尝试点击...");
                    if (!clickableInnerElement.click()) {
                        utils.log("ERROR: VIDEO_COMMENTS: 点击内部可点击元素失败。");
                        resolve(0);
                        return;
                    }
                } else {
                    utils.log("ERROR: VIDEO_COMMENTS: 评论按钮层不可点击，且内部也未找到可点击元素。");
                    resolve(0);
                    return;
                }
            }
            utils.log("VIDEO_COMMENTS: 点击操作已执行。");


            // 2. 等待评论区弹出
            utils.log("VIDEO_COMMENTS: 等待评论区弹出...");
            sleep(2500); // 稍微增加等待时间

            // 3. 验证评论区是否弹出 (你的后续逻辑)
            // ... (省略了你的评论区验证和采集逻辑，假设它们是正确的) ...
            // 示例：
            const commentSectionIndicator = id("com.xingin.xhs:id/ivx").textMatches(/^共\s*\d+\s*条评论$/).findOne(3000);
            if (!commentSectionIndicator) {
                utils.log("ERROR: VIDEO_COMMENTS: 评论区未弹出或指示元素不匹配。");
                // 尝试回退一下，以防点错了
                back();
                sleep(1000);
                resolve(0);
                return;
            }
            utils.log("VIDEO_COMMENTS: 评论区成功弹出，指示文本: \"" + commentSectionIndicator.text() + "\"");

            // 4. 验证评论区容器
            const commentSectionContainer = id("com.xingin.xhs:id/bb5").findOne(2000);
            if (!commentSectionContainer) {
                utils.log("ERROR: VIDEO_COMMENTS: 未找到评论区容器 (com.xingin.xhs:id/iwv)");
                resolve(0);
                return;
            }

            utils.log("VIDEO_COMMENTS: 评论区容器验证成功，开始采集评论...");

            // 5. 使用现有的评论采集逻辑（因为单条评论的结构和图文笔记相同）
            collectVideoComments(commentSectionContainer, noteTitle, keywordsString, targetRegionIndex, enableLiking)
                .then(function (commentCount) {
                    utils.log("VIDEO_COMMENTS: 视频笔记评论采集完成，共采集 " + commentCount + " 条评论");

                    // 6. 关闭视频评论弹窗
                    utils.log("VIDEO_COMMENTS: 关闭视频评论弹窗...");
                    const closeButton = id("com.xingin.xhs:id/b5b").findOne(2000);
                    if (closeButton && closeButton.clickable()) {
                        if (closeButton.click()) {
                            utils.log("VIDEO_COMMENTS: 评论弹窗关闭成功");
                            sleep(1000);
                        } else {
                            utils.log("WARNING: VIDEO_COMMENTS: 点击评论弹窗关闭按钮失败 (click() returned false)");
                        }
                    } else {
                        utils.log("WARNING: VIDEO_COMMENTS: 未找到或无法点击评论弹窗关闭按钮，尝试物理返回键...");
                        back();
                        sleep(1000);
                    }

                    resolve(commentCount);
                })
                .catch(function (error) {
                    utils.log("ERROR: VIDEO_COMMENTS: 视频笔记评论采集出错: " + error);

                    // 即使出错也要尝试关闭弹窗
                    try {
                        const closeButton = id("com.xingin.xhs:id/b5b").findOne(1000);
                        if (closeButton) {
                            closeButton.click();
                            sleep(1000);
                        }
                    } catch (e) {
                        // 忽略关闭弹窗的错误
                    }

                    resolve(0);
                });

        } catch (e) {
            utils.log("ERROR: VIDEO_COMMENTS: 视频笔记评论采集异常: " + e.name + " - " + e.message + "\nStack: " + e.stack);
            resolve(0);
        }
    });
}

/**
 * 专门的视频评论采集函数（在评论区容器内进行）
 * @param {string} noteTitle - 笔记标题
 * @param {string} keywordsString - 关键词字符串
 * @param {number} targetRegionIndex - 目标区域索引（0表示不限）
 * @param {boolean} enableLiking - 是否启用点赞功能
 * @returns {Promise<number>} 采集到的评论数量
 */
function collectVideoComments(passedCommentPopup, noteTitle, keywordsString, targetRegionIndex, enableLiking) {
    // 设置默认值
    if (typeof targetRegionIndex === 'undefined') {
        targetRegionIndex = 0;
    }
    if (typeof enableLiking === 'undefined') {
        enableLiking = false;
    }

    utils.log("VIDEO_COMMENTS: 开始在视频评论区容器内采集评论...");
    utils.log("VIDEO_COMMENTS: 目标区域: " + utils.getRegionNameByIndex(targetRegionIndex));

    return new Promise(function (resolve) {
        try {
            if (!passedCommentPopup) {
                utils.log("ERROR: VIDEO_COMMENTS: 传入的评论区弹窗对象(bb5)为null。");
                resolve(0);
                return;
            }
            utils.log("VIDEO_COMMENTS: 评论区主弹窗(bb5)已传入, ID: " + passedCommentPopup.id());

            // 步骤 1: 在 iwv 内部找到列表的直接父容器 bb5
            const listParentContainerId = "com.xingin.xhs:id/bb5";
            let listParentContainer = passedCommentPopup.findOne(id(listParentContainerId));

            if (!listParentContainer) {
                utils.log("ERROR: VIDEO_COMMENTS: 在弹窗(iwv)内未找到列表的直接父容器 (ID: " + listParentContainerId + ")。");
                let globalBb5 = id(listParentContainerId).findOne(500);
                if (globalBb5) {
                    utils.log("DEBUG: 全局找到了 bb5 (ID: " + listParentContainerId + "), Bounds: " + globalBb5.bounds() + ". 但它不在 bb5 内。");
                } else {
                    utils.log("DEBUG: 全局也未找到 bb5 (ID: " + listParentContainerId + ")。");
                }
                resolve(0);
                return;
            }
            utils.log("VIDEO_COMMENTS: 成功定位到列表的直接父容器 (ID: " + listParentContainerId + "), Class: " + listParentContainer.className());

            // 步骤 2: 在 bb5 内部找到可滚动的列表 (fph 或 bb6)
            const scrollableListId_fph = "com.xingin.xhs:id/fph";
            const scrollableListId_bb6 = "com.xingin.xhs:id/bb6";
            let scrollableCommentList = null;

            utils.log("VIDEO_COMMENTS: 尝试在父容器(bb5)内查找列表ID '" + scrollableListId_fph + "'...");
            scrollableCommentList = listParentContainer.findOne(id(scrollableListId_fph));
            if (scrollableCommentList) {
                utils.log("VIDEO_COMMENTS: 在父容器(bb5)内通过ID '" + scrollableListId_fph + "' 找到了可滚动列表.");
            } else {
                utils.log("VIDEO_COMMENTS: 未找到ID '" + scrollableListId_fph + "'，尝试ID '" + scrollableListId_bb6 + "'...");
                scrollableCommentList = listParentContainer.findOne(id(scrollableListId_bb6));
                if (scrollableCommentList) {
                    utils.log("VIDEO_COMMENTS: 在父容器(bb5)内通过ID '" + scrollableListId_bb6 + "' 找到了可滚动列表.");
                }
            }

            if (!scrollableCommentList) {
                utils.log("VIDEO_COMMENTS: 通过ID在父容器(bb5)内未找到列表，尝试通过类名 'androidx.recyclerview.widget.RecyclerView'...");
                scrollableCommentList = listParentContainer.findOne(className("androidx.recyclerview.widget.RecyclerView").scrollable(true));
                if (scrollableCommentList) {
                    utils.log("VIDEO_COMMENTS: 在父容器(bb5)内通过类名找到了一个可滚动的RecyclerView, ID: " + (scrollableCommentList.id() || '无ID') + ".");
                }
            }

            if (!scrollableCommentList) {
                utils.log("ERROR: VIDEO_COMMENTS: 在父容器(bb5)内最终也未能找到可滚动的评论列表。");
                resolve(0);
                return;
            }
            utils.log("VIDEO_COMMENTS: 评论区可滚动列表 (ID: " + (scrollableCommentList.id() || '无ID') + ", Class: " + scrollableCommentList.className() + ") 已定位.");

            // --- 后续的采集和滚动逻辑与你之前的版本基本一致 ---
            let collectedComments = [];
            let currentScroll = 0;
            // 移除滚动次数限制 - 采集所有评论直到到达底部
            let noNewCommentsStreak = 0;
            const maxNoNewCommentsStreak = 3;
            let likedCount = 0; // 记录点赞成功的数量

            function collectCurrentScreenComments() {
                utils.log(`VIDEO_COMMENTS: 采集当前屏幕评论 (滚动次数: ${currentScroll}, 无新评论次数: ${noNewCommentsStreak})`);

                const commentItemContainers = scrollableCommentList.find(id("com.xingin.xhs:id/eud"));
                utils.log(`VIDEO_COMMENTS: 在列表(ID: ${scrollableCommentList.id() || '无ID'})内找到 ${commentItemContainers.size()} 个评论项容器(eud)。`); // 增加了列表ID的日志
                let newCommentsOnThisScreen = 0;

                if (!commentItemContainers.empty()) {
                    commentItemContainers.forEach(function (eudContainer, index) {
                        try {
                            // utils.log(`  Processing eudContainer ${index + 1}: BoundsTop=${eudContainer.bounds().top}`);
                            const nicknameElement = eudContainer.findOne(id("com.xingin.xhs:id/jmt"));
                            const commentElement = eudContainer.findOne(id("com.xingin.xhs:id/jfh"));

                            if (nicknameElement && commentElement) {
                                const nickname = nicknameElement.text() || "";
                                const commentText = commentElement.text() || "";
                                if (commentText.trim() || nickname.trim()) {
                                    const commentSignature = `${nickname.trim()}::${commentText.trim()}`;
                                    if (!collectedComments.some(c => c.signature === commentSignature)) {
                                        let matchesKeywords = keywordsString ? keywordsString.split(',').some(k => commentText.includes(k.trim())) : false;

                                        // 如果匹配关键词，还需要检查区域筛选
                                        if (matchesKeywords && targetRegionIndex > 0) {
                                            // 提取用户属地信息 - 创建独立的变量避免引用问题
                                            let extractedLocation = utils.extractUserLocation(commentText);
                                            let currentUserLocation = String(extractedLocation || ""); // 强制转换为字符串
                                            let currentRegionMatches = utils.matchUserRegion(currentUserLocation, targetRegionIndex);

                                            if (!currentRegionMatches) {
                                                utils.log("VIDEO_COMMENTS: 评论匹配关键字但区域不匹配，跳过: " + nickname.trim() + " - 属地: \"" + currentUserLocation + "\"");
                                                matchesKeywords = false; // 区域不匹配，设置为不匹配
                                            } else {
                                                utils.log("VIDEO_COMMENTS: 评论匹配关键字且区域匹配: " + nickname.trim() + " - 属地: \"" + currentUserLocation + "\"");
                                            }
                                        }

                                        // 如果启用点赞功能且评论匹配关键词，执行点赞操作
                                        if (enableLiking && matchesKeywords) {
                                            utils.log(`VIDEO_COMMENTS: 准备为匹配关键词的评论点赞: ${nickname.trim()}`);
                                            try {
                                                // 使用简化版评论模块中的点赞函数
                                                let likeSuccess = simpleComments.likeComment(commentElement, nicknameElement, nickname.trim(), commentText.trim());
                                                if (likeSuccess) {
                                                    likedCount++; // 增加点赞成功计数
                                                    utils.log(`VIDEO_COMMENTS: ✓ 点赞成功: ${nickname.trim()} - "${commentText.trim().substring(0, 30)}..." (总计已点赞: ${likedCount})`);
                                                } else {
                                                    utils.log(`VIDEO_COMMENTS: ✗ 点赞失败: ${nickname.trim()} - "${commentText.trim().substring(0, 30)}..."`);
                                                }
                                            } catch (likeError) {
                                                utils.log(`VIDEO_COMMENTS: ✗ 点赞操作异常: ${nickname.trim()} - ${likeError}`);
                                            }
                                        }

                                        const newCommentObj = {
                                            nickname: nickname.trim(),
                                            content: commentText.trim(),
                                            signature: commentSignature,
                                            matchesKeyword: matchesKeywords, // 使用统一的字段名
                                            xhsId: "", // 初始化小红书号字段
                                            userInfoCollected: false // 标记是否已采集用户信息
                                        };

                                        // 如果评论匹配关键字，标记需要获取用户信息
                                        if (matchesKeywords) {
                                            utils.log("VIDEO_COMMENTS: 评论匹配关键字，标记需要获取用户信息: " + nickname.trim());
                                            newCommentObj.needsUserInfo = true; // 标记需要获取用户信息
                                            newCommentObj.nicknameElement = nicknameElement; // 保存元素引用
                                        }

                                        collectedComments.push(newCommentObj);
                                        newCommentsOnThisScreen++;
                                        utils.log(`    ✓ New comment: ${nickname.trim()} - "${commentText.trim().substring(0, 30)}..."`);
                                    }
                                }
                            } else {
                                let missing = [];
                                if (!nicknameElement) missing.push("Nickname(jmt)");
                                if (!commentElement) missing.push("Comment(jfh)");
                                utils.log(`    ✗ Skip (missing elements in eudContainer ${index + 1}: ${missing.join(', ')})`);
                            }
                        } catch (e) {
                            utils.log(`VIDEO_COMMENTS: 处理评论项 (eudContainer ${index + 1}) 时出错: ${e}`);
                        }
                    });
                } else {
                    utils.log("VIDEO_COMMENTS: 在当前屏幕的列表内未找到评论项容器(eud)。");
                }

                utils.log(`VIDEO_COMMENTS: 本次采集到 ${newCommentsOnThisScreen} 条新评论。`);
                utils.log(`VIDEO_COMMENTS: 累计采集 ${collectedComments.length} 条评论.`);

                // 检查是否到达底部
                const bottomIndicator = scrollableCommentList.parent().findOne(textMatches(/已经到底|无更多评论|- 到底了 -/).visibleToUser(true));

                // 如果检测到底部提示，进行最后一次评论采集确认
                if (bottomIndicator) {
                    utils.log("VIDEO_COMMENTS: 检测到评论区底部提示，进行最后一次评论采集确认...");

                    // 如果本次有新评论，说明底部还有内容，继续滚动一次
                    if (newCommentsOnThisScreen > 0) {
                        utils.log(`VIDEO_COMMENTS: 底部检测时发现 ${newCommentsOnThisScreen} 条新评论，继续滚动一次确保完全采集...`);
                        noNewCommentsStreak = 0; // 重置连续无新评论计数
                        // 继续滚动，不立即停止
                    } else {
                        // 没有新评论且到达底部，确认结束
                        utils.log("VIDEO_COMMENTS: 底部检测时没有新评论，确认已到达评论区底部。");
                        finishCollection();
                        return;
                    }
                } else {
                    // 未到达底部的情况，更新连续无新评论计数
                    if (newCommentsOnThisScreen === 0 && currentScroll > 0) {
                        noNewCommentsStreak++;
                        // 如果连续多次没有新评论，结束采集
                        if (noNewCommentsStreak >= maxNoNewCommentsStreak) {
                            utils.log(`VIDEO_COMMENTS: 连续 ${noNewCommentsStreak} 次滚动未发现新评论，可能已到达评论末尾。`);
                            finishCollection();
                            return;
                        }
                    } else if (newCommentsOnThisScreen > 0) {
                        noNewCommentsStreak = 0;
                        utils.log(`VIDEO_COMMENTS: 本次采集到 ${newCommentsOnThisScreen} 条新评论，继续滚动寻找更多评论...`);
                    }
                }

                utils.log(`VIDEO_COMMENTS: 在评论列表(ID: ${scrollableCommentList.id() || '无ID'})内滚动...`); // 增加了列表ID的日志
                let performedScroll = false;
                if (scrollableCommentList && typeof scrollableCommentList.scrollForward === 'function') {
                    if (scrollableCommentList.scrollable() && scrollableCommentList.scrollForward()) {
                        utils.log("VIDEO_COMMENTS: scrollForward() 成功.");
                        performedScroll = true;
                    } else {
                        utils.log("VIDEO_COMMENTS: scrollForward() 返回 false或列表不可滚动. 尝试用swipe.");
                        let listBoundsForSwipe = scrollableCommentList.bounds();
                        if (listBoundsForSwipe && listBoundsForSwipe.height() > 10) {
                            swipe(listBoundsForSwipe.centerX(), listBoundsForSwipe.centerY() + listBoundsForSwipe.height() * 0.3, listBoundsForSwipe.centerX(), listBoundsForSwipe.centerY() - listBoundsForSwipe.height() * 0.3, 800);
                            performedScroll = true;
                        } else {
                            utils.log("VIDEO_COMMENTS: 评论列表bounds无效或高度为0，无法执行swipe。结束采集。");
                            finishCollection();
                            return;
                        }
                    }
                } else {
                    utils.log("VIDEO_COMMENTS: 列表无 scrollForward 方法或scrollableCommentList无效，使用 swipe。");
                    let listBoundsForSwipe = scrollableCommentList ? scrollableCommentList.bounds() : null;
                    if (listBoundsForSwipe && listBoundsForSwipe.height() > 10) {
                        swipe(listBoundsForSwipe.centerX(), listBoundsForSwipe.centerY() + listBoundsForSwipe.height() * 0.3, listBoundsForSwipe.centerX(), listBoundsForSwipe.centerY() - listBoundsForSwipe.height() * 0.3, 800);
                        performedScroll = true;
                    } else {
                        utils.log("WARN: VIDEO_COMMENTS: 评论列表bounds无效或高度为0或不可见，无法滚动。");
                        finishCollection();
                        return;
                    }
                }

                if (performedScroll) {
                    sleep(2500);
                    currentScroll++;
                    collectCurrentScreenComments();
                } else {
                    utils.log("VIDEO_COMMENTS: 未能执行任何滚动操作，可能已到达无法滚动的状态。结束采集。");
                    finishCollection();
                }
            } // end of collectCurrentScreenComments

            function finishCollection() {
                utils.log(`VIDEO_COMMENTS: 评论采集完成流程启动, 共 ${collectedComments.length} 条评论`);

                // 统计匹配关键字的评论数量
                let matchedCount = 0;
                for (let i = 0; i < collectedComments.length; i++) {
                    if (collectedComments[i].matchesKeyword) {
                        matchedCount++;
                    }
                }

                utils.log(`VIDEO_COMMENTS: 共采集到 ${collectedComments.length} 条评论，其中 ${matchedCount} 条匹配关键字`);
                if (enableLiking) {
                    utils.log("VIDEO_COMMENTS: 点赞功能已启用，共成功点赞 " + likedCount + " 条评论");
                }

                // 在保存前，同步获取所有匹配关键字评论的用户信息
                utils.log(`VIDEO_COMMENTS: 开始获取 ${matchedCount} 条匹配评论的用户信息...`);

                let userInfoPromises = [];
                let processedUsers = new Set(); // 用于跟踪已处理的用户
                let userInfoMap = new Map(); // 存储用户昵称 -> 小红书号的映射

                // 第一步：去重并获取唯一用户的信息
                for (let i = 0; i < collectedComments.length; i++) {
                    if (collectedComments[i].needsUserInfo && collectedComments[i].nicknameElement) {
                        const comment = collectedComments[i];
                        const nickname = comment.nickname;

                        // 检查是否已经处理过这个用户
                        if (!processedUsers.has(nickname)) {
                            processedUsers.add(nickname);
                            utils.log(`VIDEO_COMMENTS: 首次处理用户 ${nickname}，开始获取用户信息`);

                            const promise = userProfile.getUserInfoFromNickname(comment.nicknameElement, comment.content)
                                .then(function (userInfo) {
                                    if (userInfo.success) {
                                        const xhsId = userInfo.xhsId || "";
                                        userInfoMap.set(nickname, xhsId);
                                        utils.log(`VIDEO_COMMENTS: ✓ 成功获取用户信息: ${userInfo.nickname} -> ${xhsId}`);
                                    } else {
                                        utils.log(`VIDEO_COMMENTS: ✗ 获取用户信息失败: ${userInfo.error || '未知错误'}`);
                                    }
                                    return userInfo;
                                })
                                .catch(function (error) {
                                    utils.log(`VIDEO_COMMENTS: ✗ 获取用户信息异常: ${error}`);
                                    return { success: false, error: error };
                                });
                            userInfoPromises.push(promise);
                        } else {
                            utils.log(`VIDEO_COMMENTS: 用户 ${nickname} 已处理过，跳过重复获取`);
                        }
                    }
                }

                // 等待所有用户信息获取完成后再保存文件
                if (userInfoPromises.length > 0) {
                    utils.log(`VIDEO_COMMENTS: 等待 ${userInfoPromises.length} 个用户信息获取完成（最多等待30秒）...`);

                    // 设置总体超时（30秒）避免ScriptInterruptedException
                    const allPromises = Promise.all(userInfoPromises);
                    const overallTimeout = new Promise(function (resolve) {
                        setTimeout(function () {
                            utils.log(`VIDEO_COMMENTS: 用户信息获取超时，继续保存文件...`);
                            resolve();
                        }, 30000);
                    });

                    Promise.race([allPromises, overallTimeout])
                        .then(function () {
                            utils.log(`VIDEO_COMMENTS: 用户信息获取完成或超时，开始分配用户信息到所有相关评论...`);

                            // 将获取到的用户信息分配给所有相关评论
                            for (let i = 0; i < collectedComments.length; i++) {
                                if (collectedComments[i].needsUserInfo) {
                                    const nickname = collectedComments[i].nickname;
                                    const xhsId = userInfoMap.get(nickname);
                                    if (xhsId) {
                                        collectedComments[i].xhsId = xhsId;
                                        collectedComments[i].userInfoCollected = true;
                                        utils.log(`VIDEO_COMMENTS: ✓ 分配用户信息: ${nickname} -> ${xhsId}`);
                                    }
                                }
                            }

                            utils.log(`VIDEO_COMMENTS: 用户信息分配完成，开始保存文件...`);
                            saveAndFinish();
                        })
                        .catch(function (error) {
                            utils.log(`VIDEO_COMMENTS: 用户信息获取过程中出现错误: ${error}，继续保存文件...`);
                            saveAndFinish();
                        });
                } else {
                    utils.log(`VIDEO_COMMENTS: 无需获取用户信息，直接保存文件...`);
                    saveAndFinish();
                }

                function saveAndFinish() {
                    if (collectedComments.length > 0) {
                        try {
                            // 调试：检查传递给保存函数的评论数据
                            const matchedBeforeSave = collectedComments.filter(c => c && c.matchesKeyword);
                            utils.log(`VIDEO_COMMENTS: 准备保存 ${collectedComments.length} 条评论，其中 ${matchedBeforeSave.length} 条匹配关键字`);

                            // 调试：打印前5条匹配关键字的评论
                            for (let i = 0; i < Math.min(matchedBeforeSave.length, 5); i++) {
                                const comment = matchedBeforeSave[i];
                                utils.log(`VIDEO_COMMENTS: 匹配评论 #${i + 1}: ${comment.nickname} - "${comment.content.substring(0, 30)}..." - matchesKeyword: ${comment.matchesKeyword}`);
                            }

                            // 调用从 xhs_simple_comments.js 导入的函数
                            simpleComments.saveCommentsToFile(collectedComments, noteTitle, keywordsString);
                            // saveCommentsToFile 内部应该有自己的成功日志和toast
                        } catch (e) {
                            // 如果 simpleComments.saveCommentsToFile 内部没有完全捕获异常并向上抛出
                            utils.log(`ERROR: VIDEO_COMMENTS: 调用 simpleComments.saveCommentsToFile 时抛出未捕获异常: ${e}`);
                        }

                        // 保存用户数据到数据管理器
                        try {
                            const dataManager = require('./data_manager.js');
                            const usersToSave = [];

                            for (let i = 0; i < collectedComments.length; i++) {
                                const comment = collectedComments[i];
                                if (comment.matchesKeyword) {
                                    const userObj = {
                                        uid: comment.nickname + "_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9), // 生成唯一ID
                                        nickname: comment.nickname,
                                        xhsId: comment.xhsId || '未获取',
                                        commentText: comment.content,
                                        noteId: 'note_' + Date.now(), // 生成笔记ID
                                        noteTitle: noteTitle || '未知',
                                        timestamp: Date.now(),
                                        status: 'collected'
                                    };
                                    usersToSave.push(userObj);
                                }
                            }

                            if (usersToSave.length > 0) {
                                const saveResult = dataManager.saveScrapedCommentUsers(usersToSave);
                                if (saveResult) {
                                    utils.log(`VIDEO_COMMENTS: ✓ 已保存 ${usersToSave.length} 个用户到数据管理器`);
                                } else {
                                    utils.log(`VIDEO_COMMENTS: ✗ 保存用户到数据管理器失败`);
                                }
                            }
                        } catch (e) {
                            utils.log(`VIDEO_COMMENTS: 保存用户数据时出错: ${e}`);
                        }
                    }
                    resolve(collectedComments.length);
                }
            }

            collectCurrentScreenComments();

        } catch (e) {
            utils.log(`ERROR: VIDEO_COMMENTS: 视频评论采集核心异常: ${e.name} - ${e.message}\nStack: ${e.stack}`);
            resolve(0);
        }
    });
}


/**
 * 检查是否在视频笔记详情页
 * @returns {boolean} 是否在视频笔记详情页
 */
function isVideoNoteDetailPage() {
    try {
        // 检查视频笔记特有的评论按钮容器
        const commentButtonContainer = id("com.xingin.xhs:id/c9y").findOne(1000);
        return commentButtonContainer !== null;
    } catch (e) {
        utils.log(`VIDEO_COMMENTS: 检查视频笔记详情页时出错: ${e}`);
        return false;
    }
}

/**
 * 检查视频评论区是否已弹出
 * @returns {boolean} 评论区是否已弹出
 */
function isVideoCommentSectionOpen() {
    try {
        const commentSectionIndicator = id("com.xingin.xhs:id/ivx").findOne(1000);
        if (!commentSectionIndicator) {
            return false;
        }

        const indicatorText = commentSectionIndicator.text() || "";
        const commentPattern = /^共\d+条评论$/;
        return commentPattern.test(indicatorText);
    } catch (e) {
        utils.log(`VIDEO_COMMENTS: 检查视频评论区状态时出错: ${e}`);
        return false;
    }
}

// /**
//  * 将视频评论保存到文件
//  * @param {Array} comments - 评论数组
//  * @param {string} noteTitle - 笔记标题
//  * @param {string} keywordsString - 关键词字符串
//  */
// function saveVideoCommentsToFile(comments, noteTitle, keywordsString) {
//     utils.log("VIDEO_COMMENTS: 开始保存视频评论到文件...");
//     utils.log(`VIDEO_COMMENTS: 准备保存 ${comments.length} 条评论`);

//     try {
//         // 去重处理
//         const uniqueComments = [];
//         const seenSignatures = new Set();

//         for (let i = 0; i < comments.length; i++) {
//             const comment = comments[i];
//             if (comment && comment.signature && !seenSignatures.has(comment.signature)) {
//                 seenSignatures.add(comment.signature);
//                 uniqueComments.push(comment);
//             }
//         }

//         utils.log(`VIDEO_COMMENTS: 去重后评论数量: ${uniqueComments.length} 条`);

//         // 创建目录
//         let logDir;
//         try {
//             logDir = files.join(files.getSdcardPath(), "XHS_Video_Comments");
//             if (!files.exists(logDir)) {
//                 files.create(logDir + "/.nomedia");
//             }
//         } catch (e) {
//             logDir = files.cwd();
//             utils.log(`VIDEO_COMMENTS: 使用当前工作目录: ${logDir}`);
//         }

//         // 创建文件名
//         const timestamp = new Date().getTime();
//         const safeTitle = noteTitle.replace(/[\\/:*?"<>|]/g, "_").substring(0, 20);
//         const filePath = files.join(logDir, `XHS_Video_Comments_${safeTitle}_${timestamp}.txt`);

//         // 创建文件内容
//         let content = `===== 视频笔记评论 | 标题: "${noteTitle}" | 时间: ${new Date().toLocaleString()} | 共 ${uniqueComments.length} 条评论 =====\n\n`;
//         content += `关键字: "${keywordsString}"\n\n`;

//         // 匹配关键字的评论
//         const matchedComments = uniqueComments.filter(c => c.matchesKeywords);
//         content += `【匹配关键字的评论】(${matchedComments.length} 条)\n\n`;

//         if (matchedComments.length > 0) {
//             for (let i = 0; i < matchedComments.length; i++) {
//                 content += `${i + 1}. 用户: ${matchedComments[i].nickname}\n`;
//                 content += `   评论: ${matchedComments[i].content}\n`;
//                 content += `   ----------------------------\n`;
//             }
//         } else {
//             content += "没有找到匹配关键字的评论\n\n";
//         }

//         // 其他评论
//         const otherComments = uniqueComments.filter(c => !c.matchesKeywords);
//         content += `\n【其他评论】(${otherComments.length} 条)\n\n`;

//         if (otherComments.length > 0) {
//             for (let i = 0; i < otherComments.length; i++) {
//                 content += `${i + 1}. 用户: ${otherComments[i].nickname}\n`;
//                 content += `   评论: ${otherComments[i].content}\n`;
//                 content += `   ----------------------------\n`;
//             }
//         } else {
//             content += "没有找到其他评论\n\n";
//         }

//         // 写入文件
//         files.write(filePath, content);
//         utils.log(`VIDEO_COMMENTS: 视频评论已保存到文件: ${filePath}`);

//         // 显示通知
//         toast(`已保存 ${uniqueComments.length} 条视频评论到文件，其中 ${matchedComments.length} 条匹配关键字`);

//     } catch (e) {
//         utils.log(`VIDEO_COMMENTS: 保存视频评论到文件时出错: ${e}`);
//         throw e;
//     }
// }

module.exports = {
    processVideoNote: processVideoNote, // 新增：视频笔记业务统一处理
    collectVideoNoteComments,
    collectVideoComments,
    isVideoNoteDetailPage,
    isVideoCommentSectionOpen
};

utils.log("VIDEO_COMMENTS: 小红书视频笔记评论采集模块 (xhs_video_comments.js) 加载完毕。");
