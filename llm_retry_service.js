// LLM重试服务模块：为LLM API调用提供智能重试机制
const utils = require('./utils.js');
const llmService = require('./llm_service.js');

/**
 * LLM重试配置
 */
const RETRY_CONFIG = {
    // 第一阶段：短间隔重试
    PHASE1_RETRIES: 5,
    PHASE1_INTERVAL: 2 * 60 * 1000, // 2分钟

    // 第二阶段：长间隔重试
    PHASE2_RETRIES: 3,
    PHASE2_INTERVAL: 10 * 60 * 1000, // 10分钟

    // 总超时时间（约45分钟）
    TOTAL_TIMEOUT: 45 * 60 * 1000
};

/**
 * 检查错误是否需要重试
 * @param {string} error 错误信息
 * @returns {boolean} 是否需要重试
 */
function shouldRetry(error) {
    if (!error) return false;

    const errorLower = error.toLowerCase();

    // 网络相关错误 - 需要重试
    if (errorLower.indexOf("timeout") >= 0 ||
        errorLower.indexOf("connection") >= 0 ||
        errorLower.indexOf("network") >= 0 ||
        errorLower.indexOf("socket") >= 0 ||
        errorLower.indexOf("502") >= 0 ||
        errorLower.indexOf("503") >= 0 ||
        errorLower.indexOf("504") >= 0 ||
        errorLower.indexOf("500") >= 0) {
        return true;
    }

    // API限流错误 - 需要重试
    if (errorLower.indexOf("rate limit") >= 0 ||
        errorLower.indexOf("too many requests") >= 0 ||
        errorLower.indexOf("429") >= 0) {
        return true;
    }

    // 服务暂时不可用 - 需要重试
    if (errorLower.indexOf("service unavailable") >= 0 ||
        errorLower.indexOf("temporarily unavailable") >= 0) {
        return true;
    }

    // 认证错误、参数错误等 - 不需要重试
    if (errorLower.indexOf("401") >= 0 ||
        errorLower.indexOf("403") >= 0 ||
        errorLower.indexOf("400") >= 0 ||
        errorLower.indexOf("invalid") >= 0 ||
        errorLower.indexOf("unauthorized") >= 0) {
        return false;
    }

    // 默认重试
    return true;
}

/**
 * 等待指定时间（Auto.js环境使用sleep）
 * @param {number} ms 等待时间（毫秒）
 */
function waitMs(ms) {
    // 在Auto.js环境中使用sleep函数
    if (typeof sleep === 'function') {
        sleep(ms);
    } else {
        // 备用方案：使用setTimeout（虽然在Auto.js中不常用）
        var start = Date.now();
        while (Date.now() - start < ms) {
            // 忙等待
        }
    }
}

/**
 * 带重试机制的LLM调用
 * @param {string} noteContent 笔记内容
 * @param {string} apiUrl API地址
 * @param {string} modelName 模型名称
 * @param {string} userPrompt 用户提示词
 * @param {number} temperature 温度参数
 * @param {number} maxTokens 最大Token数
 * @param {boolean} useDoubaoProxy 是否使用豆包反代
 * @param {string} doubaoPhoneNumber 豆包登录手机号
 * @param {function} progressCallback 进度回调函数
 * @returns {Promise<{success: boolean, comment: string|null, error: string|null}>}
 */
function generateCommentWithRetry(noteContent, apiUrl, modelName, userPrompt, temperature, maxTokens, useDoubaoProxy, doubaoPhoneNumber, progressCallback) {
    return new Promise(function (resolve) {
        var startTime = Date.now();
        var totalRetries = 0;
        var phase1Retries = 0;
        var phase2Retries = 0;

        function updateProgress(message) {
            if (progressCallback && typeof progressCallback === 'function') {
                progressCallback(message);
            }
            utils.log("LLM_RETRY: " + message);
        }

        function attemptCall() {
            // 检查总超时
            if (Date.now() - startTime > RETRY_CONFIG.TOTAL_TIMEOUT) {
                var timeoutMsg = "LLM调用总超时（45分钟），停止重试";
                updateProgress(timeoutMsg);
                resolve({ success: false, comment: null, error: timeoutMsg });
                return;
            }

            totalRetries++;
            updateProgress(`第 ${totalRetries} 次尝试调用LLM API...`);

            try {
                var result = llmService.generateCommentWithLLM(
                    noteContent, apiUrl, modelName, userPrompt,
                    temperature, maxTokens, useDoubaoProxy, doubaoPhoneNumber
                );

                if (result.success) {
                    updateProgress(`LLM调用成功！总尝试次数: ${totalRetries}`);
                    resolve(result);
                    return;
                }

                // 调用失败，检查是否需要重试
                if (!shouldRetry(result.error)) {
                    updateProgress(`LLM调用失败，错误不可重试: ${result.error}`);
                    resolve(result);
                    return;
                }

                // 需要重试，确定重试策略
                var shouldContinue = false;
                var waitTime = 0;

                if (phase1Retries < RETRY_CONFIG.PHASE1_RETRIES) {
                    // 第一阶段：短间隔重试
                    phase1Retries++;
                    waitTime = RETRY_CONFIG.PHASE1_INTERVAL;
                    shouldContinue = true;
                    updateProgress(`第一阶段重试 ${phase1Retries}/${RETRY_CONFIG.PHASE1_RETRIES}，${waitTime / 1000 / 60}分钟后重试。错误: ${result.error}`);
                } else if (phase2Retries < RETRY_CONFIG.PHASE2_RETRIES) {
                    // 第二阶段：长间隔重试
                    phase2Retries++;
                    waitTime = RETRY_CONFIG.PHASE2_INTERVAL;
                    shouldContinue = true;
                    updateProgress(`第二阶段重试 ${phase2Retries}/${RETRY_CONFIG.PHASE2_RETRIES}，${waitTime / 1000 / 60}分钟后重试。错误: ${result.error}`);
                } else {
                    // 所有重试都用完了
                    updateProgress(`所有重试机会已用完，最终失败。错误: ${result.error}`);
                    resolve(result);
                    return;
                }

                if (shouldContinue) {
                    // 等待后重试
                    updateProgress(`等待 ${waitTime / 1000 / 60} 分钟后重试...`);
                    setTimeout(attemptCall, waitTime);
                }

            } catch (exception) {
                var exceptionMsg = "LLM调用发生异常: " + exception.toString();
                updateProgress(exceptionMsg);

                if (!shouldRetry(exceptionMsg)) {
                    resolve({ success: false, comment: null, error: exceptionMsg });
                    return;
                }

                // 异常也按重试逻辑处理
                var shouldContinue = false;
                var waitTime = 0;

                if (phase1Retries < RETRY_CONFIG.PHASE1_RETRIES) {
                    phase1Retries++;
                    waitTime = RETRY_CONFIG.PHASE1_INTERVAL;
                    shouldContinue = true;
                    updateProgress(`异常重试 ${phase1Retries}/${RETRY_CONFIG.PHASE1_RETRIES}，${waitTime / 1000 / 60}分钟后重试`);
                } else if (phase2Retries < RETRY_CONFIG.PHASE2_RETRIES) {
                    phase2Retries++;
                    waitTime = RETRY_CONFIG.PHASE2_INTERVAL;
                    shouldContinue = true;
                    updateProgress(`异常重试 ${phase2Retries}/${RETRY_CONFIG.PHASE2_RETRIES}，${waitTime / 1000 / 60}分钟后重试`);
                } else {
                    resolve({ success: false, comment: null, error: exceptionMsg });
                    return;
                }

                if (shouldContinue) {
                    setTimeout(attemptCall, waitTime);
                }
            }
        }

        // 开始第一次尝试
        attemptCall();
    });
}

/**
 * 中止重试机制的标志
 */
var abortRetry = false;

/**
 * 设置中止重试标志
 */
function setAbortRetry() {
    abortRetry = true;
    utils.log("LLM_RETRY: 收到中止重试信号");
}

/**
 * 重置中止重试标志
 */
function resetAbortRetry() {
    abortRetry = false;
    utils.log("LLM_RETRY: 重置中止重试标志");
}

/**
 * 检查是否应该中止重试
 */
function shouldAbortRetry() {
    return abortRetry;
}

module.exports = {
    generateCommentWithRetry,
    setAbortRetry,
    resetAbortRetry,
    shouldAbortRetry,
    RETRY_CONFIG
};

utils.log("LLM重试服务模块加载完毕 (llm_retry_service.js)");
