// ui.js - 重构支持选项卡系统 (v8.0)
"ui"; // Good practice

console.log("UI_REFACTORED: ui.js module parsed with tab support.");

// 引入选项卡和用户列表模块
let uiTabsModule;
let uiUserListModule;

try {
    uiTabsModule = require('./ui_tabs.js');
    uiUserListModule = require('./ui_user_list.js');
    console.log("UI_REFACTORED: Tab modules loaded successfully.");
} catch (e) {
    console.error("UI_REFACTORED: Failed to load tab modules:", e);
    // 如果模块加载失败，使用原始UI
}

function getUILayoutXML() {
    console.log("UI_REFACTORED: getUILayoutXML() called.");

    // 如果选项卡模块可用，使用新的选项卡布局
    if (uiTabsModule && typeof uiTabsModule.getTabLayoutXML === 'function') {
        console.log("UI_REFACTORED: Using tab layout.");
        return uiTabsModule.getTabLayoutXML();
    }

    // 否则使用原始布局作为后备
    console.log("UI_REFACTORED: Using fallback layout.");
    return `
        <vertical padding="16">
            <text textSize="18sp" textColor="black" text="小红书自动化助手 (v7 - 兼容模式)" />

            <card cardBackgroundColor="#ffeeee" cardCornerRadius="8dp" cardElevation="4dp" margin="8dp">
                <vertical padding="12dp">
                    <text textSize="16sp" textColor="#cc0000" textStyle="bold" text="⚠️ 重要提示" />
                    <text textSize="14sp" textColor="#cc0000" text="使用前请先手动关闭小红书app！" marginTop="4dp" />
                    <text textSize="12sp" textColor="#666666" text="确保从干净状态开始任务，避免元素检测失败" marginTop="2dp" />
                </vertical>
            </card>

            <ScrollView>
                <vertical>
                    <text textSize="16sp" textColor="black" marginTop="10dp" text="笔记搜索与筛选:" />
                    <input id="search_keyword" hint="输入笔记关键字"/>
                    <text textSize="14sp" text="排序依据:" />
                    <spinner id="search_sort_by" entries="综合|最新|最多点赞|最多评论|最多收藏"/>
                    <text textSize="14sp" text="发布时间:" />
                    <spinner id="search_publish_time" entries="不限|一天内|一周内|半年内"/>
                    <text textSize="14sp" text="位置距离:" />
                    <spinner id="search_location_distance" entries="不限|同城|附近"/>

                    <text textSize="16sp" textColor="black" marginTop="10dp" text="任务设置:" />
                    <text textSize="14sp" text="目标客户筛选关键词 (逗号分隔):" />
                    <input id="commentKeywords" hint="例如：求带,想了解,怎么做" />
                    <text textSize="14sp" text="目标客户区域筛选 (可选):" />
                    <spinner id="targetRegion" entries="不限|北京|上海|天津|重庆|河北|山西|内蒙古|辽宁|吉林|黑龙江|江苏|浙江|安徽|福建|江西|山东|河南|湖北|湖南|广东|广西|海南|四川|贵州|云南|西藏|陕西|甘肃|青海|宁夏|新疆"/>
                    <text textSize="14sp" text="私信模板:" />
                    <input id="messageTemplate" hint="请输入要发送的私信内容" />

                    <text textSize="16sp" textColor="black" marginTop="10dp" text="任务类型:" />
                    <checkbox id="task_collect_users" text="采集目标客户" checked="true" />
                    <checkbox id="task_comment_notes" text="笔记截流(评论笔记)" />
                    <checkbox id="task_like_users" text="目标客户留痕(点赞)" />

                    <text textSize="16sp" textColor="black" marginTop="10dp" text="评论设置 (笔记截流功能):" />
                    <text textSize="14sp" text="自定义评论内容 (每行一条评论):" />
                    <input id="customComments" hint="输入评论内容，每行一条" lines="4" />
                    <text textSize="14sp" text="评论模式:" />
                    <spinner id="commentMode" entries="随机选择|按顺序选择"/>
                    <checkbox id="enableCommentDeduplication" text="避免重复评论 (已评论过的笔记将跳过)" />
                    <checkbox id="enableLlmComments" text="使用AI生成评论" />

                    <text textSize="16sp" textColor="black" marginTop="10dp" text="AI模型设置:" />
                    <checkbox id="useDoubaoProxy" text="使用豆包AI" />
                    <input id="doubaoPhoneNumber" hint="豆包登录手机号 (例如: 13800138000)"/>
                    <input id="llmApiUrl" hint="通用LLM API 地址 (例如: http://localhost:1234/v1)"/>
                    <input id="llmModelName" hint="LLM 模型名称 (例如: gpt-3.5-turbo)"/>

                    <text textSize="14sp" text="AI分析方式:" marginTop="10dp" />
                    <checkbox id="llmUseContentExtraction" text="提取笔记内容发送给AI" checked="true" />
                    <checkbox id="llmUseShareLink" text="复制分享链接发送给AI" />

                    <text textSize="14sp" text="提示词模板选择:" marginTop="10dp" />
                    <horizontal>
                        <button id="templateSelectorBtn" text="点击选择模板" layout_weight="1" />
                        <button id="refreshTemplatesBtn" text="刷新" />
                    </horizontal>
                    <text textSize="14sp" text="当前模板内容:" marginTop="8dp" />
                    <input id="selectedLlmPromptContent" lines="5" enabled="false" hint="此处显示选中模板的内容"/>

                    <text text="添加/编辑模板:" marginTop="16dp" />
                    <input id="newPromptTemplateName" hint="模板名称 (例如：通用评论模板)"/>
                    <input id="newPromptTemplateContent" hint="模板内容 (使用 {笔记内容} 代表笔记正文)" lines="4" />
                    <horizontal>
                        <button id="editSelectedTemplateBtn" text="编辑选中模板" layout_weight="1"/>
                        <button id="clearEditAreaBtn" text="清空编辑区域" layout_weight="1"/>
                    </horizontal>
                    <horizontal>
                        <button id="addLlmPromptTemplateBtn" text="保存模板" layout_weight="1"/>
                        <button id="deleteLlmPromptTemplateBtn" text="删除选中模板" layout_weight="1"/>
                    </horizontal>

                    <button id="startBtn" text="开始任务" marginTop="10dp" />
                    <button id="stopBtn" text="停止任务" />
                    <button id="saveConfigBtn" text="保存配置" />
                    <button id="openAccessibilityBtn" text="开启无障碍服务" />
                    <button id="exportCsvBtn" text="导出为CSV" />
                    <button id="exportTxtBtn" text="导出为TXT" />

                    <text textSize="16sp" textColor="black" marginTop="10dp" text="任务状态:" />
                    <text id="scrapingStatus" text="状态: 未开始" marginTop="5dp"/>
                    <text id="processedNotesCount" text="已处理笔记: 0" marginTop="5dp"/>
                    <text id="collectedUsersCount" text="已采集用户: 0" marginTop="5dp"/>

                    <text text="状态列表:" />
                    <list id="statusList">
                        <text text="{{this.nickname}} - {{this.status}}" />
                    </list>

                    <text textSize="14sp" marginTop="10dp" text="账号 (每行一个，格式：账号----密码):" />
                    <input id="accounts" hint="account1----pass1" lines="4" />
                    <text text="博主ID:" />
                    <input id="bloggerId" hint="请输入博主ID (可选)" />
                    <text text="搜索词(旧版):" />
                    <input id="searchKeyword" hint="请输入搜索关键词" />
                </vertical>
            </ScrollView>
            <text text="日志 (可拖动):" />
            <frame id="logFrame" h="100dp" bg="#ffeeeeee">
                <text id="logText" textColor="gray" textSize="10sp" margin="5" />
            </frame>
        </vertical>
    `;
}

// dataManager and configManager will be passed from main.js
function attachUIEventHandlers(uiObject, utilsObject, dataManagerObject, configManagerObject, mainTaskManager) {
    console.log("UI_REFACTORED: attachUIEventHandlers() called with tab support.");
    toast("UI_REFACTORED: Attaching event handlers (Tabs + LLM Templates)...");

    if (!uiObject || !configManagerObject || !utilsObject) {
        console.error("UI_ERROR: Essential objects (uiObject, configManagerObject, utilsObject) are missing in attachUIEventHandlers.");
        return;
    }

    // 首先附加选项卡事件处理器
    if (uiTabsModule && typeof uiTabsModule.attachTabEventHandlers === 'function') {
        console.log("UI_REFACTORED: Attaching tab event handlers.");
        uiTabsModule.attachTabEventHandlers(uiObject, dataManagerObject, utilsObject);

        // 默认显示主功能选项卡
        if (typeof uiTabsModule.switchTab === 'function') {
            uiTabsModule.switchTab(uiObject, 'main');
        }
    }

    // 附加用户列表事件处理器
    if (uiUserListModule && typeof uiUserListModule.attachUserListEventHandlers === 'function') {
        console.log("UI_REFACTORED: Attaching user list event handlers.");
        uiUserListModule.attachUserListEventHandlers(uiObject, dataManagerObject, utilsObject);
    }

    let currentConfig = configManagerObject.loadConfig(); // Central config object for this scope
    // Ensure configManagerObject itself might hold a reference if other parts of it expect a .currentConfig
    // For now, this local currentConfig will be the source of truth for modifications within this function.
    // If configManagerObject is designed to have its own .currentConfig updated, adapt as needed.
    // For example: configManagerObject.currentConfig = currentConfig;

    // New Search Button Handler
    if (uiObject.btn_start_search) {
        uiObject.btn_start_search.on("click", () => {
            const keyword = uiObject.search_keyword.getText().toString();
            const sortBy = uiObject.search_sort_by.getSelectedItemPosition();
            const publishTime = uiObject.search_publish_time.getSelectedItemPosition();
            const locationDistance = uiObject.search_location_distance.getSelectedItemPosition();

            if (!keyword) {
                toast("请输入搜索关键字");
                return;
            }

            const searchParams = { keyword, sortBy, publishTime, locationDistance };
            utilsObject.log("搜索参数: " + JSON.stringify(searchParams));

            if (mainTaskManager && typeof mainTaskManager.handleSearchRequest === 'function') {
                mainTaskManager.handleSearchRequest(searchParams);
            } else {
                toast("错误：无法触达搜索处理函数 (mainTaskManager)");
                utilsObject.logError("UI: mainTaskManager or handleSearchRequest is not available.");
            }
        });
    } else {
        utilsObject.logWarn("UI: btn_start_search UI element not found for attaching handler.");
    }

    uiObject.startBtn.on("click", () => {
        utilsObject.log("开始任务按钮被点击");

        // 检查是否选择了任务类型
        const collectUsers = uiObject.task_collect_users && uiObject.task_collect_users.checked;
        const commentNotes = uiObject.task_comment_notes && uiObject.task_comment_notes.checked;
        const likeUsers = uiObject.task_like_users && uiObject.task_like_users.checked;

        if (!collectUsers && !commentNotes && !likeUsers) {
            toast("请至少选择一种任务类型！");
            utilsObject.log("开始任务失败：未选择任务类型");
            return;
        }

        // 检查搜索关键词
        const keyword = uiObject.search_keyword.getText().toString().trim();
        if (!keyword) {
            toast("请输入搜索关键词！");
            utilsObject.log("开始任务失败：未输入搜索关键词");
            return;
        }

        // 提醒用户确保小红书已关闭
        toast("提醒：请确保已手动关闭小红书app！");
        utilsObject.log("UI: 提醒用户确保小红书已关闭");

        // 直接继续执行任务
        continueTaskExecution();

        // 定义继续执行任务的函数
        function continueTaskExecution() {

            // 检查评论关键词（如果需要采集目标客户）
            if (collectUsers) {
                const commentKeywords = uiObject.commentKeywords.getText().toString().trim();
                if (!commentKeywords) {
                    toast("请输入目标客户筛选关键词！");
                    utilsObject.log("开始任务失败：未设置目标客户筛选关键词");
                    return;
                }
            }

            // 检查自定义评论内容（如果需要评论笔记）
            if (commentNotes) {
                const customComments = uiObject.customComments.getText().toString().trim();
                if (!customComments) {
                    toast("请输入自定义评论内容！");
                    utilsObject.log("开始任务失败：未设置自定义评论内容");
                    return;
                }
            }

            // 获取搜索参数
            const sortBy = uiObject.search_sort_by.getSelectedItemPosition();
            const publishTime = uiObject.search_publish_time.getSelectedItemPosition();
            const locationDistance = uiObject.search_location_distance.getSelectedItemPosition();
            const targetRegion = uiObject.targetRegion.getSelectedItemPosition();
            const searchParams = { keyword, sortBy, publishTime, locationDistance, targetRegion };

            // 开始执行任务
            utilsObject.log("开始执行任务，选择的任务类型：" +
                (collectUsers ? "采集目标客户 " : "") +
                (commentNotes ? "笔记截流 " : "") +
                (likeUsers ? "目标客户留痕 " : ""));

            // 先执行搜索笔记
            if (mainTaskManager && typeof mainTaskManager.handleSearchRequest === 'function') {
                utilsObject.log("开始搜索笔记，参数：" + JSON.stringify(searchParams));
                toast("开始搜索笔记...");

                // 使用线程执行搜索，并在搜索完成后自动开始执行选定的任务
                threads.start(function () {
                    try {
                        // 执行搜索
                        const searchResult = mainTaskManager.handleSearchRequest(searchParams);

                        // 如果搜索失败，则退出
                        if (!searchResult || !searchResult.success) {
                            toast("搜索失败，无法开始任务");
                            utilsObject.log("搜索失败，无法开始任务：" + (searchResult ? searchResult.message : "未知错误"));
                            return;
                        }

                        // 等待搜索结果页面完全加载（给UI一些时间更新）
                        sleep(3000);

                        // 检查是否在搜索结果页
                        if (mainTaskManager && typeof mainTaskManager.isSearchResultsPage === 'function' &&
                            mainTaskManager.isSearchResultsPage()) {

                            // 如果选择了采集目标客户，启动评论采集
                            if (collectUsers && mainTaskManager && typeof mainTaskManager.startCommentUserScraping === 'function') {
                                utilsObject.log("搜索完成，开始采集目标客户");
                                toast("搜索完成，开始采集目标客户");
                                mainTaskManager.startCommentUserScraping();
                            }

                            // 笔记截流(评论笔记)功能处理
                            if (commentNotes && mainTaskManager && typeof mainTaskManager.startNoteCommenting === 'function') {
                                utilsObject.log("搜索完成，开始笔记截流(评论笔记)");
                                toast("搜索完成，开始笔记截流(评论笔记)");
                                mainTaskManager.startNoteCommenting();
                            } else if (commentNotes) {
                                toast("笔记截流功能暂不可用");
                                utilsObject.log("笔记截流功能：mainTaskManager.startNoteCommenting 不可用");
                            }

                            // 点赞功能已集成到评论采集中，会根据配置自动执行
                            if (likeUsers && collectUsers) {
                                utilsObject.log("目标客户留痕(点赞)功能已启用，将在评论采集过程中自动执行");
                            } else if (likeUsers && !collectUsers) {
                                toast("点赞功能需要同时启用\"采集目标客户\"功能");
                                utilsObject.log("点赞功能需要同时启用采集目标客户功能");
                            }
                        } else {
                            toast("搜索似乎成功了，但未能进入搜索结果页，请检查应用状态");
                            utilsObject.log("搜索似乎成功了，但未能进入搜索结果页，请检查应用状态");
                        }
                    } catch (e) {
                        toast("执行任务时发生错误：" + e.toString());
                        utilsObject.log("执行任务时发生错误：" + e.toString());
                    }
                });
            } else {
                toast("错误：无法执行搜索功能");
                utilsObject.log("错误：mainTaskManager.handleSearchRequest 不可用");
            }
        }
    });

    uiObject.stopBtn.on("click", () => {
        utilsObject.log("停止任务按钮被点击");

        // 停止评论采集任务
        if (mainTaskManager && typeof mainTaskManager.stopCommentUserScraping === 'function') {
            mainTaskManager.stopCommentUserScraping();
        }

        toast("正在停止所有任务...");
        // 延迟一秒后强制停止所有线程，给任务一个优雅退出的机会
        setTimeout(function () {
            threads.shutDownAll(); // 强制停止所有脚本线程
            toast("所有任务已停止");
        }, 1000);
    });

    uiObject.saveConfigBtn.on("click", () => {
        utilsObject.log("保存配置按钮被点击");
        if (configManagerObject && typeof configManagerObject.saveConfig === 'function') {
            // Construct the config object to save.
            // Most settings are read directly from UI elements.
            // LLM Prompt Template data (llmPromptTemplates, selectedLlmPromptTemplateName)
            // should come from the `currentConfig` variable scoped to attachUIEventHandlers,
            // as it's updated by the template management UI interactions.

            // Create a new object for saving, copying non-template UI values and template values from scoped currentConfig
            let configToSave = {
                searchKeyword: uiObject.searchKeyword.getText().toString(),
                bloggerId: uiObject.bloggerId.getText().toString(),
                commentKeywords: uiObject.commentKeywords.getText().toString(),
                targetRegion: uiObject.targetRegion.getSelectedItemPosition(),
                messageTemplate: uiObject.messageTemplate.getText().toString(),
                accounts: uiObject.accounts.getText().toString(),
                advanced_search_keyword: uiObject.search_keyword ? uiObject.search_keyword.getText().toString() : "",
                advanced_search_sort_by: uiObject.search_sort_by ? uiObject.search_sort_by.getSelectedItemPosition() : 0,
                advanced_search_publish_time: uiObject.search_publish_time ? uiObject.search_publish_time.getSelectedItemPosition() : 0,
                advanced_search_location_distance: uiObject.search_location_distance ? uiObject.search_location_distance.getSelectedItemPosition() : 0,
                task_collect_users: uiObject.task_collect_users ? uiObject.task_collect_users.checked : true,
                task_comment_notes: uiObject.task_comment_notes ? uiObject.task_comment_notes.checked : false,
                task_like_users: uiObject.task_like_users ? uiObject.task_like_users.checked : false,
                customComments: uiObject.customComments ? uiObject.customComments.getText().toString() : "",
                commentMode: uiObject.commentMode ? uiObject.commentMode.getSelectedItemPosition() : 0,
                enableCommentDeduplication: uiObject.enableCommentDeduplication ? uiObject.enableCommentDeduplication.checked : false,
                enableLlmComments: uiObject.enableLlmComments ? uiObject.enableLlmComments.checked : false,
                useDoubaoProxy: uiObject.useDoubaoProxy ? uiObject.useDoubaoProxy.checked : false,
                doubaoPhoneNumber: uiObject.doubaoPhoneNumber ? uiObject.doubaoPhoneNumber.getText().toString() : "",
                llmApiUrl: uiObject.llmApiUrl ? uiObject.llmApiUrl.getText().toString() : "",
                llmModelName: uiObject.llmModelName ? uiObject.llmModelName.getText().toString() : "",
                llmUseContentExtraction: uiObject.llmUseContentExtraction ? uiObject.llmUseContentExtraction.checked : true,
                llmUseShareLink: uiObject.llmUseShareLink ? uiObject.llmUseShareLink.checked : false,
                llmTemperature: 0.7, // 固定值，不再从UI获取
                llmMaxTokens: 512, // 固定值，不再从UI获取

                // Source LLM template data from the function-scoped currentConfig
                llmPromptTemplates: currentConfig.llmPromptTemplates,
                selectedLlmPromptTemplateName: currentConfig.selectedLlmPromptTemplateName,

                // 安全控制配置
                safetyMinCommentCount: uiObject.safetyMinCommentCount ? parseInt(uiObject.safetyMinCommentCount.getText().toString()) || 0 : 0,
                safetyCommentDelayMin: uiObject.safetyCommentDelayMin ? parseInt(uiObject.safetyCommentDelayMin.getText().toString()) || 5 : 5,
                safetyCommentDelayMax: uiObject.safetyCommentDelayMax ? parseInt(uiObject.safetyCommentDelayMax.getText().toString()) || 15 : 15,
                safetyMaxCommentsPerAccount: uiObject.safetyMaxCommentsPerAccount ? parseInt(uiObject.safetyMaxCommentsPerAccount.getText().toString()) || 50 : 50,

                // 多账号配置
                enableMultiAccount: uiObject.enableMultiAccount ? uiObject.enableMultiAccount.checked : false,
                accountList: uiObject.accountList ? uiObject.accountList.getText().toString() : "",
                autoSwitchOnLimit: uiObject.autoSwitchOnLimit ? uiObject.autoSwitchOnLimit.checked : true
            };

            configManagerObject.saveConfig(configToSave);
            // Update the scoped currentConfig to reflect the saved state, especially if saveConfig modifies it (e.g., cleans up)
            // or if other parts of the UI rely on this specific instance being the absolute latest.
            // For now, assume saveConfig just saves, and our scoped currentConfig is already what we want.
            // If saveConfig returns the (potentially modified) saved config, use that:
            // currentConfig = configManagerObject.saveConfig(configToSave);
            // For simplicity, we'll assume currentConfig is kept up-to-date by the template functions directly.

            // 如果启用了多账号功能，同步账号列表到账号管理器
            if (configToSave.enableMultiAccount && configToSave.accountList) {
                try {
                    const accountManager = require('./xhs_account_manager.js');
                    if (accountManager && typeof accountManager.setAccountList === 'function') {
                        accountManager.setAccountList(configToSave.accountList);
                        utilsObject.log("UI: 账号列表已同步到账号管理器");
                    }
                } catch (e) {
                    utilsObject.logError("UI: 同步账号列表失败: " + e.toString());
                }
            }

            toast("配置已保存");
        } else {
            toast("保存配置功能暂不可用 (configManager missing)");
        }
    });

    uiObject.openAccessibilityBtn.on("click", () => {
        utilsObject.log("开启无障碍服务按钮被点击");
        utilsObject.checkAndRequestAccessibility();
    });

    // 导出功能现在由用户列表模块处理，这里保留兼容性
    if (uiObject.exportCsvBtn) {
        uiObject.exportCsvBtn.on("click", function () {
            utilsObject.log("导出CSV按钮被点击 (兼容模式)");
            if (dataManagerObject && typeof dataManagerObject.exportUsersToCSV === 'function') {
                // 使用新的采集用户数据
                var scrapedUsers = dataManagerObject.getScrapedCommentUsers();
                if (scrapedUsers && scrapedUsers.length > 0) {
                    var timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
                    var filePath = files.join(files.getSdcardPath(), "XHS_Users_Export_" + timestamp + ".csv");
                    dataManagerObject.exportUsersToCSV(scrapedUsers, filePath);
                } else {
                    // 后备数据
                    var filePath = files.join(files.getSdcardPath(), "XHS_Users_Export.csv");
                    var usersToExport = [{ nickname: "示例用户(无数据)", user_id: "test_id_placeholder", comment: "示例评论", status: "pending" }];
                    dataManagerObject.exportUsersToCSV(usersToExport, filePath);
                }
            } else {
                toast("导出CSV功能暂不可用 (dataManager missing)");
            }
        });
    }

    if (uiObject.exportTxtBtn) {
        uiObject.exportTxtBtn.on("click", function () {
            utilsObject.log("导出TXT按钮被点击 (兼容模式)");
            if (dataManagerObject && typeof dataManagerObject.exportUsersToTXT === 'function') {
                // 使用新的采集用户数据
                var scrapedUsers = dataManagerObject.getScrapedCommentUsers();
                if (scrapedUsers && scrapedUsers.length > 0) {
                    var timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
                    var filePath = files.join(files.getSdcardPath(), "XHS_Users_Export_" + timestamp + ".txt");
                    dataManagerObject.exportUsersToTXT(scrapedUsers, filePath);
                } else {
                    // 后备数据
                    var filePath = files.join(files.getSdcardPath(), "XHS_Users_Export.txt");
                    var usersToExport = [{ nickname: "示例用户(无数据)", user_id: "test_id_placeholder", comment: "示例评论", status: "pending" }];
                    dataManagerObject.exportUsersToTXT(usersToExport, filePath);
                }
            } else {
                toast("导出TXT功能暂不可用 (dataManager missing)");
            }
        });
    }

    // Event listener for the "笔记截流(评论笔记)" checkbox
    if (uiObject.task_comment_notes) {
        uiObject.task_comment_notes.on("check", function (checked) {
            if (configManagerObject && typeof configManagerObject.setNoteCommentingEnable === 'function') {
                utilsObject.log("UI: task_comment_notes checkbox changed, new state: " + checked);
                configManagerObject.setNoteCommentingEnable(checked);
                // Optionally, provide user feedback, though immediate save might be silent
                // toast("笔记评论设置已 " + (checked ? "开启" : "关闭"));
            } else {
                utilsObject.logWarn("UI: configManagerObject.setNoteCommentingEnable is not available to update checkbox state.");
            }
        });
    } else {
        utilsObject.logWarn("UI: task_comment_notes UI element not found for attaching 'check' handler.");
    }

    // Event listener for the "使用AI生成评论" checkbox
    if (uiObject.enableLlmComments) {
        uiObject.enableLlmComments.on("check", function (checked) {
            if (uiObject.customComments) {
                uiObject.customComments.setEnabled(!checked); // 如果选中AI，则禁用手动评论框
                utilsObject.log("UI: enableLlmComments checkbox changed, customComments enabled: " + !checked);
            }

            // 立即保存配置状态
            if (configManagerObject && typeof configManagerObject.saveConfig === 'function') {
                // 获取当前配置
                var currentConfig = configManagerObject.loadConfig();
                // 更新AI评论设置
                currentConfig.enableLlmComments = checked;
                // 保存配置
                configManagerObject.saveConfig(currentConfig);
                utilsObject.log("UI: enableLlmComments状态已保存: " + checked);
                toast("AI评论设置已" + (checked ? "启用" : "禁用"));
            }
        });
    } else {
        utilsObject.logWarn("UI: enableLlmComments UI element not found for attaching 'check' handler.");
    }

    // Event listener for "使用豆包AI" checkbox
    if (uiObject.useDoubaoProxy) {
        uiObject.useDoubaoProxy.on("check", (checked) => {
            // 根据选择启用/禁用相关输入框
            if (uiObject.doubaoPhoneNumber) uiObject.doubaoPhoneNumber.setEnabled(checked);
            if (uiObject.llmApiUrl) uiObject.llmApiUrl.setEnabled(!checked);
            if (uiObject.llmModelName) uiObject.llmModelName.setEnabled(!checked);

            utilsObject.log("UI: Doubao AI " + (checked ? "enabled" : "disabled"));

            // 立即保存配置状态
            if (configManagerObject && typeof configManagerObject.saveConfig === 'function') {
                var currentConfig = configManagerObject.loadConfig();
                currentConfig.useDoubaoProxy = checked;
                configManagerObject.saveConfig(currentConfig);
                utilsObject.log("UI: useDoubaoProxy状态已保存: " + checked);
            }
        });
    }

    // Event listeners for AI analysis method checkboxes (mutual exclusive)
    if (uiObject.llmUseContentExtraction) {
        uiObject.llmUseContentExtraction.on("check", (checked) => {
            if (checked && uiObject.llmUseShareLink) {
                uiObject.llmUseShareLink.checked = false; // 取消另一个选项
                utilsObject.log("UI: Content extraction selected, share link deselected");
            }

            // 立即保存配置状态
            if (configManagerObject && typeof configManagerObject.saveConfig === 'function') {
                var currentConfig = configManagerObject.loadConfig();
                currentConfig.llmUseContentExtraction = checked;
                if (checked) {
                    currentConfig.llmUseShareLink = false; // 确保互斥
                }
                configManagerObject.saveConfig(currentConfig);
                utilsObject.log("UI: llmUseContentExtraction状态已保存: " + checked);
            }
        });
    }

    if (uiObject.llmUseShareLink) {
        uiObject.llmUseShareLink.on("check", (checked) => {
            if (checked && uiObject.llmUseContentExtraction) {
                uiObject.llmUseContentExtraction.checked = false; // 取消另一个选项
                utilsObject.log("UI: Share link selected, content extraction deselected");
            }

            // 立即保存配置状态
            if (configManagerObject && typeof configManagerObject.saveConfig === 'function') {
                var currentConfig = configManagerObject.loadConfig();
                currentConfig.llmUseShareLink = checked;
                if (checked) {
                    currentConfig.llmUseContentExtraction = false; // 确保互斥
                }
                configManagerObject.saveConfig(currentConfig);
                utilsObject.log("UI: llmUseShareLink状态已保存: " + checked);
            }
        });
    }

    // --- LLM Prompt Template Management Handlers ---

    // 确保 currentConfig 被初始化
    if (!configManagerObject.currentConfig) {
        configManagerObject.currentConfig = configManagerObject.loadConfig();
    }

    function updateTemplateDisplay() {
        // 确保有模板数据
        if (!configManagerObject.currentConfig.llmPromptTemplates) {
            configManagerObject.currentConfig.llmPromptTemplates = [
                { name: "默认模板", content: "请针对以下小红书笔记内容，生成一条友好且相关的评论。笔记内容：\n{笔记内容}" }
            ];
        }

        var templates = configManagerObject.currentConfig.llmPromptTemplates || [];
        var selectedTemplateName = configManagerObject.currentConfig.selectedLlmPromptTemplateName;

        if (templates.length === 0) {
            if (uiObject.templateSelectorBtn) uiObject.templateSelectorBtn.setText("无可用模板");
            if (uiObject.selectedLlmPromptContent) uiObject.selectedLlmPromptContent.setText("");
            return;
        }

        // 找到当前选中的模板
        let currentTemplate = templates.find(t => t.name === selectedTemplateName);
        if (!currentTemplate) {
            currentTemplate = templates[0]; // 默认第一个
            configManagerObject.currentConfig.selectedLlmPromptTemplateName = currentTemplate.name;
        }

        // 更新显示
        if (uiObject.templateSelectorBtn) {
            uiObject.templateSelectorBtn.setText(`${currentTemplate.name} ▼`);
        }
        if (uiObject.selectedLlmPromptContent) {
            uiObject.selectedLlmPromptContent.setText(currentTemplate.content || "");
        }

        utilsObject.log(`UI: 显示模板 "${currentTemplate.name}"`);
    }

    function showTemplateSelector() {
        const templates = configManagerObject.currentConfig.llmPromptTemplates || [];
        if (templates.length === 0) {
            toast("没有可用的模板");
            return;
        }

        const templateNames = templates.map(t => t.name);
        const currentSelected = configManagerObject.currentConfig.selectedLlmPromptTemplateName;
        let selectedIndex = templateNames.indexOf(currentSelected);
        if (selectedIndex === -1) selectedIndex = 0;

        // 使用 Auto.js 的选择对话框
        dialogs.singleChoice("选择提示词模板", templateNames, selectedIndex)
            .then(function (index) {
                if (index >= 0 && index < templates.length) {
                    const selectedTemplate = templates[index];
                    configManagerObject.currentConfig.selectedLlmPromptTemplateName = selectedTemplate.name;
                    updateTemplateDisplay();
                    configManagerObject.saveConfig(configManagerObject.currentConfig);
                    utilsObject.log(`UI: 用户选择了模板: ${selectedTemplate.name}`);
                }
            })
            .catch(function (error) {
                utilsObject.log(`UI: 模板选择对话框错误: ${error}`);
            });
    }

    // 模板选择按钮
    if (uiObject.templateSelectorBtn) {
        uiObject.templateSelectorBtn.on("click", () => {
            showTemplateSelector();
        });
    }

    // 刷新模板按钮
    if (uiObject.refreshTemplatesBtn) {
        uiObject.refreshTemplatesBtn.on("click", () => {
            updateTemplateDisplay();
            toast("模板列表已刷新");
        });
    }

    // 编辑选中模板按钮
    if (uiObject.editSelectedTemplateBtn) {
        uiObject.editSelectedTemplateBtn.on("click", () => {
            if (!configManagerObject.currentConfig) {
                toast("配置未加载");
                return;
            }

            const templates = configManagerObject.currentConfig.llmPromptTemplates || [];
            const selectedTemplateName = configManagerObject.currentConfig.selectedLlmPromptTemplateName;

            if (!selectedTemplateName) {
                toast("没有选中的模板");
                return;
            }

            const selectedTemplate = templates.find(t => t.name === selectedTemplateName);
            if (!selectedTemplate) {
                toast("找不到选中的模板");
                return;
            }

            // 将选中模板的内容填充到编辑区域
            if (uiObject.newPromptTemplateName) {
                uiObject.newPromptTemplateName.setText(selectedTemplate.name);
            }
            if (uiObject.newPromptTemplateContent) {
                uiObject.newPromptTemplateContent.setText(selectedTemplate.content || "");
            }

            toast("模板已加载到编辑区域，修改后点击'保存模板'");
            utilsObject.log(`UI: 加载模板到编辑区域: ${selectedTemplate.name}`);
        });
    }

    // 清空编辑区域按钮
    if (uiObject.clearEditAreaBtn) {
        uiObject.clearEditAreaBtn.on("click", () => {
            if (uiObject.newPromptTemplateName) {
                uiObject.newPromptTemplateName.setText("");
            }
            if (uiObject.newPromptTemplateContent) {
                uiObject.newPromptTemplateContent.setText("");
            }
            toast("编辑区域已清空");
            utilsObject.log("UI: 清空了模板编辑区域");
        });
    }

    if (uiObject.addLlmPromptTemplateBtn) {
        uiObject.addLlmPromptTemplateBtn.on("click", () => {
            const newName = uiObject.newPromptTemplateName.getText().toString().trim();
            const newContent = uiObject.newPromptTemplateContent.getText().toString().trim();

            if (!newName) {
                toast("模板名称不能为空");
                return;
            }
            if (!newContent) {
                toast("模板内容不能为空");
                return;
            }
            if (!configManagerObject.currentConfig) return;

            let templates = configManagerObject.currentConfig.llmPromptTemplates || [];
            const existingIndex = templates.findIndex(t => t.name === newName);

            let actionType = "";
            if (existingIndex !== -1) { // Update existing
                templates[existingIndex].content = newContent;
                actionType = "更新";
                utilsObject.log(`UI: Updated prompt template: ${newName}`);
            } else { // Add new
                templates.push({ name: newName, content: newContent });
                actionType = "添加";
                utilsObject.log(`UI: Added new prompt template: ${newName}`);
            }

            configManagerObject.currentConfig.llmPromptTemplates = templates;
            configManagerObject.currentConfig.selectedLlmPromptTemplateName = newName; // Select the new/updated one

            // 刷新模板显示
            updateTemplateDisplay();

            // 清空编辑区域
            uiObject.newPromptTemplateName.setText("");
            uiObject.newPromptTemplateContent.setText("");

            configManagerObject.saveConfig(configManagerObject.currentConfig);
            toast(`模板已${actionType}: ${newName}`);
        });
    }

    if (uiObject.deleteLlmPromptTemplateBtn) {
        uiObject.deleteLlmPromptTemplateBtn.on("click", () => {
            if (!configManagerObject.currentConfig || !utilsObject) {
                if (utilsObject) utilsObject.logWarn("UI: deleteLlmPromptTemplateBtn prerequisites not met (currentConfig or utilsObject).");
                toast("删除操作暂时无法执行。");
                return;
            }

            let templates = configManagerObject.currentConfig.llmPromptTemplates || [];

            if (templates.length <= 1) {
                toast("不能删除最后一个模板。");
                utilsObject.log("UI: Attempted to delete the last prompt template.");
                return;
            }

            const currentSelectedName = configManagerObject.currentConfig.selectedLlmPromptTemplateName;
            const templateIndex = templates.findIndex(t => t.name === currentSelectedName);

            if (templateIndex === -1) {
                toast("当前没有选中的模板。");
                return;
            }

            const deletedTemplateName = templates[templateIndex].name;
            templates.splice(templateIndex, 1); // Remove the template
            configManagerObject.currentConfig.llmPromptTemplates = templates;
            utilsObject.log(`UI: Deleted prompt template: ${deletedTemplateName}`);

            // 选择一个新的模板
            if (templates.length > 0) {
                let newIndex = templateIndex;
                if (newIndex >= templates.length) {
                    newIndex = templates.length - 1;
                }
                configManagerObject.currentConfig.selectedLlmPromptTemplateName = templates[newIndex].name;
            } else {
                configManagerObject.currentConfig.selectedLlmPromptTemplateName = "";
            }

            updateTemplateDisplay(); // 刷新显示

            configManagerObject.saveConfig(configManagerObject.currentConfig);
            toast("模板已删除: " + deletedTemplateName);
        });
    }

    // --- End LLM Prompt Template Management Handlers ---

    // Comment Scraping Button Handlers
    if (uiObject.startCommentScrapingBtn) {
        uiObject.startCommentScrapingBtn.on("click", () => {
            utilsObject.log("开始评论采集按钮被点击");
            if (mainTaskManager && typeof mainTaskManager.startCommentUserScraping === 'function') {
                mainTaskManager.startCommentUserScraping();
            } else {
                toast("错误：无法触达开始评论采集处理函数 (mainTaskManager)");
                utilsObject.logError("UI: mainTaskManager or startCommentUserScraping is not available.");
            }
        });
    } else {
        utilsObject.logWarn("UI: startCommentScrapingBtn UI element not found for attaching handler.");
    }

    if (uiObject.stopCommentScrapingBtn) {
        uiObject.stopCommentScrapingBtn.on("click", () => {
            utilsObject.log("停止评论采集按钮被点击");
            if (mainTaskManager && typeof mainTaskManager.stopCommentUserScraping === 'function') {
                mainTaskManager.stopCommentUserScraping();
            } else {
                toast("错误：无法触达停止评论采集处理函数 (mainTaskManager)");
                utilsObject.logError("UI: mainTaskManager or stopCommentUserScraping is not available.");
            }
        });
    } else {
        utilsObject.logWarn("UI: stopCommentScrapingBtn UI element not found for attaching handler.");
    }

    if (configManagerObject && typeof configManagerObject.loadConfig === 'function') {
        const loadedConfig = configManagerObject.loadConfig();
        if (loadedConfig) {
            // 加载旧版配置项
            if (uiObject.searchKeyword) uiObject.searchKeyword.setText(loadedConfig.searchKeyword || "");
            if (uiObject.bloggerId) uiObject.bloggerId.setText(loadedConfig.bloggerId || "");

            // 加载任务设置
            if (uiObject.commentKeywords) uiObject.commentKeywords.setText(loadedConfig.commentKeywords || "");
            if (uiObject.targetRegion) uiObject.targetRegion.setSelection(loadedConfig.targetRegion || 0);
            if (uiObject.messageTemplate) uiObject.messageTemplate.setText(loadedConfig.messageTemplate || "");
            if (uiObject.accounts) uiObject.accounts.setText(loadedConfig.accounts || "");

            // 加载搜索偏好
            if (uiObject.search_keyword) uiObject.search_keyword.setText(loadedConfig.advanced_search_keyword || "");
            if (uiObject.search_sort_by) uiObject.search_sort_by.setSelection(loadedConfig.advanced_search_sort_by || 0);
            if (uiObject.search_publish_time) uiObject.search_publish_time.setSelection(loadedConfig.advanced_search_publish_time || 0);
            if (uiObject.search_location_distance) uiObject.search_location_distance.setSelection(loadedConfig.advanced_search_location_distance || 0);

            // 加载任务类型选择
            if (uiObject.task_collect_users) uiObject.task_collect_users.checked = loadedConfig.task_collect_users !== undefined ? loadedConfig.task_collect_users : true;
            if (uiObject.task_comment_notes) uiObject.task_comment_notes.checked = loadedConfig.task_comment_notes || false;
            if (uiObject.task_like_users) uiObject.task_like_users.checked = loadedConfig.task_like_users || false;

            // 加载评论设置
            if (uiObject.customComments) uiObject.customComments.setText(loadedConfig.customComments || "");
            if (uiObject.commentMode) uiObject.commentMode.setSelection(loadedConfig.commentMode || 0);
            if (uiObject.enableCommentDeduplication) uiObject.enableCommentDeduplication.checked = loadedConfig.enableCommentDeduplication || false;

            // 加载LLM评论设置
            if (uiObject.enableLlmComments) uiObject.enableLlmComments.checked = loadedConfig.enableLlmComments || false;
            if (uiObject.useDoubaoProxy) uiObject.useDoubaoProxy.checked = loadedConfig.useDoubaoProxy || false;
            if (uiObject.doubaoPhoneNumber) uiObject.doubaoPhoneNumber.setText(loadedConfig.doubaoPhoneNumber || "");
            if (uiObject.llmApiUrl) uiObject.llmApiUrl.setText(loadedConfig.llmApiUrl || "");
            if (uiObject.llmModelName) uiObject.llmModelName.setText(loadedConfig.llmModelName || "");
            if (uiObject.llmUseContentExtraction) uiObject.llmUseContentExtraction.checked = loadedConfig.llmUseContentExtraction !== undefined ? loadedConfig.llmUseContentExtraction : true;
            if (uiObject.llmUseShareLink) uiObject.llmUseShareLink.checked = loadedConfig.llmUseShareLink || false;

            // 加载安全控制设置
            if (uiObject.safetyMinCommentCount) uiObject.safetyMinCommentCount.setText((loadedConfig.safetyMinCommentCount || 0).toString());
            if (uiObject.safetyCommentDelayMin) uiObject.safetyCommentDelayMin.setText((loadedConfig.safetyCommentDelayMin || 5).toString());
            if (uiObject.safetyCommentDelayMax) uiObject.safetyCommentDelayMax.setText((loadedConfig.safetyCommentDelayMax || 15).toString());
            if (uiObject.safetyMaxCommentsPerAccount) uiObject.safetyMaxCommentsPerAccount.setText((loadedConfig.safetyMaxCommentsPerAccount || 50).toString());

            // 加载多账号设置
            if (uiObject.enableMultiAccount) uiObject.enableMultiAccount.checked = loadedConfig.enableMultiAccount || false;
            if (uiObject.accountList) uiObject.accountList.setText(loadedConfig.accountList || "");
            if (uiObject.autoSwitchOnLimit) uiObject.autoSwitchOnLimit.checked = loadedConfig.autoSwitchOnLimit !== undefined ? loadedConfig.autoSwitchOnLimit : true;

            // Load and initialize LLM Prompt Template UI
            if (configManagerObject.currentConfig) { // ensure currentConfig is initialized
                configManagerObject.currentConfig.llmPromptTemplates = loadedConfig.llmPromptTemplates || [{ name: "默认模板", content: "请针对以下内容生成评论：\n{笔记内容}" }];
                configManagerObject.currentConfig.selectedLlmPromptTemplateName = loadedConfig.selectedLlmPromptTemplateName || (configManagerObject.currentConfig.llmPromptTemplates.length > 0 ? configManagerObject.currentConfig.llmPromptTemplates[0].name : "");
                updateTemplateDisplay();
            }
            // 设置初始状态：自定义评论框根据AI评论选项启用/禁用
            if (uiObject.enableLlmComments && uiObject.customComments) {
                const isLlmEnabled = uiObject.enableLlmComments.checked;
                uiObject.customComments.setEnabled(!isLlmEnabled);
            }

            // 设置豆包AI相关输入框的初始状态
            if (uiObject.useDoubaoProxy) {
                const useDoubao = uiObject.useDoubaoProxy.checked;
                if (uiObject.doubaoPhoneNumber) uiObject.doubaoPhoneNumber.setEnabled(useDoubao);
                if (uiObject.llmApiUrl) uiObject.llmApiUrl.setEnabled(!useDoubao);
                if (uiObject.llmModelName) uiObject.llmModelName.setEnabled(!useDoubao);
            }

            // 设置多账号相关输入框的初始状态
            if (uiObject.enableMultiAccount) {
                const enableMulti = uiObject.enableMultiAccount.checked;
                if (uiObject.accountList) uiObject.accountList.setEnabled(enableMulti);
                if (uiObject.autoSwitchOnLimit) uiObject.autoSwitchOnLimit.setEnabled(enableMulti);
            }

            // 确保模板内容显示框始终禁用编辑
            if (uiObject.selectedLlmPromptContent) {
                uiObject.selectedLlmPromptContent.setEnabled(false);
            }

            utilsObject.log("配置已加载到UI。");
        }
    }

    // 多账号管理按钮事件处理器
    if (uiObject.refreshAccountStatusBtn) {
        uiObject.refreshAccountStatusBtn.on("click", () => {
            try {
                const accountManager = require('./xhs_account_manager.js');
                if (accountManager && typeof accountManager.getAccountStatus === 'function') {
                    const accountStatus = accountManager.getAccountStatus();
                    const accountStatusText = "当前账号: " + accountStatus.currentAccount + " | 下个账号: " + accountStatus.nextAccount + " | 总数: " + accountStatus.totalAccounts;

                    if (uiObject.accountStatusDisplay) {
                        uiObject.accountStatusDisplay.setText(accountStatusText);
                    }

                    toast("账号状态已刷新");
                    utilsObject.log("UI: 账号状态已刷新 - " + accountStatusText);
                } else {
                    toast("账号管理模块不可用");
                }
            } catch (e) {
                toast("刷新账号状态失败: " + e.toString());
                utilsObject.logError("UI: 刷新账号状态失败: " + e.toString());
            }
        });
    }

    if (uiObject.switchAccountBtn) {
        uiObject.switchAccountBtn.on("click", () => {
            try {
                const accountManager = require('./xhs_account_manager.js');
                if (accountManager && typeof accountManager.switchToNextAccount === 'function') {
                    toast("正在切换账号，请稍候...");
                    utilsObject.log("UI: 手动触发账号切换");

                    // 在后台执行账号切换
                    setTimeout(() => {
                        const success = accountManager.switchToNextAccount();
                        if (success) {
                            toast("账号切换成功");
                            // 刷新账号状态显示
                            const accountStatus = accountManager.getAccountStatus();
                            const switchStatusText = "当前账号: " + accountStatus.currentAccount + " | 下个账号: " + accountStatus.nextAccount + " | 总数: " + accountStatus.totalAccounts;

                            if (uiObject.accountStatusDisplay) {
                                uiObject.accountStatusDisplay.setText(switchStatusText);
                            }
                        } else {
                            toast("账号切换失败");
                        }
                    }, 100);
                } else {
                    toast("账号管理模块不可用");
                }
            } catch (e) {
                toast("切换账号失败: " + e.toString());
                utilsObject.logError("UI: 切换账号失败: " + e.toString());
            }
        });
    }

    // 启用多账号复选框事件处理器
    if (uiObject.enableMultiAccount) {
        uiObject.enableMultiAccount.on("check", (checked) => {
            // 根据选择启用/禁用相关输入框
            if (uiObject.accountList) uiObject.accountList.setEnabled(checked);
            if (uiObject.autoSwitchOnLimit) uiObject.autoSwitchOnLimit.setEnabled(checked);

            utilsObject.log("UI: 多账号功能 " + (checked ? "启用" : "禁用"));

            // 立即保存配置状态
            if (configManagerObject && typeof configManagerObject.saveConfig === 'function') {
                var currentConfig = configManagerObject.loadConfig();
                currentConfig.enableMultiAccount = checked;
                configManagerObject.saveConfig(currentConfig);
                utilsObject.log("UI: enableMultiAccount状态已保存: " + checked);
            }
        });
    }

    // 安全状态管理按钮事件处理器
    if (uiObject.refreshSafetyStatusBtn) {
        uiObject.refreshSafetyStatusBtn.on("click", () => {
            try {
                const safetyControl = require('./xhs_safety_control.js');
                if (safetyControl && typeof safetyControl.getSafetyStatus === 'function') {
                    const safetyStatus = safetyControl.getSafetyStatus();
                    const safetyStatusText = "今日评论次数: " + safetyStatus.commentCountToday + " | 上次评论: " + safetyStatus.lastCommentTimeFormatted;

                    if (uiObject.safetyStatusDisplay) {
                        uiObject.safetyStatusDisplay.setText(safetyStatusText);
                    }

                    toast("安全状态已刷新");
                    utilsObject.log("UI: 安全状态已刷新 - " + safetyStatusText);
                } else {
                    toast("安全控制模块不可用");
                }
            } catch (e) {
                toast("刷新安全状态失败: " + e.toString());
                utilsObject.logError("UI: 刷新安全状态失败: " + e.toString());
            }
        });
    }

    if (uiObject.resetDailyCountBtn) {
        uiObject.resetDailyCountBtn.on("click", () => {
            try {
                const safetyControl = require('./xhs_safety_control.js');
                if (safetyControl && typeof safetyControl.resetDailyCount === 'function') {
                    const success = safetyControl.resetDailyCount();
                    if (success) {
                        // 刷新状态显示
                        const safetyStatus = safetyControl.getSafetyStatus();
                        const resetStatusText = "今日评论次数: " + safetyStatus.commentCountToday + " | 上次评论: " + safetyStatus.lastCommentTimeFormatted;

                        if (uiObject.safetyStatusDisplay) {
                            uiObject.safetyStatusDisplay.setText(resetStatusText);
                        }

                        toast("每日评论计数已重置");
                        utilsObject.log("UI: 每日评论计数已重置");
                    } else {
                        toast("重置失败");
                    }
                } else {
                    toast("安全控制模块不可用");
                }
            } catch (e) {
                toast("重置每日计数失败: " + e.toString());
                utilsObject.logError("UI: 重置每日计数失败: " + e.toString());
            }
        });
    }

    // 确保在初始化时显示模板
    updateTemplateDisplay();

    // 初始化账号状态显示
    try {
        const accountManager = require('./xhs_account_manager.js');
        if (accountManager && typeof accountManager.getAccountStatus === 'function') {
            const accountStatus = accountManager.getAccountStatus();
            const accountStatusText = "当前账号: " + accountStatus.currentAccount + " | 下个账号: " + accountStatus.nextAccount + " | 总数: " + accountStatus.totalAccounts;

            if (uiObject.accountStatusDisplay) {
                uiObject.accountStatusDisplay.setText(accountStatusText);
            }
        }
    } catch (e) {
        utilsObject.log("UI: 初始化账号状态显示失败: " + e.toString());
    }

    // 初始化安全状态显示
    try {
        const safetyControl = require('./xhs_safety_control.js');
        if (safetyControl && typeof safetyControl.getSafetyStatus === 'function') {
            const safetyStatus = safetyControl.getSafetyStatus();
            const safetyStatusText = "今日评论次数: " + safetyStatus.commentCountToday + " | 上次评论: " + safetyStatus.lastCommentTimeFormatted;

            if (uiObject.safetyStatusDisplay) {
                uiObject.safetyStatusDisplay.setText(safetyStatusText);
            }
        }
    } catch (e) {
        utilsObject.log("UI: 初始化安全状态显示失败: " + e.toString());
    }

    console.log("UI_REFACTORED: Event handlers attached.");
    toast("UI_REFACTORED: Event handlers attached.");
}

// UI Update functions for comment scraping
function updateScrapingStatus(uiObject, statusText) {
    if (uiObject && uiObject.scrapingStatus) {
        ui.run(() => {
            uiObject.scrapingStatus.setText("状态: " + statusText);
        });
    } else {
        console.error("UI_UPDATE_ERROR: scrapingStatus UI element not found.");
    }
}

function updateProcessedNotesCount(uiObject, count) {
    if (uiObject && uiObject.processedNotesCount) {
        ui.run(() => {
            uiObject.processedNotesCount.setText("已处理笔记: " + count);
        });
    } else {
        console.error("UI_UPDATE_ERROR: processedNotesCount UI element not found.");
    }
}

function updateCollectedUsersCount(uiObject, count) {
    if (uiObject && uiObject.collectedUsersCount) {
        ui.run(() => {
            uiObject.collectedUsersCount.setText("已采集用户: " + count);
        });
    } else {
        console.error("UI_UPDATE_ERROR: collectedUsersCount UI element not found.");
    }
}

/**
 * 更新用户统计信息显示
 * @param {Object} uiObject - UI对象
 * @param {Object} dataManagerObject - 数据管理对象
 */
function updateUserStatisticsDisplay(uiObject, dataManagerObject) {
    if (!dataManagerObject || typeof dataManagerObject.getUserStatistics !== 'function') {
        return;
    }

    try {
        const stats = dataManagerObject.getUserStatistics();

        // 更新主界面的采集用户数量
        if (uiObject && uiObject.collectedUsersCount) {
            ui.run(() => {
                uiObject.collectedUsersCount.setText("已采集用户: " + stats.totalUsers);
            });
        }

        // 更新用户列表页面的统计信息
        if (uiUserListModule && typeof uiUserListModule.refreshUserStatistics === 'function') {
            uiUserListModule.refreshUserStatistics(uiObject, dataManagerObject, { log: console.log });
        }

    } catch (e) {
        console.error("UI_UPDATE_ERROR: Failed to update user statistics:", e);
    }
}

// updateLog and updateStatusList are no longer part of this module's direct responsibility for export.
// main.js will define its own updaters that have access to the `ui` object created there.

module.exports = {
    getUILayoutXML: getUILayoutXML,
    attachUIEventHandlers: attachUIEventHandlers,
    updateScrapingStatus: updateScrapingStatus,
    updateProcessedNotesCount: updateProcessedNotesCount,
    updateCollectedUsersCount: updateCollectedUsersCount,
    updateUserStatisticsDisplay: updateUserStatisticsDisplay
};