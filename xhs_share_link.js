/**
 * XHS Share Link Module
 * 负责复制小红书笔记的分享链接
 */

var utils = require('./utils.js');
var noteTypes = require('./xhs_note_types.js');

/**
 * Android 10+兼容的剪贴板获取函数
 * 通过创建悬浮窗获取焦点来读取剪贴板
 */
function getClipboardWithFocus() {
    try {
        utils.log("SHARE_LINK: 使用悬浮窗方式获取剪贴板内容");

        var clipboardContent = null;
        var completed = false;

        ui.run(() => {
            var w = floaty.rawWindow(
                '<vertical>\
                    <img src="@drawable/ic_description_black_48dp" h="20" />\
                </vertical>'
            );
            w.setTouchable(false);
            w.requestFocus();

            var timer = setInterval(() => {
                if (completed) {
                    try {
                        clipboardContent = getClip();
                        utils.log("SHARE_LINK: 悬浮窗方式获取到剪贴板内容: " + (clipboardContent || "空"));
                    } catch (e) {
                        utils.log("SHARE_LINK: 悬浮窗方式获取剪贴板异常: " + e);
                    } finally {
                        w.close();
                        clearInterval(timer);
                    }
                }
            }, 10);
        });

        // 触发获取
        sleep(100);
        completed = true;

        // 等待操作完成
        var waitCount = 0;
        while (clipboardContent === null && waitCount < 30) { // 最多等待3秒
            sleep(100);
            waitCount++;
        }

        return clipboardContent;

    } catch (error) {
        utils.log("SHARE_LINK: 悬浮窗方式获取剪贴板异常: " + error);

        // 回退到普通方式
        try {
            utils.log("SHARE_LINK: 回退到普通getClip方式");
            return getClip();
        } catch (fallbackError) {
            utils.log("SHARE_LINK: 普通方式也失败: " + fallbackError);
            return null;
        }
    }
}

/**
 * 复制图文笔记的分享链接
 * @returns {string|null} 返回复制的链接，失败返回null
 */
function copyImageTextNoteShareLink() {
    try {
        utils.log("SHARE_LINK: 开始复制图文笔记分享链接");

        // 在id为com.xingin.xhs:id/iw2的顶部容器内查找转发按钮
        var topContainer = id("com.xingin.xhs:id/iw2").findOne(3000);
        if (!topContainer) {
            utils.log("SHARE_LINK: 未找到图文笔记顶部容器 (iw2)");
            return null;
        }

        var moreOperateButton = topContainer.findOne(id("com.xingin.xhs:id/moreOperateIV"));
        if (!moreOperateButton) {
            utils.log("SHARE_LINK: 未找到图文笔记转发按钮 (moreOperateIV)");
            return null;
        }

        utils.log("SHARE_LINK: 点击图文笔记转发按钮");
        if (!moreOperateButton.click()) {
            utils.log("SHARE_LINK: 点击图文笔记转发按钮失败");
            return null;
        }

        sleep(2000); // 等待菜单弹出

        // 查找功能菜单组件
        var contentMenu = id("android:id/content").findOne(3000);
        if (!contentMenu) {
            utils.log("SHARE_LINK: 未找到功能菜单组件 (android:id/content)");
            return null;
        }

        // 第一步：通过desc定位到"复制链接"按钮的位置
        var copyLinkDescElement = contentMenu.findOne(desc("复制链接"));

        if (!copyLinkDescElement) {
            utils.log("SHARE_LINK: 未找到desc='复制链接'的元素");

            // 调试：列出菜单中所有有desc属性的元素
            var allElements = contentMenu.find(desc(/.*/));
            utils.log("SHARE_LINK: 菜单中共找到 " + allElements.length + " 个有desc的元素");
            for (var i = 0; i < allElements.length && i < 10; i++) {
                var elem = allElements[i];
                var elemDesc = elem.desc() || "null";
                var elemId = elem.id() || "null";
                var elemClass = elem.className() || "null";
                utils.log("SHARE_LINK: 元素" + i + " - desc: '" + elemDesc + "', id: '" + elemId + "', class: '" + elemClass + "'");
            }
            return null;
        }

        utils.log("SHARE_LINK: 找到desc='复制链接'的元素");

        // 第二步：在复制链接Button下查找可点击的ViewGroup
        var copyLinkButton = null;

        utils.log("SHARE_LINK: 分析desc元素的层级结构");
        utils.log("SHARE_LINK: desc元素信息 - class: " + copyLinkDescElement.className() + ", clickable: " + copyLinkDescElement.clickable());

        // 方式1：查找desc元素的直接子元素中的可点击ViewGroup
        var children = copyLinkDescElement.children();
        utils.log("SHARE_LINK: desc元素有 " + children.length + " 个子元素");

        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var childClass = child.className() || "";
            var childClickable = child.clickable();
            utils.log("SHARE_LINK: 子元素" + i + " - class: " + childClass + ", clickable: " + childClickable);

            if (childClass.indexOf("ViewGroup") >= 0 && childClickable) {
                copyLinkButton = child;
                utils.log("SHARE_LINK: 找到可点击的ViewGroup子元素");
                break;
            }
        }

        // 方式2：如果没找到，尝试查找所有后代中的可点击ViewGroup
        if (!copyLinkButton) {
            utils.log("SHARE_LINK: 在直接子元素中未找到，查找所有后代元素");
            var allDescendants = copyLinkDescElement.find(className(/.*ViewGroup.*/));
            utils.log("SHARE_LINK: 找到 " + allDescendants.length + " 个ViewGroup后代元素");

            for (var j = 0; j < allDescendants.length; j++) {
                var descendant = allDescendants[j];
                if (descendant.clickable()) {
                    copyLinkButton = descendant;
                    utils.log("SHARE_LINK: 找到可点击的ViewGroup后代元素");
                    break;
                }
            }
        }

        // 调试：显示找到的元素信息
        if (copyLinkButton) {
            utils.log("SHARE_LINK: 最终找到的可点击元素信息:");
            utils.log("SHARE_LINK: - ID: " + (copyLinkButton.id() || "null"));
            utils.log("SHARE_LINK: - Class: " + (copyLinkButton.className() || "null"));
            utils.log("SHARE_LINK: - Desc: " + (copyLinkButton.desc() || "null"));
            utils.log("SHARE_LINK: - Text: " + (copyLinkButton.text() || "null"));
            utils.log("SHARE_LINK: - Clickable: " + copyLinkButton.clickable());
            utils.log("SHARE_LINK: - Bounds: " + JSON.stringify(copyLinkButton.bounds()));
        }

        if (!copyLinkButton) {
            utils.log("SHARE_LINK: 所有方式都未找到复制链接按钮");

            // 调试：截图保存当前界面
            try {
                var timestamp = new Date().getTime();
                var screenshotPath = "/sdcard/xhs_debug_menu_" + timestamp + ".png";
                captureScreen(screenshotPath);
                utils.log("SHARE_LINK: 已保存调试截图到: " + screenshotPath);
            } catch (screenshotError) {
                utils.log("SHARE_LINK: 截图失败: " + screenshotError);
            }

            return null;
        }

        // 清空剪贴板，确保能准确检测复制结果
        setClip("");
        utils.log("SHARE_LINK: 已清空剪贴板");

        utils.log("SHARE_LINK: 点击复制链接按钮");
        var clickSuccess = false;

        // 尝试方式1: 直接点击
        if (copyLinkButton.click()) {
            clickSuccess = true;
            utils.log("SHARE_LINK: 直接点击成功");
        } else {
            utils.log("SHARE_LINK: 直接点击失败，尝试坐标点击");

            // 尝试方式2: 坐标点击
            try {
                var bounds = copyLinkButton.bounds();
                var centerX = bounds.centerX();
                var centerY = bounds.centerY();
                utils.log("SHARE_LINK: 尝试点击坐标 (" + centerX + ", " + centerY + ")");

                if (click(centerX, centerY)) {
                    clickSuccess = true;
                    utils.log("SHARE_LINK: 坐标点击成功");
                } else {
                    utils.log("SHARE_LINK: 坐标点击也失败");
                }
            } catch (coordError) {
                utils.log("SHARE_LINK: 坐标点击异常: " + coordError);
            }
        }

        if (!clickSuccess) {
            utils.log("SHARE_LINK: 所有点击方式都失败");
            return null;
        }

        // 等待复制完成并获取剪贴板内容（Android 10+兼容方案）
        utils.log("SHARE_LINK: 等待复制完成...");
        sleep(2000); // 等待复制操作完成

        var clipboardText = getClipboardWithFocus();
        utils.log("SHARE_LINK: 剪贴板内容: " + clipboardText);

        return clipboardText;

    } catch (error) {
        utils.log("SHARE_LINK: 复制图文笔记链接时发生异常: " + error);
        return null;
    }
}

/**
 * 复制视频笔记的分享链接
 * @returns {string|null} 返回复制的链接，失败返回null
 */
function copyVideoNoteShareLink() {
    try {
        utils.log("SHARE_LINK: 开始复制视频笔记分享链接");

        // 先找到wm容器，再在其中找到c9y功能按钮组件
        var wmContainer = id("com.xingin.xhs:id/wm").findOne(3000);
        if (!wmContainer) {
            utils.log("SHARE_LINK: 未找到视频笔记容器 (wm)");
            return null;
        }

        // 在wm容器中查找c9y功能按钮组件
        var c9yContainer = wmContainer.findOne(id("com.xingin.xhs:id/c9y"));
        if (!c9yContainer) {
            utils.log("SHARE_LINK: 未找到视频笔记功能按钮组件 (c9y)");
            return null;
        }

        utils.log("SHARE_LINK: 找到c9y功能按钮组件，查找转发按钮...");

        // 在c9y组件中获取转发按钮 (indexInParent=4)
        var shareButton = c9yContainer.child(4);
        if (!shareButton) {
            utils.log("SHARE_LINK: 未找到视频笔记转发按钮 (indexInParent=4)");
            return null;
        }

        utils.log("SHARE_LINK: 找到转发按钮 (c9y.child(4))");

        utils.log("SHARE_LINK: 点击视频笔记转发按钮");
        if (!shareButton.click()) {
            utils.log("SHARE_LINK: 点击视频笔记转发按钮失败");
            return null;
        }

        sleep(2000); // 等待菜单弹出

        // 查找视频笔记专用的功能菜单容器 (gg9)
        var gg9Container = id("com.xingin.xhs:id/gg9").findOne(3000);
        if (!gg9Container) {
            utils.log("SHARE_LINK: 未找到视频笔记功能菜单容器 (gg9)");
            return null;
        }

        // 在gg9容器中查找RecyclerView
        var recyclerView = gg9Container.findOne(className("androidx.recyclerview.widget.RecyclerView"));
        if (!recyclerView) {
            utils.log("SHARE_LINK: 未找到RecyclerView");
            return null;
        }

        utils.log("SHARE_LINK: 找到RecyclerView，尝试滑动查找复制链接");

        // 调试：显示RecyclerView中所有子元素
        var childCount = recyclerView.childCount();
        utils.log("SHARE_LINK: RecyclerView有 " + childCount + " 个子元素");

        for (var i = 0; i < childCount; i++) {
            try {
                var child = recyclerView.child(i);
                if (child) {
                    var childDesc = child.desc() || "";
                    var childId = child.id() || "";
                    utils.log("SHARE_LINK: 子元素[" + i + "] desc: '" + childDesc + "', id: '" + childId + "'");
                }
            } catch (e) {
                utils.log("SHARE_LINK: 检查子元素[" + i + "]时出错: " + e);
            }
        }

        // 在RecyclerView中滑动查找复制链接
        var maxSwipes = 5;
        var copyLinkElement = null;

        // 先尝试直接查找复制链接
        copyLinkElement = gg9Container.findOne(desc("复制链接"));
        if (copyLinkElement) {
            utils.log("SHARE_LINK: ✓ 直接在gg9容器中找到复制链接！");
        } else {
            utils.log("SHARE_LINK: 在gg9容器中未找到复制链接，开始滑动...");

            for (var swipeCount = 0; swipeCount < maxSwipes; swipeCount++) {
                utils.log("SHARE_LINK: 进行第" + (swipeCount + 1) + "次左滑");

                // 使用scrollForward方法滑动RecyclerView
                utils.log("SHARE_LINK: 使用scrollForward方法滑动RecyclerView");
                try {
                    recyclerView.scrollForward();
                    utils.log("SHARE_LINK: scrollForward执行成功");
                } catch (scrollError) {
                    utils.log("SHARE_LINK: scrollForward失败，使用手动滑动: " + scrollError);

                    // 备用方案：手动滑动
                    var bounds = recyclerView.bounds();
                    var startX = bounds.right - 100;
                    var endX = bounds.left + 100;
                    var centerY = bounds.centerY();

                    utils.log("SHARE_LINK: 手动左滑 - 从(" + startX + "," + centerY + ")到(" + endX + "," + centerY + ")");
                    swipe(startX, centerY, endX, centerY, 500);
                }

                // 滑动后等待DOM刷新并多次尝试查找
                utils.log("SHARE_LINK: 滑动后等待DOM刷新...");
                sleep(3000); // 增加等待时间让DOM完全刷新

                // 多次尝试查找，解决DOM刷新延迟问题
                var found = false;
                var maxRetries = 5;

                for (var retry = 0; retry < maxRetries && !found; retry++) {
                    utils.log("SHARE_LINK: 第" + (retry + 1) + "次尝试查找复制链接...");

                    // 重新获取gg9容器，避免DOM缓存问题
                    var freshGg9Container = id("com.xingin.xhs:id/gg9").findOne(1000);
                    if (!freshGg9Container) {
                        utils.log("SHARE_LINK: 重新获取gg9容器失败");
                        continue;
                    }

                    // 策略1: 全局查找text="复制链接"
                    var copyLinkTextView = text("复制链接").findOne(1000);
                    if (copyLinkTextView) {
                        utils.log("SHARE_LINK: ✓ 全局找到text='复制链接'的textview");

                        // 从textview向上查找hzs元素
                        var hzsElement = null;
                        var currentElement = copyLinkTextView;
                        var maxLevels = 5; // 最多向上查找5层

                        for (var level = 0; level < maxLevels; level++) {
                            currentElement = currentElement.parent();
                            if (!currentElement) {
                                utils.log("SHARE_LINK: 向上查找第" + (level + 1) + "层时到达顶层");
                                break;
                            }

                            var elementId = currentElement.id() || "";
                            var elementClass = currentElement.className() || "";
                            utils.log("SHARE_LINK: 向上查找第" + (level + 1) + "层 - id: '" + elementId + "', class: '" + elementClass + "'");

                            if (elementId === "com.xingin.xhs:id/hzs") {
                                hzsElement = currentElement;
                                utils.log("SHARE_LINK: ✓ 找到hzs元素！");
                                break;
                            }
                        }

                        if (hzsElement) {
                            copyLinkElement = hzsElement;
                            found = true;
                            break;
                        } else {
                            utils.log("SHARE_LINK: 未找到hzs元素，使用textview作为备选");
                            copyLinkElement = copyLinkTextView;
                            found = true;
                            break;
                        }
                    }

                    // 策略2: 在新的gg9容器中查找
                    copyLinkTextView = freshGg9Container.findOne(text("复制链接"));
                    if (copyLinkTextView) {
                        utils.log("SHARE_LINK: ✓ 在新gg9容器中找到text='复制链接'的textview");
                        copyLinkElement = copyLinkTextView;
                        found = true;
                        break;
                    }

                    // 策略3: 查找desc="复制链接"
                    var copyLinkDescElement = desc("复制链接").findOne(1000);
                    if (copyLinkDescElement) {
                        utils.log("SHARE_LINK: ✓ 找到desc='复制链接'的元素");
                        copyLinkElement = copyLinkDescElement;
                        found = true;
                        break;
                    }

                    if (!found && retry < maxRetries - 1) {
                        utils.log("SHARE_LINK: 第" + (retry + 1) + "次查找失败，等待1秒后重试...");
                        sleep(1000);
                    }
                }

                if (found) {
                    break;
                } else {
                    utils.log("SHARE_LINK: 滑动后仍未找到复制链接");
                }
            }
        }

        if (!copyLinkElement) {
            utils.log("SHARE_LINK: 滑动" + maxSwipes + "次后仍未找到复制链接");
            return null;
        }

        // 确定要点击的元素
        var hzsButton = null;
        var copyLinkElementId = copyLinkElement.id() || "";

        if (copyLinkElementId === "com.xingin.xhs:id/hzs") {
            // 如果copyLinkElement本身就是hzs元素，直接使用
            hzsButton = copyLinkElement;
            utils.log("SHARE_LINK: copyLinkElement本身就是hzs元素");
        } else {
            // 否则在copyLinkElement中查找hzs元素
            hzsButton = copyLinkElement.findOne(id("com.xingin.xhs:id/hzs"));
            if (hzsButton) {
                utils.log("SHARE_LINK: 在copyLinkElement中找到hzs按钮元素");
            } else {
                utils.log("SHARE_LINK: 在copyLinkElement中未找到hzs按钮，尝试直接点击copyLinkElement");
                hzsButton = copyLinkElement;
            }
        }

        utils.log("SHARE_LINK: 准备点击元素，id: '" + (hzsButton.id() || "") + "', class: '" + (hzsButton.className() || "") + "'");

        // 清空剪贴板，确保能准确检测复制结果
        setClip("");
        utils.log("SHARE_LINK: 已清空剪贴板");

        utils.log("SHARE_LINK: 点击复制链接按钮 (hzs)");
        var clickSuccess = false;

        // 尝试方式1: 直接点击hzs按钮
        if (hzsButton.click()) {
            clickSuccess = true;
            utils.log("SHARE_LINK: 直接点击hzs按钮成功");
        } else {
            utils.log("SHARE_LINK: 直接点击hzs按钮失败，尝试坐标点击");

            // 尝试方式2: 坐标点击
            try {
                var bounds = hzsButton.bounds();
                var centerX = bounds.centerX();
                var centerY = bounds.centerY();
                utils.log("SHARE_LINK: 尝试点击坐标 (" + centerX + ", " + centerY + ")");

                if (click(centerX, centerY)) {
                    clickSuccess = true;
                    utils.log("SHARE_LINK: 坐标点击成功");
                } else {
                    utils.log("SHARE_LINK: 坐标点击也失败");
                }
            } catch (coordError) {
                utils.log("SHARE_LINK: 坐标点击异常: " + coordError);
            }
        }

        if (!clickSuccess) {
            utils.log("SHARE_LINK: 所有点击方式都失败");
            return null;
        }

        // 等待复制完成并获取剪贴板内容（Android 10+兼容方案）
        utils.log("SHARE_LINK: 等待复制完成...");
        sleep(2000); // 等待复制操作完成

        var clipboardText = getClipboardWithFocus();
        utils.log("SHARE_LINK: 剪贴板内容: " + clipboardText);

        return clipboardText;

    } catch (error) {
        utils.log("SHARE_LINK: 复制视频笔记链接时发生异常: " + error);
        return null;
    }
}

/**
 * 根据笔记类型复制分享链接
 * @param {string} noteType 笔记类型
 * @returns {string|null} 返回复制的链接，失败返回null
 */
function copyShareLinkByNoteType(noteType) {
    if (noteType === noteTypes.NOTE_TYPES.VIDEO) {
        return copyVideoNoteShareLink();
    } else {
        return copyImageTextNoteShareLink();
    }
}

/**
 * 复制当前笔记的分享链接
 * @returns {Promise<string|null>} 返回Promise，成功时resolve链接，失败时resolve null
 */
function copyCurrentNoteShareLink() {
    return new Promise(function (resolve) {
        try {
            utils.log("SHARE_LINK: 开始复制当前笔记分享链接");

            // 检测笔记类型
            var noteType = noteTypes.detectNoteTypeInDetailPage();
            utils.log("SHARE_LINK: 检测到笔记类型: " + noteTypes.getNoteTypeDescription(noteType));

            var shareLink = copyShareLinkByNoteType(noteType);

            if (shareLink) {
                utils.log("SHARE_LINK: 成功复制分享链接: " + shareLink.substring(0, 100) + "...");
                resolve(shareLink);
            } else {
                utils.log("SHARE_LINK: 复制分享链接失败");
                resolve(null);
            }

        } catch (error) {
            utils.log("SHARE_LINK: 复制分享链接过程中发生异常: " + error);
            resolve(null);
        }
    });
}

/**
 * 测试分享链接功能 - 用于调试
 */
function testShareLinkFunction() {
    utils.log("SHARE_LINK_TEST: 开始测试分享链接功能");

    try {
        // 检测当前是否在笔记详情页
        var noteType = noteTypes.detectNoteTypeInDetailPage();
        utils.log("SHARE_LINK_TEST: 检测到笔记类型: " + noteTypes.getNoteTypeDescription(noteType));

        if (noteType === noteTypes.NOTE_TYPES.UNKNOWN) {
            utils.log("SHARE_LINK_TEST: 当前不在笔记详情页，请先进入笔记详情页");
            toast("请先进入笔记详情页");
            return false;
        }

        // 尝试复制分享链接
        var shareLink = copyShareLinkByNoteType(noteType);

        if (shareLink) {
            utils.log("SHARE_LINK_TEST: 测试成功！复制的链接: " + shareLink);
            toast("测试成功！链接已复制到剪贴板");
            return true;
        } else {
            utils.log("SHARE_LINK_TEST: 测试失败，无法复制分享链接");
            toast("测试失败，请查看日志");
            return false;
        }

    } catch (error) {
        utils.log("SHARE_LINK_TEST: 测试过程中发生异常: " + error);
        toast("测试异常: " + error.toString());
        return false;
    }
}

module.exports = {
    copyImageTextNoteShareLink,
    copyVideoNoteShareLink,
    copyShareLinkByNoteType,
    copyCurrentNoteShareLink,
    testShareLinkFunction
};

utils.log("SHARE_LINK: XHS Share Link Module loaded successfully.");
