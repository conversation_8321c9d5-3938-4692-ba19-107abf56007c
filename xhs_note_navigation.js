/**
 * 小红书笔记导航模块
 * 负责笔记识别、导航和批量处理
 */

const utils = require('./utils.js');
const noteTypes = require('./xhs_note_types.js');
const videoComments = require('./xhs_video_comments.js');
const simpleComments = require('./xhs_simple_comments.js');
const configManager = require('./config.js');
const noteCommenting = require('./xhs_note_commenting.js');

// 选择器定义
const SELECTORS = {
    NOTE_CARD_ITEM: { id: "com.xingin.xhs:id/hpx" },
    NOTE_CARD_TITLE: { id: "com.xingin.xhs:id/g_q" },
    NOTE_CARD_USER_NICKNAME: { id: "com.xingin.xhs:id/zb" }
};

/**
 * 提取当前屏幕可见的笔记（支持笔记类型检测）
 * @returns {Array} 笔记信息数组
 */
function extractVisibleNotes() {
    utils.log("NOTE_NAV: 开始提取当前屏幕可见的笔记...");
    let notesOnScreen = [];
    const containers = id(SELECTORS.NOTE_CARD_ITEM.id).find();

    if (containers.empty()) {
        utils.log("NOTE_NAV: 当前屏幕未找到笔记容器");
        return notesOnScreen;
    }

    utils.log("NOTE_NAV: 找到 " + containers.size() + " 个笔记容器");

    for (let i = 0; i < containers.size(); i++) {
        let container = containers.get(i);
        utils.log("NOTE_NAV: 处理容器 " + (i + 1) + "/" + containers.size());

        if (!container || typeof container.bounds !== 'function') {
            utils.log("NOTE_NAV: 无效的容器对象，跳过");
            continue;
        }

        // 在搜索结果页面暂时跳过笔记类型检测，因为不够准确
        // 笔记类型将在进入详情页后进行检测
        const noteType = noteTypes.NOTE_TYPES.UNKNOWN; // 暂时标记为未知
        utils.log(`NOTE_NAV: 搜索页面暂不检测笔记类型，将在详情页检测`);

        // 检查是否为直播笔记（这个在搜索页面比较可靠）
        try {
            const liveElement = container.findOne(id("com.xingin.xhs:id/ikj").text("直播中"));
            if (liveElement && liveElement.visibleToUser()) {
                utils.log("NOTE_NAV: 跳过直播笔记 (容器 " + (i + 1) + ")");
                continue;
            }
        } catch (e) {
            // 忽略直播检测错误
        }

        // 提取标题和作者信息
        let titleElement = container.findOne(id(SELECTORS.NOTE_CARD_TITLE.id));
        let authorElement = container.findOne(id(SELECTORS.NOTE_CARD_USER_NICKNAME.id));

        let titleText = titleElement ? (titleElement.text() || "").replace(/\n/g, ' ').trim() : "[No Title]";
        let authorText = authorElement ? (authorElement.text() || "").replace(/\n/g, ' ').trim() : "[No Author]";

        utils.log("NOTE_NAV: 标题: \"" + titleText.substring(0, 30) + "...\"");
        utils.log("NOTE_NAV: 作者: \"" + authorText + "\"");

        if (titleText !== "[No Title]" || authorText !== "[No Author]") {
            let noteSignature = authorText + "::" + titleText;
            notesOnScreen.push({
                title: titleText,
                author: authorText,
                signature: noteSignature,
                noteType: noteType,
                clickableElement: container,
                rawContainerInfo: {
                    boundsTop: container.bounds().top,
                    indexInParent: container.indexInParent(),
                    drawingOrder: container.drawingOrder(),
                    id: container.id(),
                    className: container.className()
                }
            });
        } else {
            utils.log("NOTE_NAV: 容器 " + (i + 1) + " 未能提取到标题或作者信息");
        }
    }

    utils.log("NOTE_NAV: 提取完成，找到 " + notesOnScreen.length + " 个有效笔记");
    return notesOnScreen;
}

/**
 * 循环处理当前屏幕内的所有笔记，采集评论
 * @param {Set} processedNoteIds - 已处理的笔记ID集合
 * @param {string} keywordsString - 评论关键词
 * @param {Function} backToPreviousPage - 返回上一页的函数
 * @param {Function} isNoteDetailPage - 检查是否在笔记详情页的函数
 * @param {number} targetRegionIndex - 目标区域索引（0表示不限）
 * @returns {Promise<object>} 处理结果
 */
function processCurrentScreenNotes(processedNoteIds, keywordsString, backToPreviousPage, isNoteDetailPage, targetRegionIndex) {
    // 设置默认值
    if (typeof targetRegionIndex === 'undefined') {
        targetRegionIndex = 0;
    }

    utils.log("NOTE_NAV: 开始循环处理当前屏幕内的所有笔记...");
    utils.log("NOTE_NAV: 目标区域: " + utils.getRegionNameByIndex(targetRegionIndex));

    return new Promise(function (resolve) {
        try {
            // 获取当前屏幕内的所有笔记
            const notesOnScreen = extractVisibleNotes();
            if (notesOnScreen.length === 0) {
                utils.log("NOTE_NAV: 当前屏幕内没有找到笔记");
                resolve({ success: false, reason: "当前屏幕内没有找到笔记" });
                return;
            }

            utils.log("NOTE_NAV: 当前屏幕找到 " + notesOnScreen.length + " 个笔记，开始逐个处理...");

            let processedCount = 0;
            let totalCollectedComments = 0;

            // 递归处理每个笔记
            function processNextNote(noteIndex) {
                if (noteIndex >= notesOnScreen.length) {
                    utils.log("NOTE_NAV: 当前屏幕所有笔记处理完成。处理了 " + processedCount + " 个笔记，采集了 " + totalCollectedComments + " 条评论");
                    resolve({
                        success: true,
                        processedCount: processedCount,
                        totalComments: totalCollectedComments,
                        reason: "当前屏幕所有笔记处理完成"
                    });
                    return;
                }

                const noteInfo = notesOnScreen[noteIndex];
                utils.log("NOTE_NAV: 处理第 " + (noteIndex + 1) + "/" + notesOnScreen.length + " 个笔记: \"" + noteInfo.signature.substring(0, 50) + "...\"");
                utils.log("NOTE_NAV: 笔记类型: " + noteTypes.getNoteTypeDescription(noteInfo.noteType));

                // 检查是否已处理过
                if (processedNoteIds && processedNoteIds.has(noteInfo.signature)) {
                    utils.log("NOTE_NAV: 笔记已处理过，跳过: \"" + noteInfo.signature.substring(0, 50) + "...\"");
                    processNextNote(noteIndex + 1);
                    return;
                }

                // 尝试点击进入笔记
                utils.log("NOTE_NAV: 尝试点击进入笔记: \"" + noteInfo.title + "\"");
                if (noteInfo.clickableElement && typeof noteInfo.clickableElement.click === 'function' && noteInfo.clickableElement.click()) {
                    utils.log("NOTE_NAV: 点击成功，等待页面加载...");
                    sleep(3500);

                    if (isNoteDetailPage()) {
                        utils.log("NOTE_NAV: 成功进入笔记详情页，开始采集评论...");

                        // 标记为已处理
                        if (processedNoteIds) {
                            processedNoteIds.add(noteInfo.signature);
                        }
                        processedCount++;

                        // 在详情页进行准确的笔记类型检测
                        utils.log("NOTE_NAV: 在详情页进行笔记类型检测...");
                        const actualNoteType = noteTypes.detectNoteTypeInDetailPage();
                        noteInfo.noteType = actualNoteType; // 更新 noteInfo 中的 noteType
                        utils.log("NOTE_NAV: 详情页检测结果: " + noteTypes.getNoteTypeDescription(actualNoteType));

                        if (actualNoteType !== noteInfo.noteType && noteInfo.noteType !== noteTypes.NOTE_TYPES.UNKNOWN && noteInfo.originalType !== undefined) { // originalType is a hypothetical field if you store pre-detection type
                            utils.log("NOTE_NAV: 笔记类型修正(从列表页到详情页): " + noteTypes.getNoteTypeDescription(noteInfo.originalType) + " → " + noteTypes.getNoteTypeDescription(actualNoteType));
                        }

                        // --- 新增：获取当前笔记的详细信息，包括提取的文本 ---
                        let detailedNoteInfoFromPage = getCurrentNoteInfo(); // 调用新函数
                        noteInfo.extractedText = detailedNoteInfoFromPage.extractedText; // 将提取的文本添加到 noteInfo
                        noteInfo.noteId = detailedNoteInfoFromPage.noteId;               // 更新 noteId
                        noteInfo.noteTitle = detailedNoteInfoFromPage.noteTitle || noteInfo.title; // 更新 noteTitle, 如果详情页没取到就用列表页的标题作为后备

                        utils.log("NOTE_NAV: 获取的详细笔记信息: noteId=" + noteInfo.noteId + ", title=" + noteInfo.noteTitle + ", extractedText length=" + (noteInfo.extractedText ? noteInfo.extractedText.length : 0));
                        // --- 结束新增 ---

                        // 根据实际笔记类型选择不同的评论采集方法
                        let commentCollectionPromise;

                        // 从配置中获取功能开关
                        const enableLiking = configManager && configManager.getCommentScrapingConfig &&
                            configManager.getCommentScrapingConfig().enableLiking || false;
                        const enableCommenting = configManager && configManager.getNoteCommentingConfig &&
                            configManager.getNoteCommentingConfig().enableCommenting || false;

                        // 如果启用了评论功能，先发布评论（支持AI评论）
                        let commentPublishPromise = Promise.resolve(true);
                        if (enableCommenting) {
                            utils.log("NOTE_NAV: 开始发布评论...");

                            // 直接调用评论发布模块（AI评论生成已集成在其中）
                            utils.log("NOTE_NAV: 调用评论发布模块...");

                            // 修正函数调用参数顺序：(noteTitle, noteAuthor, customComments)
                            commentPublishPromise = Promise.resolve(
                                noteCommenting.publishCommentInCurrentNote(
                                    noteInfo.noteTitle || noteInfo.title,
                                    noteInfo.author || "Unknown Author",
                                    [] // 空数组，让评论模块根据配置决定使用AI评论还是预设评论
                                )
                            );
                        }

                        // 等待评论发布完成后，再进行评论采集
                        commentCollectionPromise = commentPublishPromise.then(function (commentPublished) {
                            if (enableCommenting) {
                                if (commentPublished) {
                                    utils.log("NOTE_NAV: ✓ 评论发布成功，继续评论采集...");
                                } else {
                                    utils.log("NOTE_NAV: ✗ 评论发布失败，继续评论采集...");
                                }
                                sleep(1000); // 等待一秒再开始采集
                            }

                            // 执行评论采集
                            if (actualNoteType === noteTypes.NOTE_TYPES.VIDEO) {
                                utils.log("NOTE_NAV: 使用视频笔记评论采集方法...");
                                return videoComments.collectVideoNoteComments(noteInfo.title, keywordsString, targetRegionIndex, enableLiking);
                            } else {
                                utils.log("NOTE_NAV: 使用图文笔记评论采集方法...");
                                return simpleComments.collectAndSaveComments(noteInfo.title, keywordsString, targetRegionIndex, enableLiking);
                            }
                        });

                        commentCollectionPromise
                            .then(function (commentCount) {
                                if (commentCount > 0) {
                                    utils.log("NOTE_NAV: 从笔记 \"" + noteInfo.title + "\" 采集到 " + commentCount + " 条评论");
                                    totalCollectedComments += commentCount;
                                } else {
                                    utils.log("NOTE_NAV: 从笔记 \"" + noteInfo.title + "\" 未采集到评论");
                                }

                                // 返回搜索结果页，传递笔记类型
                                utils.log("NOTE_NAV: 返回搜索结果页...");
                                return backToPreviousPage(actualNoteType);
                            })
                            .then(function (backSuccess) {
                                if (backSuccess) {
                                    utils.log("NOTE_NAV: 成功返回搜索结果页");
                                    sleep(1000);
                                    processNextNote(noteIndex + 1);
                                } else {
                                    utils.log("NOTE_NAV: 返回搜索结果页失败，尝试再次返回...");
                                    sleep(1000);
                                    backToPreviousPage().then(function (secondBackSuccess) {
                                        if (secondBackSuccess) {
                                            utils.log("NOTE_NAV: 第二次返回成功");
                                            sleep(1000);
                                            processNextNote(noteIndex + 1);
                                        } else {
                                            utils.log("ERROR: NOTE_NAV: 无法返回搜索结果页，中断处理");
                                            resolve({
                                                success: false,
                                                reason: "无法返回搜索结果页",
                                                processedCount: processedCount,
                                                totalComments: totalCollectedComments
                                            });
                                        }
                                    });
                                }
                            })
                            .catch(function (commentError) {
                                utils.log("ERROR: NOTE_NAV: 评论采集出错: " + commentError);
                                // 即使评论采集失败，也要尝试返回并继续处理下一个笔记
                                backToPreviousPage().then(function () {
                                    sleep(1000);
                                    processNextNote(noteIndex + 1);
                                });
                            });
                    } else {
                        utils.log("NOTE_NAV: 点击后未能进入详情页，跳过此笔记");
                        processNextNote(noteIndex + 1);
                    }
                } else {
                    utils.log("NOTE_NAV: 点击笔记失败，跳过: \"" + noteInfo.signature.substring(0, 50) + "...\"");
                    processNextNote(noteIndex + 1);
                }
            }

            // 开始处理第一个笔记
            processNextNote(0);

        } catch (e) {
            utils.log("ERROR: NOTE_NAV: 处理当前屏幕笔记时发生异常: " + e.toString());
            resolve({ success: false, reason: "处理异常: " + e.toString() });
        }
    });
}

// 放在 module.exports 之前

/**
 * 获取当前打开的笔记详情页中的信息，包括提取的文本内容、笔记ID和标题。
 * @returns {object} 包含 extractedText, noteId, noteTitle 的对象。
 *                   extractedText 在提取失败或无内容时为 null。
 *                   noteId 和 noteTitle 也可能为 null 如果无法获取。
 */
function getCurrentNoteInfo() {
    utils.log("NOTE_NAV_DETAIL: 开始提取当前笔记详情信息...");
    let extractedText = null;
    let noteId = null; // 具体提取逻辑需要根据APP实际情况调整
    let noteTitle = null;

    // 尝试提取笔记标题 (通用部分)
    try {
        let titleView = id("com.xingin.xhs:id/titleView").findOne(1000); // 主要标题ID
        if (!titleView) { // AutoJsPro 的 findOne 返回的是 UiObject，需要检查其是否存在及文本
            titleView = id("com.xingin.xhs:id/tvNoteTitle").findOne(1000); // 备选标题ID
        }
        if (titleView && titleView.text()) {
            noteTitle = titleView.text().trim();
            utils.log("NOTE_NAV_DETAIL: 提取到笔记标题: " + noteTitle);
        } else {
            // 尝试其他可能的标题ID
            let anotherTitleView = id("com.xingin.xhs:id/noteTitleText").findOne(500); // 另一些可能的ID
            if (anotherTitleView && anotherTitleView.text()) {
                noteTitle = anotherTitleView.text().trim();
                utils.log("NOTE_NAV_DETAIL: 提取到笔记标题 (备选ID): " + noteTitle);
            } else {
                utils.log("NOTE_NAV_DETAIL: 未能提取到笔记标题。");
            }
        }
    } catch (e) {
        utils.log("NOTE_NAV_DETAIL: 提取笔记标题时出错: " + e.toString());
    }

    // 尝试图文笔记内容提取
    utils.log("NOTE_NAV_DETAIL: 尝试图文笔记内容提取 (ID: com.xingin.xhs:id/drg)...");
    const noteContentContainer = id("com.xingin.xhs:id/drg").findOne(1500);
    if (noteContentContainer) {
        utils.log("NOTE_NAV_DETAIL: 找到图文笔记容器 (com.xingin.xhs:id/drg)。");
        const textViews = noteContentContainer.find(className("android.widget.TextView"));
        if (textViews && !textViews.empty()) {
            let texts = [];
            textViews.forEach(tv => {
                if (tv && tv.text()) { // 确保 tv 对象存在且有 text 方法
                    texts.push(tv.text());
                }
            });
            if (texts.length > 0) {
                extractedText = texts.join("\n").trim();
                utils.log("NOTE_NAV_DETAIL: 图文内容提取成功，长度: " + extractedText.length);
            } else {
                utils.log("NOTE_NAV_DETAIL: 图文容器内未找到有效TextView文本。");
            }
        } else {
            utils.log("NOTE_NAV_DETAIL: 图文容器 (com.xingin.xhs:id/drg) 内未找到TextView。");
        }
    } else {
        utils.log("NOTE_NAV_DETAIL: 未找到图文笔记容器 (com.xingin.xhs:id/drg)。");
    }

    // 如果图文提取未成功，尝试视频笔记简介提取
    if (!extractedText || extractedText.trim() === "") {
        utils.log("NOTE_NAV_DETAIL: 图文提取失败或无内容，尝试视频笔记简介提取...");
        const expandButton = id("com.xingin.xhs:id/noteContentText").findOne(1000);
        let expandButtonClicked = false;

        if (expandButton && expandButton.clickable()) {
            utils.log("NOTE_NAV_DETAIL: 找到视频简介展开按钮 (com.xingin.xhs:id/noteContentText)，尝试点击...");
            if (expandButton.click()) {
                utils.log("NOTE_NAV_DETAIL: 视频简介展开按钮点击成功。");
                expandButtonClicked = true;
                sleep(1500); // 等待内容加载

                const videoDescriptionElement = id("com.xingin.xhs:id/ba8").findOne(1000);
                if (videoDescriptionElement && videoDescriptionElement.text()) {
                    extractedText = videoDescriptionElement.text().trim();
                    utils.log("NOTE_NAV_DETAIL: 视频简介提取成功，长度: " + extractedText.length);
                } else {
                    utils.log("NOTE_NAV_DETAIL: 未找到视频简介元素 (com.xingin.xhs:id/ba8) 或无文本。");
                }
            } else {
                utils.log("NOTE_NAV_DETAIL: 视频简介展开按钮点击失败。");
            }
        } else {
            utils.log("NOTE_NAV_DETAIL: 未找到视频简介展开按钮 (com.xingin.xhs:id/noteContentText) 或按钮不可点击。尝试直接读取。");
            // 如果展开按钮不存在或不可点击，尝试直接读取简介区（可能已展开或不同结构）
            const videoDescriptionElementDirect = id("com.xingin.xhs:id/ba8").findOne(1000);
            if (videoDescriptionElementDirect && videoDescriptionElementDirect.text()) {
                let directText = videoDescriptionElementDirect.text().trim();
                if (directText) { // 确保提取到非空文本
                    extractedText = directText;
                    utils.log("NOTE_NAV_DETAIL: 直接从 com.xingin.xhs:id/ba8 提取到简介文本。");
                } else {
                    utils.log("NOTE_NAV_DETAIL: 直接从 com.xingin.xhs:id/ba8 提取到空文本。");
                }
            } else {
                utils.log("NOTE_NAV_DETAIL: 直接读取 com.xingin.xhs:id/ba8 未成功。");
            }
        }

        if (expandButtonClicked) {
            utils.log("NOTE_NAV_DETAIL: 视频简介提取流程(点击展开后)执行 back() 操作。");
            back();
            sleep(1000);
            utils.log("NOTE_NAV_DETAIL: back() 操作后延时结束。");
        }
    }

    if (extractedText && extractedText.trim() === "") {
        extractedText = null;
        utils.log("NOTE_NAV_DETAIL: 提取到的文本为空或仅含空格，重置为null。");
    }

    // 提取 noteId - 这是一个难点，需要根据实际app情况适配，以下为占位逻辑
    // 通常 noteId 不会直接暴露在UI上，可能需要更复杂的技巧（如分析网络请求、页面元数据等）
    // 此处仅为示意，实际项目中可能需要更专业的逆向工程或API分析
    // let tempNoteIdContainer = id("some_hidden_note_id_container").findOne(200);
    // if (tempNoteIdContainer && tempNoteIdContainer.desc()) { // 假设ID在content-desc
    //    noteId = tempNoteIdContainer.desc();
    // }
    // if (!noteId) {
    //    utils.log("NOTE_NAV_DETAIL: 未能提取到笔记ID。该功能在当前实现中可能受限。");
    // }


    return {
        extractedText: extractedText,
        noteId: noteId,
        noteTitle: noteTitle
    };
}

/**
 * 提取当前笔记的文本内容（供AI评论生成使用）
 * @returns {string|null} 提取到的文本内容，失败时返回null
 */
function extractNoteContent() {
    utils.log("NOTE_NAV: 开始提取当前笔记内容...");

    try {
        const noteInfo = getCurrentNoteInfo();
        if (noteInfo && noteInfo.extractedText) {
            utils.log("NOTE_NAV: 成功提取笔记内容，长度: " + noteInfo.extractedText.length);
            return noteInfo.extractedText;
        } else {
            utils.log("NOTE_NAV: 未能提取到笔记内容");
            return null;
        }
    } catch (e) {
        utils.log("NOTE_NAV: 提取笔记内容时发生异常: " + e.toString());
        return null;
    }
}

module.exports = {
    extractVisibleNotes,
    processCurrentScreenNotes,
    getCurrentNoteInfo, // 新增导出
    extractNoteContent, // 新增导出
    SELECTORS
};

utils.log("NOTE_NAV: 小红书笔记导航模块 (xhs_note_navigation.js) 加载完毕。");
