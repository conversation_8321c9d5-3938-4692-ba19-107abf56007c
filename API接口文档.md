# 小红书自动化项目 API 接口文档

## 核心业务模块 API

### main.js - 主程序模块

#### startCommentingNotes()
```javascript
function startCommentingNotes()
```
- **功能**: 启动评论任务的主函数
- **参数**: 无
- **返回值**: 无
- **说明**: 根据UI配置启动相应的评论任务流程

### xhs_simple_comments.js - 图文笔记处理模块

#### processImageTextNote(noteTitle, noteAuthor)
```javascript
function processImageTextNote(noteTitle, noteAuthor)
```
- **功能**: 图文笔记业务统一处理入口
- **参数**: 
  - `noteTitle` (string): 笔记标题
  - `noteAuthor` (string): 笔记作者
- **返回值**: boolean - 处理是否成功
- **说明**: 执行评论发布、用户信息采集、点赞等操作，最后返回搜索结果页

#### collectAndSaveComments(noteTitle, keywordsString, targetRegionIndex, enableLiking)
```javascript
function collectAndSaveComments(noteTitle, keywordsString, targetRegionIndex, enableLiking)
```
- **功能**: 采集并保存图文笔记的评论
- **参数**:
  - `noteTitle` (string): 笔记标题
  - `keywordsString` (string): 关键词字符串
  - `targetRegionIndex` (number): 目标区域索引
  - `enableLiking` (boolean): 是否启用点赞
- **返回值**: number - 采集到的评论数量

#### likeComment(commentElement)
```javascript
function likeComment(commentElement)
```
- **功能**: 点赞指定评论
- **参数**: 
  - `commentElement` (UiObject): 评论元素
- **返回值**: boolean - 点赞是否成功

### xhs_video_comments.js - 视频笔记处理模块

#### processVideoNote(noteTitle, noteAuthor)
```javascript
function processVideoNote(noteTitle, noteAuthor)
```
- **功能**: 视频笔记业务统一处理入口
- **参数**: 
  - `noteTitle` (string): 笔记标题
  - `noteAuthor` (string): 笔记作者
- **返回值**: boolean - 处理是否成功
- **说明**: 执行评论发布、用户信息采集、点赞等操作，最后返回搜索结果页

#### collectVideoNoteComments(noteTitle, keywordsString, targetRegionIndex, enableLiking)
```javascript
function collectVideoNoteComments(noteTitle, keywordsString, targetRegionIndex, enableLiking)
```
- **功能**: 采集视频笔记的评论
- **参数**:
  - `noteTitle` (string): 笔记标题
  - `keywordsString` (string): 关键词字符串
  - `targetRegionIndex` (number): 目标区域索引
  - `enableLiking` (boolean): 是否启用点赞
- **返回值**: number - 采集到的评论数量

#### isVideoNoteDetailPage()
```javascript
function isVideoNoteDetailPage()
```
- **功能**: 检测当前是否在视频笔记详情页
- **参数**: 无
- **返回值**: boolean - 是否在视频笔记详情页

### xhs_note_commenting.js - 评论处理模块

#### publishCommentInCurrentNote(noteTitle, noteAuthor, customComments)
```javascript
function publishCommentInCurrentNote(noteTitle, noteAuthor, customComments)
```
- **功能**: 在当前笔记中发布评论
- **参数**:
  - `noteTitle` (string): 笔记标题
  - `noteAuthor` (string): 笔记作者
  - `customComments` (Array): 自定义评论数组
- **返回值**: boolean - 评论发布是否成功
- **说明**: 根据配置决定使用AI评论还是预设评论

#### generateAIComment(noteTitle, noteAuthor, shareLink)
```javascript
function generateAIComment(noteTitle, noteAuthor, shareLink)
```
- **功能**: 生成AI评论
- **参数**:
  - `noteTitle` (string): 笔记标题
  - `noteAuthor` (string): 笔记作者
  - `shareLink` (string): 分享链接
- **返回值**: Promise<string> - 生成的AI评论内容

#### publishPresetComment(customComments)
```javascript
function publishPresetComment(customComments)
```
- **功能**: 发布预设评论
- **参数**:
  - `customComments` (Array): 预设评论数组
- **返回值**: boolean - 发布是否成功

## 工具和支持模块 API

### xhs_actions.js - 通用操作工具库

#### searchNotes(keyword, sortByOption, publishTimeOption, locationDistanceOption)
```javascript
function searchNotes(keyword, sortByOption, publishTimeOption, locationDistanceOption)
```
- **功能**: 搜索小红书笔记
- **参数**:
  - `keyword` (string): 搜索关键词
  - `sortByOption` (number): 排序选项
  - `publishTimeOption` (number): 发布时间选项
  - `locationDistanceOption` (number): 位置距离选项
- **返回值**: Object - 搜索结果对象

#### isSearchResultsPage()
```javascript
function isSearchResultsPage()
```
- **功能**: 检测当前是否在搜索结果页
- **参数**: 无
- **返回值**: boolean - 是否在搜索结果页

#### backToPreviousPage(noteType)
```javascript
function backToPreviousPage(noteType)
```
- **功能**: 返回上一页
- **参数**:
  - `noteType` (string): 笔记类型
- **返回值**: boolean - 返回是否成功

#### extractVisibleNotes_AdaptedOfficial()
```javascript
function extractVisibleNotes_AdaptedOfficial()
```
- **功能**: 提取当前屏幕可见的笔记列表
- **参数**: 无
- **返回值**: Array - 笔记信息数组

### xhs_share_link.js - 分享链接工具库

#### copyCurrentNoteShareLink()
```javascript
function copyCurrentNoteShareLink()
```
- **功能**: 复制当前笔记的分享链接
- **参数**: 无
- **返回值**: Promise<string|null> - 分享链接或null

#### copyImageTextNoteShareLink()
```javascript
function copyImageTextNoteShareLink()
```
- **功能**: 复制图文笔记的分享链接
- **参数**: 无
- **返回值**: string|null - 分享链接或null

#### copyVideoNoteShareLink()
```javascript
function copyVideoNoteShareLink()
```
- **功能**: 复制视频笔记的分享链接
- **参数**: 无
- **返回值**: string|null - 分享链接或null

### xhs_note_types.js - 笔记类型检测工具库

#### detectNoteTypeInDetailPage()
```javascript
function detectNoteTypeInDetailPage()
```
- **功能**: 在详情页检测笔记类型
- **参数**: 无
- **返回值**: string - 笔记类型常量

#### detectNoteType(container)
```javascript
function detectNoteType(container)
```
- **功能**: 在搜索结果页检测笔记类型
- **参数**:
  - `container` (UiObject): 笔记容器元素
- **返回值**: string - 笔记类型常量

#### getNoteTypeDescription(noteType)
```javascript
function getNoteTypeDescription(noteType)
```
- **功能**: 获取笔记类型的中文描述
- **参数**:
  - `noteType` (string): 笔记类型常量
- **返回值**: string - 中文描述

#### 笔记类型常量
```javascript
const NOTE_TYPES = {
    IMAGE_TEXT: "image_text",    // 图文笔记
    VIDEO: "video",              // 视频笔记
    LIVE: "live",                // 直播笔记
    UNKNOWN: "unknown"           // 未知类型
};
```

### xhs_comment_actions.js - 评论操作工具库

#### scrapeCommentsFromNote(noteId, noteTitle, keywordsString)
```javascript
function scrapeCommentsFromNote(noteId, noteTitle, keywordsString)
```
- **功能**: 从笔记中采集评论
- **参数**:
  - `noteId` (string): 笔记ID
  - `noteTitle` (string): 笔记标题
  - `keywordsString` (string): 关键词字符串
- **返回值**: Promise<Array> - 采集到的评论数据数组

#### openCommentsSection()
```javascript
function openCommentsSection()
```
- **功能**: 打开评论区
- **参数**: 无
- **返回值**: boolean - 是否成功打开

#### extractVisibleComments()
```javascript
function extractVisibleComments()
```
- **功能**: 提取当前可见的评论
- **参数**: 无
- **返回值**: Array - 评论数据数组

## AI和服务模块 API

### llm_service.js - AI服务模块

#### generateComment(prompt, noteTitle, noteAuthor)
```javascript
function generateComment(prompt, noteTitle, noteAuthor)
```
- **功能**: 生成AI评论
- **参数**:
  - `prompt` (string): 提示词
  - `noteTitle` (string): 笔记标题
  - `noteAuthor` (string): 笔记作者
- **返回值**: Promise<string> - 生成的评论内容

### doubao_webview.js - 豆包WebView模块

#### createDoubaoWebView()
```javascript
function createDoubaoWebView()
```
- **功能**: 创建豆包WebView
- **参数**: 无
- **返回值**: WebView对象

#### sendMessageToDoubao(webview, message)
```javascript
function sendMessageToDoubao(webview, message)
```
- **功能**: 向豆包发送消息
- **参数**:
  - `webview` (WebView): WebView对象
  - `message` (string): 消息内容
- **返回值**: Promise<string> - AI回复内容

### config.js - 配置管理模块

#### loadConfig()
```javascript
function loadConfig()
```
- **功能**: 加载配置文件
- **参数**: 无
- **返回值**: Object - 配置对象

#### saveConfig(config)
```javascript
function saveConfig(config)
```
- **功能**: 保存配置文件
- **参数**:
  - `config` (Object): 配置对象
- **返回值**: boolean - 保存是否成功

#### getDefaultConfig()
```javascript
function getDefaultConfig()
```
- **功能**: 获取默认配置
- **参数**: 无
- **返回值**: Object - 默认配置对象

### utils.js - 通用工具模块

#### log(message)
```javascript
function log(message)
```
- **功能**: 记录日志
- **参数**:
  - `message` (string): 日志消息
- **返回值**: 无

#### matchKeywords(text, keywordsString)
```javascript
function matchKeywords(text, keywordsString)
```
- **功能**: 检查文本是否匹配关键词
- **参数**:
  - `text` (string): 待检查的文本
  - `keywordsString` (string): 关键词字符串
- **返回值**: boolean - 是否匹配

#### ensureAppOpen(appName)
```javascript
function ensureAppOpen(appName)
```
- **功能**: 确保指定应用已打开
- **参数**:
  - `appName` (string): 应用名称
- **返回值**: boolean - 是否成功打开

## 数据结构定义

### 笔记信息对象
```javascript
{
    title: string,           // 笔记标题
    author: string,          // 笔记作者
    clickableElement: UiObject, // 可点击的元素
    bounds: Object,          // 元素边界
    noteType: string         // 笔记类型
}
```

### 评论数据对象
```javascript
{
    uid: string,             // 用户ID
    nickname: string,        // 用户昵称
    commentText: string,     // 评论内容
    noteId: string,          // 笔记ID
    noteTitle: string,       // 笔记标题
    timestamp: number,       // 时间戳
    matchesKeyword: boolean  // 是否匹配关键词
}
```

### 配置对象结构
```javascript
{
    enableCommenting: boolean,      // 是否启用评论功能
    enableLlmComments: boolean,     // 是否启用AI评论
    enableUserCollection: boolean,  // 是否启用用户信息采集
    enableLiking: boolean,          // 是否启用点赞功能
    useDoubaoProxy: boolean,        // 是否使用豆包AI
    llmUseShareLink: boolean,       // AI评论是否使用分享链接
    keywords: string,               // 关键词
    targetRegion: number,           // 目标区域
    customComments: Array           // 自定义评论列表
}
```

---

**文档版本**: 1.0  
**最后更新**: 2024年1月  
**维护者**: 项目开发团队
