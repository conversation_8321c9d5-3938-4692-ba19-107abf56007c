/**
 * 豆包WebView自动登录模块
 * 支持手机号+短信验证码登录
 */

var utils = require(files.path("./utils.js"));
var SmsReader = require(files.path("./sms_reader.js")).SmsReader;

/**
 * 豆包登录管理器
 */
function DoubaoLoginManager() {
    this.webview = null;
    this.webviewControl = null;
    this.smsReader = null;
}

/**
 * 初始化WebView
 */
DoubaoLoginManager.prototype.init = function (webviewControl) {
    this.webviewControl = webviewControl;

    // 初始化短信读取器
    this.smsReader = new SmsReader();
    if (!this.smsReader.requestSmsPermission()) {
        utils.log("DOUBAO_LOGIN: 短信权限提醒完成，将使用手动输入模式", "warn");
    }

    utils.log("DOUBAO_LOGIN: 登录管理器初始化完成");
    return true;
};

/**
 * 检测当前登录状态
 */
DoubaoLoginManager.prototype.checkLoginStatus = function () {
    utils.log("DOUBAO_LOGIN: 检测登录状态...");

    var isLoggedIn = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                // 检测登录状态的多种方法
                var indicators = {
                    hasLoginButton: false,
                    hasUserAvatar: false,
                    hasWelcomeText: false,
                    pageContent: ''
                };
                
                // 方法1: 查找登录按钮（使用精确选择器）
                var specificLoginButton = document.querySelector('button[data-testid="to_login_button"]');
                if (specificLoginButton) {
                    indicators.hasLoginButton = true;
                } else {
                    // 备用方法：查找包含登录文字的按钮
                    var loginButtons = document.querySelectorAll('button, a, div');
                    for (var i = 0; i < loginButtons.length; i++) {
                        var text = loginButtons[i].textContent || loginButtons[i].innerText || '';
                        if (text.includes('登录') || text.includes('注册') || text.includes('手机号')) {
                            indicators.hasLoginButton = true;
                            break;
                        }
                    }
                }
                
                // 方法2: 查找用户头像或用户信息
                var userElements = document.querySelectorAll('[class*="avatar"], [class*="user"], [class*="profile"]');
                indicators.hasUserAvatar = userElements.length > 0;
                
                // 方法3: 检查欢迎文字
                indicators.hasWelcomeText = document.body.textContent.includes('你好，我是豆包');
                
                // 方法4: 获取页面主要内容用于判断
                indicators.pageContent = document.body.textContent.substring(0, 200);
                
                return indicators;
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                try {
                    var result = value ? JSON.parse(value.replace(/^"|"$/g, '')) : null;
                    if (result) {
                        utils.log("DOUBAO_LOGIN: 登录状态检测结果:");
                        utils.log("- 有登录按钮: " + result.hasLoginButton);
                        utils.log("- 有用户头像: " + result.hasUserAvatar);
                        utils.log("- 有欢迎文字: " + result.hasWelcomeText);
                        utils.log("- 页面内容: " + result.pageContent.substring(0, 100) + "...");

                        // 判断登录状态：
                        // 1. 如果有欢迎文字且没有登录按钮，说明已登录
                        // 2. 如果页面内容包含聊天相关内容且没有登录按钮，也说明已登录
                        var hasChat = result.pageContent.includes('发消息') ||
                            result.pageContent.includes('输入') ||
                            result.pageContent.includes('聊天');

                        isLoggedIn = (result.hasWelcomeText || hasChat) && !result.hasLoginButton;
                    }
                } catch (e) {
                    utils.log("DOUBAO_LOGIN: 解析登录状态失败: " + e);
                }
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    utils.log("DOUBAO_LOGIN: 当前登录状态: " + (isLoggedIn ? "已登录" : "未登录"));
    return isLoggedIn;
};

/**
 * 查找并点击登录按钮
 */
DoubaoLoginManager.prototype.clickLoginButton = function () {
    utils.log("DOUBAO_LOGIN: 查找登录按钮...");

    var loginClicked = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                // 使用正确的登录按钮选择器
                var loginButton = document.querySelector('button[data-testid="to_login_button"]');

                if (loginButton) {
                    // 高亮登录按钮
                    loginButton.style.border = '3px solid red';
                    loginButton.style.boxShadow = '0 0 10px red';

                    // 点击登录按钮
                    loginButton.click();

                    // 也尝试触发其他事件
                    var clickEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    loginButton.dispatchEvent(clickEvent);

                    return '找到并点击登录按钮: ' + (loginButton.textContent || loginButton.innerText);
                } else {
                    // 备用方法：查找包含"登录"文字的按钮
                    var buttons = document.querySelectorAll('button');
                    for (var i = 0; i < buttons.length; i++) {
                        var btn = buttons[i];
                        var text = btn.textContent || btn.innerText || '';
                        if (text.includes('登录')) {
                            btn.style.border = '3px solid red';
                            btn.style.boxShadow = '0 0 10px red';
                            btn.click();
                            return '备用方法找到并点击登录按钮: ' + text;
                        }
                    }
                    return '未找到登录按钮';
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);
                loginClicked = result.includes('找到并点击');
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    if (loginClicked) {
        utils.log("DOUBAO_LOGIN: 登录按钮点击成功，等待登录页面加载...");
        sleep(3000); // 等待登录页面加载
        return true;
    } else {
        utils.log("DOUBAO_LOGIN: 未找到或点击登录按钮失败");
        return false;
    }
};

/**
 * 输入手机号
 */
DoubaoLoginManager.prototype.inputPhoneNumber = function (phoneNumber) {
    utils.log("DOUBAO_LOGIN: 输入手机号: " + phoneNumber);

    var inputSuccess = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                var phone = '${phoneNumber}';

                // 使用正确的手机号输入框选择器
                var phoneInput = document.querySelector('input[data-testid="login_phone_number_input"]');

                if (phoneInput) {
                    // 高亮输入框
                    phoneInput.style.border = '3px solid blue';
                    phoneInput.style.backgroundColor = 'lightyellow';

                    // 清空并输入手机号
                    phoneInput.focus();
                    phoneInput.value = '';

                    // 使用原生setter
                    var nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                    nativeInputValueSetter.call(phoneInput, phone);

                    // 触发事件
                    phoneInput.dispatchEvent(new Event('input', { bubbles: true }));
                    phoneInput.dispatchEvent(new Event('change', { bubbles: true }));
                    phoneInput.dispatchEvent(new Event('blur', { bubbles: true }));

                    return '手机号输入成功: ' + phoneInput.value;
                } else {
                    // 备用方法：查找包含"手机号"的输入框
                    var inputs = document.querySelectorAll('input');
                    for (var i = 0; i < inputs.length; i++) {
                        var input = inputs[i];
                        var placeholder = input.placeholder || '';
                        if (placeholder.includes('手机号') || placeholder.includes('请输入手机号')) {
                            input.style.border = '3px solid blue';
                            input.style.backgroundColor = 'lightyellow';
                            input.focus();
                            input.value = phone;
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            return '备用方法手机号输入成功: ' + input.value;
                        }
                    }
                    return '未找到手机号输入框';
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);
                inputSuccess = result.includes('输入成功');
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    return inputSuccess;
};

/**
 * 勾选同意协议
 */
DoubaoLoginManager.prototype.checkAgreement = function () {
    utils.log("DOUBAO_LOGIN: 勾选同意协议...");

    var checkSuccess = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                // 查找同意协议的复选框
                var checkbox = document.querySelector('input[type="checkbox"]');

                if (checkbox) {
                    // 高亮复选框
                    var checkboxContainer = checkbox.parentElement;
                    if (checkboxContainer) {
                        checkboxContainer.style.border = '3px solid green';
                        checkboxContainer.style.backgroundColor = 'lightgreen';
                    }

                    // 如果未勾选，则勾选
                    if (!checkbox.checked) {
                        checkbox.click();

                        // 也尝试触发其他事件
                        var changeEvent = new Event('change', { bubbles: true });
                        checkbox.dispatchEvent(changeEvent);
                    }

                    return '同意协议勾选成功，状态: ' + checkbox.checked;
                } else {
                    return '未找到同意协议复选框';
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);
                checkSuccess = result.includes('勾选成功');
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    return checkSuccess;
};

/**
 * 点击下一步按钮
 */
DoubaoLoginManager.prototype.clickNextButton = function () {
    utils.log("DOUBAO_LOGIN: 点击下一步按钮...");

    var nextButtonClicked = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                // 使用正确的下一步按钮选择器
                var nextButton = document.querySelector('button[data-testid="login_next_button"]');

                if (nextButton) {
                    // 高亮按钮
                    nextButton.style.border = '3px solid green';
                    nextButton.style.backgroundColor = 'lightgreen';

                    // 点击按钮
                    nextButton.click();

                    var clickEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    nextButton.dispatchEvent(clickEvent);

                    return '下一步按钮点击成功';
                } else {
                    // 备用方法：查找包含"下一步"的按钮
                    var buttons = document.querySelectorAll('button');
                    for (var i = 0; i < buttons.length; i++) {
                        var btn = buttons[i];
                        var text = btn.textContent || btn.innerText || '';
                        if (text.includes('下一步')) {
                            btn.style.border = '3px solid green';
                            btn.style.backgroundColor = 'lightgreen';
                            btn.click();
                            return '备用方法下一步按钮点击成功: ' + text;
                        }
                    }
                    return '未找到下一步按钮';
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);
                nextButtonClicked = result.includes('点击成功');
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    if (nextButtonClicked) {
        utils.log("DOUBAO_LOGIN: 下一步按钮点击成功，等待验证码页面加载...");
        sleep(3000); // 等待验证码页面加载
        return true;
    } else {
        utils.log("DOUBAO_LOGIN: 下一步按钮点击失败");
        return false;
    }
};

/**
 * 获取短信验证码（简化版 - 手动输入）
 */
DoubaoLoginManager.prototype.waitForVerificationCode = function () {
    utils.log("DOUBAO_LOGIN: 开始获取短信验证码...");

    if (!this.smsReader) {
        utils.log("DOUBAO_LOGIN: 短信读取器未初始化，使用备用手动输入", "warn");
        return this.waitForVerificationCodeManual();
    }

    // 使用简化版短信读取器（实际上是手动输入）
    utils.log("DOUBAO_LOGIN: 使用简化版验证码输入...");
    var code = this.smsReader.waitForVerificationCode(120); // 等待2分钟

    if (code) {
        utils.log("DOUBAO_LOGIN: 获取到验证码: " + code);
        return code;
    } else {
        utils.log("DOUBAO_LOGIN: 验证码获取失败或超时", "warn");
        return null;
    }
};

/**
 * 手动输入验证码（备用方案）
 */
DoubaoLoginManager.prototype.waitForVerificationCodeManual = function () {
    utils.log("DOUBAO_LOGIN: 等待用户手动输入验证码...");

    // 显示对话框让用户输入验证码
    var code = null;

    ui.run(function () {
        dialogs.rawInput("豆包登录", "自动获取验证码失败，请手动输入收到的短信验证码:", "", function (input) {
            if (input && input.trim()) {
                code = input.trim();
                utils.log("DOUBAO_LOGIN: 用户输入验证码: " + code);
            } else {
                utils.log("DOUBAO_LOGIN: 用户取消输入验证码");
            }
        });
    });

    // 等待用户输入
    var waitCount = 0;
    while (code === null && waitCount < 60) { // 最多等待60秒
        sleep(1000);
        waitCount++;
    }

    if (code) {
        utils.log("DOUBAO_LOGIN: 获取到验证码: " + code);
        return code;
    } else {
        utils.log("DOUBAO_LOGIN: 验证码输入超时");
        return null;
    }
};

/**
 * 输入验证码
 */
DoubaoLoginManager.prototype.inputVerificationCode = function (code) {
    utils.log("DOUBAO_LOGIN: 输入验证码: " + code);

    var inputSuccess = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                var verifyCode = '${code}';

                // 使用正确的验证码输入框选择器
                var codeContainer = document.querySelector('div[data-testid="code_input"]');
                var codeInput = null;

                if (codeContainer) {
                    // 查找容器内的实际输入框
                    codeInput = codeContainer.querySelector('input[type="text"]');
                }

                if (codeInput) {
                    // 高亮输入框
                    codeInput.style.border = '3px solid orange';
                    codeInput.style.backgroundColor = 'lightorange';

                    // 清空并输入验证码
                    codeInput.focus();
                    codeInput.value = '';

                    // 使用原生setter
                    var nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                    nativeInputValueSetter.call(codeInput, verifyCode);

                    // 触发事件
                    codeInput.dispatchEvent(new Event('input', { bubbles: true }));
                    codeInput.dispatchEvent(new Event('change', { bubbles: true }));
                    codeInput.dispatchEvent(new Event('blur', { bubbles: true }));

                    return '验证码输入成功: ' + codeInput.value;
                } else {
                    // 备用方法：查找包含验证码相关属性的输入框
                    var inputs = document.querySelectorAll('input');
                    for (var i = 0; i < inputs.length; i++) {
                        var input = inputs[i];
                        var placeholder = input.placeholder || '';
                        var inputMode = input.inputMode || '';

                        if (inputMode === 'decimal' || placeholder.includes('验证码')) {
                            input.style.border = '3px solid orange';
                            input.style.backgroundColor = 'lightorange';
                            input.focus();
                            input.value = verifyCode;
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            return '备用方法验证码输入成功: ' + input.value;
                        }
                    }
                    return '未找到验证码输入框';
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);
                inputSuccess = result.includes('输入成功');
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    return inputSuccess;
};

/**
 * 点击登录/确认按钮
 */
DoubaoLoginManager.prototype.clickConfirmButton = function () {
    utils.log("DOUBAO_LOGIN: 点击登录确认按钮...");

    var confirmClicked = false;
    var self = this;

    ui.run(function () {
        self.webviewControl.evaluateJavascript(`
            (function() {
                // 查找登录/确认按钮
                var buttons = document.querySelectorAll('button, a, div, span');
                var confirmButton = null;

                for (var i = 0; i < buttons.length; i++) {
                    var btn = buttons[i];
                    var text = btn.textContent || btn.innerText || '';
                    var className = btn.className || '';

                    if (text.includes('登录') || text.includes('确认') ||
                        text.includes('提交') || text.includes('完成') ||
                        text.includes('下一步') || text === '确定') {
                        confirmButton = btn;
                        break;
                    }

                    // 查找主要的提交按钮（通常是蓝色或突出的）
                    if (className.includes('primary') || className.includes('submit') ||
                        className.includes('confirm') || className.includes('login')) {
                        confirmButton = btn;
                        break;
                    }
                }

                if (confirmButton) {
                    // 高亮按钮
                    confirmButton.style.border = '3px solid purple';
                    confirmButton.style.backgroundColor = 'lightpurple';

                    // 点击按钮
                    confirmButton.click();

                    var clickEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    confirmButton.dispatchEvent(clickEvent);

                    return '登录确认按钮点击成功';
                } else {
                    return '未找到登录确认按钮';
                }
            })();
        `, new JavaAdapter(android.webkit.ValueCallback, {
            onReceiveValue: function (value) {
                var result = value ? value.replace(/^"|"$/g, '') : '';
                utils.log("DOUBAO_LOGIN: " + result);
                confirmClicked = result.includes('点击成功');
            }
        }));
    });

    // 等待回调执行
    sleep(1000);

    if (confirmClicked) {
        utils.log("DOUBAO_LOGIN: 登录确认按钮点击成功，等待登录完成...");
        sleep(3000); // 等待登录处理
        return true;
    } else {
        utils.log("DOUBAO_LOGIN: 登录确认按钮点击失败");
        return false;
    }
};

/**
 * 等待登录完成
 */
DoubaoLoginManager.prototype.waitForLoginComplete = function () {
    utils.log("DOUBAO_LOGIN: 等待登录完成...");

    var maxWait = 30; // 最多等待30秒
    var waitCount = 0;
    var loginComplete = false;
    var self = this;

    while (waitCount < maxWait && !loginComplete) {
        ui.run(function () {
            self.webviewControl.evaluateJavascript(`
                (function() {
                    // 检查登录完成的标志
                    var hasWelcome = document.body.textContent.includes('你好，我是豆包');
                    var hasLoginButton = false;

                    // 检查是否还有登录按钮
                    var buttons = document.querySelectorAll('button, a, div, span');
                    for (var i = 0; i < buttons.length; i++) {
                        var text = buttons[i].textContent || buttons[i].innerText || '';
                        if (text.includes('登录') || text.includes('注册')) {
                            hasLoginButton = true;
                            break;
                        }
                    }

                    // 检查URL变化
                    var currentUrl = window.location.href;

                    return {
                        hasWelcome: hasWelcome,
                        hasLoginButton: hasLoginButton,
                        url: currentUrl,
                        loginComplete: hasWelcome && !hasLoginButton
                    };
                })();
            `, new JavaAdapter(android.webkit.ValueCallback, {
                onReceiveValue: function (value) {
                    try {
                        var result = value ? JSON.parse(value.replace(/^"|"$/g, '')) : null;
                        if (result) {
                            utils.log("DOUBAO_LOGIN: 登录状态检查 - 欢迎页面: " + result.hasWelcome +
                                ", 登录按钮: " + result.hasLoginButton);

                            if (result.loginComplete) {
                                loginComplete = true;
                                utils.log("DOUBAO_LOGIN: 登录完成！");
                            }
                        }
                    } catch (e) {
                        utils.log("DOUBAO_LOGIN: 解析登录状态失败: " + e);
                    }
                }
            }));
        });

        sleep(1000);
        waitCount++;
    }

    if (loginComplete) {
        utils.log("DOUBAO_LOGIN: 登录成功完成");
        return true;
    } else {
        utils.log("DOUBAO_LOGIN: 登录完成检测超时");
        return false;
    }
};

/**
 * 完整的自动登录流程
 */
DoubaoLoginManager.prototype.autoLogin = function (phoneNumber) {
    utils.log("DOUBAO_LOGIN: 开始自动登录流程，手机号: " + phoneNumber);

    try {
        // 1. 检查当前登录状态
        if (this.checkLoginStatus()) {
            utils.log("DOUBAO_LOGIN: 已经登录，无需重复登录");
            return { success: true, message: "已经登录" };
        }

        // 2. 点击登录按钮
        if (!this.clickLoginButton()) {
            return { success: false, message: "点击登录按钮失败" };
        }

        // 3. 输入手机号
        if (!this.inputPhoneNumber(phoneNumber)) {
            return { success: false, message: "输入手机号失败" };
        }

        // 4. 勾选同意协议
        if (!this.checkAgreement()) {
            utils.log("DOUBAO_LOGIN: 勾选协议失败，但继续流程...");
        }

        // 5. 点击下一步按钮
        if (!this.clickNextButton()) {
            return { success: false, message: "点击下一步按钮失败" };
        }

        // 6. 等待用户输入验证码
        var code = this.waitForVerificationCode();
        if (!code) {
            return { success: false, message: "验证码输入超时或取消" };
        }

        // 7. 输入验证码
        if (!this.inputVerificationCode(code)) {
            return { success: false, message: "输入验证码失败" };
        }

        // 8. 等待登录完成（验证码输入后通常会自动登录）
        if (!this.waitForLoginComplete()) {
            return { success: false, message: "登录完成检测超时" };
        }

        utils.log("DOUBAO_LOGIN: 自动登录流程完成");
        return { success: true, message: "登录成功" };

    } catch (error) {
        utils.log("DOUBAO_LOGIN: 自动登录失败: " + error, "error");
        return { success: false, message: "登录异常: " + error.toString() };
    }
};

module.exports = {
    DoubaoLoginManager: DoubaoLoginManager
};

utils.log("豆包登录模块加载完毕 (doubao_login.js)");
