/**
 * 小红书评论发布模块 - 简化版（避免输入法干扰）
 * 专门解决输入法弹出导致的乱点问题
 */

const utils = require('./utils.js');
const noteTypes = require('./xhs_note_types.js');
const configManager = require('./config.js');

/**
 * 简单可靠的评论输入方法（避免输入法）
 * @param {Object} inputElement - 输入框元素
 * @param {string} commentText - 要输入的评论内容
 * @returns {boolean} - 输入是否成功
 */
function inputCommentSafely(inputElement, commentText) {
    try {
        utils.log("SAFE_INPUT: 开始安全输入评论: \"" + commentText + "\"");
        
        // 方法1: 直接setText（最安全，不触发输入法）
        utils.log("SAFE_INPUT: 尝试直接setText...");
        try {
            const setTextResult = inputElement.setText(commentText);
            if (setTextResult) {
                utils.log("SAFE_INPUT: ✓ 直接setText成功");
                sleep(500);
                
                // 验证输入结果
                const resultText = inputElement.text();
                if (resultText && resultText.trim() === commentText.trim()) {
                    utils.log("SAFE_INPUT: ✓ 输入验证成功");
                    return true;
                } else {
                    utils.log("SAFE_INPUT: ⚠ 输入验证失败，但继续尝试");
                }
                return true;
            }
        } catch (setTextError) {
            utils.log("SAFE_INPUT: setText失败: " + setTextError);
        }
        
        // 方法2: 复制粘贴（不点击输入框，避免弹出键盘）
        utils.log("SAFE_INPUT: 尝试复制粘贴方式...");
        try {
            // 复制到剪贴板
            setClip(commentText);
            sleep(300);
            
            // 先清空输入框
            inputElement.setText("");
            sleep(200);
            
            // 获得焦点但不点击
            inputElement.requestFocus();
            sleep(500);
            
            // 直接长按输入框
            inputElement.longClick();
            sleep(1000);
            
            // 查找并点击粘贴按钮
            const pasteButton = text("粘贴").findOne(2000);
            if (pasteButton) {
                pasteButton.click();
                sleep(800);
                utils.log("SAFE_INPUT: ✓ 粘贴操作完成");
                return true;
            } else {
                utils.log("SAFE_INPUT: 未找到粘贴按钮");
            }
        } catch (pasteError) {
            utils.log("SAFE_INPUT: 粘贴失败: " + pasteError);
        }
        
        // 方法3: 强制隐藏输入法后再setText
        utils.log("SAFE_INPUT: 尝试隐藏输入法后setText...");
        try {
            // 按返回键隐藏可能的输入法
            back();
            sleep(500);
            
            // 再次尝试setText
            const setTextResult2 = inputElement.setText(commentText);
            if (setTextResult2) {
                utils.log("SAFE_INPUT: ✓ 隐藏输入法后setText成功");
                return true;
            }
        } catch (hideError) {
            utils.log("SAFE_INPUT: 隐藏输入法方式失败: " + hideError);
        }
        
        utils.log("SAFE_INPUT: ✗ 所有安全输入方式都失败");
        return false;
        
    } catch (error) {
        utils.log("SAFE_INPUT: ✗ 安全输入异常: " + error);
        return false;
    }
}

/**
 * 在图文笔记中发布评论
 */
function publishImageTextNoteComment(commentText) {
    try {
        utils.log("IMAGE_TEXT_COMMENT: 开始发布图文笔记评论");
        
        // 查找评论输入框
        const commentContainer = id("com.xingin.xhs:id/c9y").findOne(3000);
        if (!commentContainer) {
            utils.log("IMAGE_TEXT_COMMENT: ✗ 未找到评论容器");
            return false;
        }
        
        const commentInput = commentContainer.findOne(id("com.xingin.xhs:id/dwu"));
        if (!commentInput) {
            utils.log("IMAGE_TEXT_COMMENT: ✗ 未找到评论输入框");
            return false;
        }
        
        // 使用安全输入方法
        if (!inputCommentSafely(commentInput, commentText)) {
            utils.log("IMAGE_TEXT_COMMENT: ✗ 评论输入失败");
            return false;
        }
        
        sleep(500);
        
        // 查找并点击发布按钮
        const publishButton = text("发布").findOne(2000);
        if (!publishButton) {
            utils.log("IMAGE_TEXT_COMMENT: ✗ 未找到发布按钮");
            return false;
        }
        
        if (!publishButton.click()) {
            utils.log("IMAGE_TEXT_COMMENT: ✗ 点击发布按钮失败");
            return false;
        }
        
        sleep(2000); // 等待发布完成
        utils.log("IMAGE_TEXT_COMMENT: ✓ 图文笔记评论发布成功");
        return true;
        
    } catch (error) {
        utils.log("IMAGE_TEXT_COMMENT: ✗ 发布异常: " + error);
        return false;
    }
}

/**
 * 在视频笔记中发布评论
 */
function publishVideoNoteComment(commentText) {
    try {
        utils.log("VIDEO_COMMENT: 开始发布视频笔记评论");
        
        // 查找评论输入框
        const commentContainer = id("com.xingin.xhs:id/commentLayout").findOne(3000);
        if (!commentContainer) {
            utils.log("VIDEO_COMMENT: ✗ 未找到评论容器");
            return false;
        }
        
        const commentInput = commentContainer.findOne(id("com.xingin.xhs:id/f4m"));
        if (!commentInput) {
            utils.log("VIDEO_COMMENT: ✗ 未找到评论输入框");
            return false;
        }
        
        // 使用安全输入方法
        if (!inputCommentSafely(commentInput, commentText)) {
            utils.log("VIDEO_COMMENT: ✗ 评论输入失败");
            return false;
        }
        
        sleep(500);
        
        // 查找并点击发布按钮
        const publishButton = text("发布").findOne(2000);
        if (!publishButton) {
            utils.log("VIDEO_COMMENT: ✗ 未找到发布按钮");
            return false;
        }
        
        if (!publishButton.click()) {
            utils.log("VIDEO_COMMENT: ✗ 点击发布按钮失败");
            return false;
        }
        
        sleep(2000); // 等待发布完成
        utils.log("VIDEO_COMMENT: ✓ 视频笔记评论发布成功");
        return true;
        
    } catch (error) {
        utils.log("VIDEO_COMMENT: ✗ 发布异常: " + error);
        return false;
    }
}

/**
 * 根据笔记类型发布评论
 */
function publishCommentByNoteType(noteType, commentText) {
    if (noteType === noteTypes.NOTE_TYPES.VIDEO) {
        return publishVideoNoteComment(commentText);
    } else {
        return publishImageTextNoteComment(commentText);
    }
}

/**
 * 评论管理器（简化版）
 */
function CommentManager(customComments, commentMode) {
    this.comments = [];
    this.commentMode = commentMode || 0;
    this.currentIndex = 0;
    
    if (customComments && typeof customComments === 'string') {
        this.comments = customComments.split('\n')
            .map(function(comment) { return comment.trim(); })
            .filter(function(comment) { return comment.length > 0; });
    }
}

CommentManager.prototype.getNextComment = function() {
    if (this.comments.length === 0) {
        return null;
    }
    
    let selectedComment;
    if (this.commentMode === 0) {
        // 随机模式
        const randomIndex = Math.floor(Math.random() * this.comments.length);
        selectedComment = this.comments[randomIndex];
    } else {
        // 顺序模式
        selectedComment = this.comments[this.currentIndex];
        this.currentIndex = (this.currentIndex + 1) % this.comments.length;
    }
    return selectedComment;
};

CommentManager.prototype.getCommentCount = function() {
    return this.comments.length;
};

/**
 * 在当前笔记中发布评论（简化版主入口）
 */
function publishCommentInCurrentNote(noteTitle, noteAuthor) {
    return new Promise(function(resolve) {
        try {
            utils.log("NOTE_COMMENT: 开始发布评论: " + noteTitle);
            
            // 获取配置
            const commentConfig = configManager.getNoteCommentingConfig();
            if (!commentConfig.enableCommenting) {
                utils.log("NOTE_COMMENT: 评论功能未启用");
                resolve(false);
                return;
            }
            
            // 获取评论内容
            const commentManager = new CommentManager(commentConfig.customComments, commentConfig.commentMode);
            if (commentManager.getCommentCount() === 0) {
                utils.log("NOTE_COMMENT: 没有可用的评论内容");
                resolve(false);
                return;
            }
            
            const commentText = commentManager.getNextComment();
            if (!commentText) {
                utils.log("NOTE_COMMENT: 无法获取评论内容");
                resolve(false);
                return;
            }
            
            // 检测笔记类型并发布评论
            const noteType = noteTypes.detectNoteTypeInDetailPage();
            const publishSuccess = publishCommentByNoteType(noteType, commentText);
            
            if (publishSuccess) {
                utils.log("NOTE_COMMENT: ✓ 评论发布成功: " + commentText);
            } else {
                utils.log("NOTE_COMMENT: ✗ 评论发布失败: " + commentText);
            }
            
            resolve(publishSuccess);
            
        } catch (error) {
            utils.log("NOTE_COMMENT: ✗ 发布过程异常: " + error);
            resolve(false);
        }
    });
}

module.exports = {
    CommentManager,
    publishImageTextNoteComment,
    publishVideoNoteComment,
    publishCommentByNoteType,
    publishCommentInCurrentNote,
    inputCommentSafely
};

utils.log("NOTE_COMMENT: 小红书评论发布模块(简化安全版) 加载完毕");
