// 配置模块：负责读取和保存用户配置

// Auto.js兼容的存储创建
var storage;
if (typeof storages !== 'undefined') {
    storage = storages.create("xhs_auto_script_config");
} else {
    // 如果storages不可用，使用备用方案
    storage = {
        put: function (key, value) { console.log("Storage put:", key, value); },
        get: function (key, defaultValue) { console.log("Storage get:", key); return defaultValue; }
    };
}

function saveConfig(config) {
    try {
        storage.put("searchKeyword", config.searchKeyword || "");
        storage.put("bloggerId", config.bloggerId || "");
        storage.put("commentKeywords", config.commentKeywords || "");
        storage.put("targetRegion", typeof config.targetRegion === 'number' ? config.targetRegion : 0);
        storage.put("messageTemplate", config.messageTemplate || "");
        storage.put("accounts", config.accounts || "");
        // 保存高级搜索配置
        storage.put("advanced_search_keyword", config.advanced_search_keyword || "");
        storage.put("advanced_search_sort_by", typeof config.advanced_search_sort_by === 'number' ? config.advanced_search_sort_by : 0);
        storage.put("advanced_search_publish_time", typeof config.advanced_search_publish_time === 'number' ? config.advanced_search_publish_time : 0);
        storage.put("advanced_search_location_distance", typeof config.advanced_search_location_distance === 'number' ? config.advanced_search_location_distance : 0);

        // 保存任务类型配置
        storage.put("task_collect_users", typeof config.task_collect_users === 'boolean' ? config.task_collect_users : true);
        storage.put("task_comment_notes", typeof config.task_comment_notes === 'boolean' ? config.task_comment_notes : false);
        storage.put("task_like_users", typeof config.task_like_users === 'boolean' ? config.task_like_users : false);

        // 保存评论设置
        storage.put("customComments", typeof config.customComments === 'string' ? config.customComments : "");
        storage.put("commentMode", typeof config.commentMode === 'number' ? config.commentMode : 0);
        storage.put("enableCommentDeduplication", typeof config.enableCommentDeduplication === 'boolean' ? config.enableCommentDeduplication : false);

        // 保存LLM评论配置
        storage.put("enableLlmComments", typeof config.enableLlmComments === 'boolean' ? config.enableLlmComments : false);
        storage.put("useDoubaoProxy", typeof config.useDoubaoProxy === 'boolean' ? config.useDoubaoProxy : false);
        storage.put("doubaoPhoneNumber", config.doubaoPhoneNumber || "");
        storage.put("llmApiUrl", config.llmApiUrl || "");
        storage.put("llmModelName", config.llmModelName || "");
        storage.put("llmUseContentExtraction", typeof config.llmUseContentExtraction === 'boolean' ? config.llmUseContentExtraction : true);
        storage.put("llmUseShareLink", typeof config.llmUseShareLink === 'boolean' ? config.llmUseShareLink : false);
        // storage.put("llmPrompt", config.llmPrompt || ""); // 旧的单一prompt，已由模板取代
        let templatesToSave = Array.isArray(config.llmPromptTemplates) ? config.llmPromptTemplates : [];
        storage.put("llmPromptTemplates", JSON.stringify(templatesToSave));
        storage.put("selectedLlmPromptTemplateName", config.selectedLlmPromptTemplateName || ""); // 保存时尽量直接保存，加载时处理默认/有效性
        storage.put("llmTemperature", typeof config.llmTemperature === 'number' ? config.llmTemperature : 0.7);
        storage.put("llmMaxTokens", typeof config.llmMaxTokens === 'number' ? config.llmMaxTokens : 512);

        // 保存安全控制配置
        storage.put("safetyMinCommentCount", typeof config.safetyMinCommentCount === 'number' ? config.safetyMinCommentCount : 0);
        storage.put("safetyCommentDelayMin", typeof config.safetyCommentDelayMin === 'number' ? config.safetyCommentDelayMin : 5);
        storage.put("safetyCommentDelayMax", typeof config.safetyCommentDelayMax === 'number' ? config.safetyCommentDelayMax : 15);
        storage.put("safetyMaxCommentsPerAccount", typeof config.safetyMaxCommentsPerAccount === 'number' ? config.safetyMaxCommentsPerAccount : 50);

        // 保存多账号配置
        storage.put("enableMultiAccount", typeof config.enableMultiAccount === 'boolean' ? config.enableMultiAccount : false);
        storage.put("accountList", config.accountList || "");
        storage.put("autoSwitchOnLimit", typeof config.autoSwitchOnLimit === 'boolean' ? config.autoSwitchOnLimit : true);

        // 移除评论采集滚动次数限制配置 - 采集所有评论直到底部

        // 可以添加更多需要保存的配置项
        console.log("配置已保存到storage。");
        toast("配置已保存");
    } catch (e) {
        console.error("保存配置失败: ", e);
        toast("保存配置失败: " + e);
    }
}

function loadConfig() {
    try {
        var config = {
            searchKeyword: storage.get("searchKeyword", ""),
            bloggerId: storage.get("bloggerId", ""),
            commentKeywords: storage.get("commentKeywords", ""),
            targetRegion: storage.get("targetRegion", 0),
            messageTemplate: storage.get("messageTemplate", ""),
            accounts: storage.get("accounts", ""),
            // 加载高级搜索配置
            advanced_search_keyword: storage.get("advanced_search_keyword", ""),
            advanced_search_sort_by: storage.get("advanced_search_sort_by", 0),
            advanced_search_publish_time: storage.get("advanced_search_publish_time", 0),
            advanced_search_location_distance: storage.get("advanced_search_location_distance", 0),
            // 加载任务类型配置
            task_collect_users: storage.get("task_collect_users", true),
            task_comment_notes: storage.get("task_comment_notes", false),
            task_like_users: storage.get("task_like_users", false),

            // 加载评论设置
            customComments: storage.get("customComments", ""),
            commentMode: storage.get("commentMode", 0),
            enableCommentDeduplication: storage.get("enableCommentDeduplication", false),
            // 加载LLM评论配置
            enableLlmComments: storage.get("enableLlmComments", false),
            useDoubaoProxy: storage.get("useDoubaoProxy", false),
            doubaoPhoneNumber: storage.get("doubaoPhoneNumber", ""),
            llmApiUrl: storage.get("llmApiUrl", ""),
            llmModelName: storage.get("llmModelName", ""),
            llmUseContentExtraction: storage.get("llmUseContentExtraction", true),
            llmUseShareLink: storage.get("llmUseShareLink", false),
            // llmPrompt: storage.get("llmPrompt", ""), // 旧的单一prompt，已由模板取代
            llmTemperature: storage.get("llmTemperature", 0.7),
            llmMaxTokens: storage.get("llmMaxTokens", 512),
            // 移除评论采集滚动次数限制配置

            // 加载安全控制配置
            safetyMinCommentCount: storage.get("safetyMinCommentCount", 0),
            safetyCommentDelayMin: storage.get("safetyCommentDelayMin", 5),
            safetyCommentDelayMax: storage.get("safetyCommentDelayMax", 15),
            safetyMaxCommentsPerAccount: storage.get("safetyMaxCommentsPerAccount", 50),

            // 加载多账号配置
            enableMultiAccount: storage.get("enableMultiAccount", false),
            accountList: storage.get("accountList", ""),
            autoSwitchOnLimit: storage.get("autoSwitchOnLimit", true)
        };

        // 加载和处理 llmPromptTemplates
        let loadedTemplates;
        const defaultTemplates = [
            { name: "默认内容分析模板", content: "请针对以下小红书笔记内容，生成一条友好且相关的评论。笔记内容：\n{笔记内容}" },
            { name: "默认链接分析模板", content: "请访问以下小红书笔记链接，分析其内容后生成一条友好且相关的评论。请确保评论与笔记内容相关且自然。链接：\n{笔记内容}" }
        ];
        try {
            const templatesString = storage.get("llmPromptTemplates");
            if (templatesString) {
                loadedTemplates = JSON.parse(templatesString);
                // 进一步校验 loadedTemplates 的结构
                if (!Array.isArray(loadedTemplates) || loadedTemplates.length === 0) {
                    console.warn("CONFIG: llmPromptTemplates from storage is not a non-empty array, using default.");
                    loadedTemplates = defaultTemplates;
                } else {
                    // 确保每个模板对象至少有 name 和 content 字段，并且是字符串
                    loadedTemplates = loadedTemplates.map(t => {
                        const name = (t && typeof t.name === 'string' && t.name.trim() !== "") ? t.name.trim() : "未命名模板";
                        const content = (t && typeof t.content === 'string') ? t.content : "";
                        return { name, content };
                    }).filter(t => t.name !== "未命名模板" || t.content !== ""); // 过滤掉完全无效的模板 (比如 name 是 "未命名模板" 且 content 为空)

                    if (loadedTemplates.length === 0) { // 如果过滤后为空，则重新使用默认模板
                        console.warn("CONFIG: After filtering, llmPromptTemplates is empty, using default.");
                        loadedTemplates = defaultTemplates;
                    }
                }
            } else {
                console.warn("CONFIG: No llmPromptTemplates in storage, using default.");
                loadedTemplates = defaultTemplates;
            }
        } catch (e) {
            console.error("CONFIG: Failed to parse llmPromptTemplates, using default.", e);
            loadedTemplates = defaultTemplates;
        }
        config.llmPromptTemplates = loadedTemplates;

        // 加载和处理 selectedLlmPromptTemplateName
        let storedSelectedName = storage.get("selectedLlmPromptTemplateName");
        let finalSelectedName = "";

        if (storedSelectedName && typeof storedSelectedName === 'string' && loadedTemplates.some(t => t.name === storedSelectedName)) {
            finalSelectedName = storedSelectedName;
        } else {
            if (loadedTemplates.length > 0) {
                finalSelectedName = loadedTemplates[0].name;
                if (storedSelectedName && storedSelectedName !== finalSelectedName) {
                    console.warn(`CONFIG: selectedLlmPromptTemplateName '${storedSelectedName}' not found or invalid, defaulting to first template: '${finalSelectedName}'.`);
                } else if (!storedSelectedName && finalSelectedName) { // 只有在 finalSelectedName 有效时才打印 "No selected..."
                    console.warn(`CONFIG: No selectedLlmPromptTemplateName in storage, defaulting to first template: '${finalSelectedName}'.`);
                }
            } else {
                // 此情况理论上不应发生，因为 loadedTemplates 总会有一个默认模板 (除非默认模板也被过滤了，极端情况)
                console.warn("CONFIG: llmPromptTemplates is unexpectedly empty after load, selectedLlmPromptTemplateName set to empty string.");
            }
        }
        config.selectedLlmPromptTemplateName = finalSelectedName;

        console.log("从storage加载的配置 (V_template_mgmt):", JSON.stringify(config)); // 使用JSON.stringify避免对象展开问题
        return config;
    } catch (e) {
        console.error("加载配置失败: ", e);
        toast("加载配置失败: " + e);
        // 返回一个包含最基本默认值的配置，以减少后续代码出错的可能
        return {
            searchKeyword: "",
            bloggerId: "",
            commentKeywords: "",
            targetRegion: 0,
            messageTemplate: "",
            accounts: "",
            advanced_search_keyword: "",
            advanced_search_sort_by: 0,
            advanced_search_publish_time: 0,
            advanced_search_location_distance: 0,
            task_collect_users: true,
            task_comment_notes: false,
            task_like_users: false,
            customComments: "",
            commentMode: 0,
            enableCommentDeduplication: false,
            enableLlmComments: false,
            useDoubaoProxy: false,
            doubaoPhoneNumber: "",
            llmApiUrl: "",
            llmModelName: "",
            llmUseContentExtraction: true,
            llmUseShareLink: false,
            llmPromptTemplates: [
                { name: "默认内容分析模板", content: "请针对以下小红书笔记内容，生成一条友好且相关的评论。笔记内容：\n{笔记内容}" },
                { name: "默认链接分析模板", content: "请访问以下小红书笔记链接，分析其内容后生成一条友好且相关的评论。请确保评论与笔记内容相关且自然。链接：\n{笔记内容}" }
            ],
            selectedLlmPromptTemplateName: "默认内容分析模板",
            llmTemperature: 0.7,
            llmMaxTokens: 512,

            // 默认安全控制配置
            safetyMinCommentCount: 0,
            safetyCommentDelayMin: 5,
            safetyCommentDelayMax: 15,
            safetyMaxCommentsPerAccount: 50,

            // 默认多账号配置
            enableMultiAccount: false,
            accountList: "",
            autoSwitchOnLimit: true
        };
    }
}

// 获取评论采集相关配置
function getCommentScrapingConfig() {
    var commonConfig = loadConfig(); // 复用loadConfig获取所有配置值
    return {
        commentKeywords: commonConfig.commentKeywords, // 关键词
        targetRegion: commonConfig.targetRegion || 0, // 目标区域索引
        enableLiking: commonConfig.task_like_users || false // 是否启用点赞功能
    };
}

// 获取笔记评论相关配置
function getNoteCommentingConfig() {
    var commonConfig = loadConfig(); // 复用loadConfig获取所有配置值

    return {
        customComments: commonConfig.customComments || "", // 自定义评论内容字符串
        commentMode: commonConfig.commentMode === 1 ? 'sequential' : 'random', // 评论模式：random或sequential
        enableCommenting: commonConfig.task_comment_notes || false, // 是否启用评论功能
        enableDeduplication: commonConfig.enableCommentDeduplication || false, // 是否启用去重功能

        // AI评论相关配置
        enableLlmComments: commonConfig.enableLlmComments || false, // 是否启用AI评论
        useDoubaoProxy: commonConfig.useDoubaoProxy || false, // 是否使用豆包AI
        doubaoPhoneNumber: commonConfig.doubaoPhoneNumber || "", // 豆包登录手机号
        llmUseShareLink: commonConfig.llmUseShareLink || false, // 是否使用分享链接
        llmUseContentExtraction: commonConfig.llmUseContentExtraction || false, // 是否使用内容提取
        selectedLlmPromptTemplateName: commonConfig.selectedLlmPromptTemplateName || "", // 选中的提示模板名称
        llmPromptTemplates: commonConfig.llmPromptTemplates || [], // 提示模板数组
        llmApiUrl: commonConfig.llmApiUrl || "", // LLM API URL
        llmModelName: commonConfig.llmModelName || "", // LLM模型名称
        llmTemperature: commonConfig.llmTemperature || 0.7, // LLM温度参数
        llmMaxTokens: commonConfig.llmMaxTokens || 150, // LLM最大token数

        // 安全控制配置
        safetyMinCommentCount: commonConfig.safetyMinCommentCount || 0, // 最少评论数要求
        safetyCommentDelayMin: commonConfig.safetyCommentDelayMin || 5, // 评论延迟最小值（秒）
        safetyCommentDelayMax: commonConfig.safetyCommentDelayMax || 15, // 评论延迟最大值（秒）
        safetyMaxCommentsPerAccount: commonConfig.safetyMaxCommentsPerAccount || 50, // 单账号最大评论次数

        // 多账号配置
        enableMultiAccount: commonConfig.enableMultiAccount || false, // 是否启用多账号
        accountList: commonConfig.accountList || "", // 账号列表
        autoSwitchOnLimit: commonConfig.autoSwitchOnLimit !== undefined ? commonConfig.autoSwitchOnLimit : true // 达到限制时自动切换
    };
}

// Function to specifically set the note commenting enable status
function setNoteCommentingEnable(isEnabled) {
    try {
        storage.put("task_comment_notes", typeof isEnabled === 'boolean' ? isEnabled : false);
        console.log("CONFIG: Note commenting enabled status set to: " + isEnabled);
    } catch (e) {
        console.error("CONFIG: Failed to set note commenting status: ", e);
    }
}

// Auto.js兼容的导出方式
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        saveConfig: saveConfig,
        loadConfig: loadConfig,
        getCommentScrapingConfig: getCommentScrapingConfig,
        getNoteCommentingConfig: getNoteCommentingConfig,
        setNoteCommentingEnable: setNoteCommentingEnable
    };
} else {
    // Auto.js环境 - 使用全局导出
    this.saveConfig = saveConfig;
    this.loadConfig = loadConfig;
    this.getCommentScrapingConfig = getCommentScrapingConfig;
    this.getNoteCommentingConfig = getNoteCommentingConfig;
    this.setNoteCommentingEnable = setNoteCommentingEnable;
}

console.log("配置模块加载完毕 (config.js) - V4 (setNoteCommentingEnable added)");