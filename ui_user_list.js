// ui_user_list.js - 用户列表功能模块
"ui";

console.log("UI_USER_LIST: ui_user_list.js module parsed.");

/**
 * 附加用户列表相关的事件处理器
 * @param {Object} uiObject - UI对象
 * @param {Object} dataManagerObject - 数据管理对象
 * @param {Object} utilsObject - 工具对象
 */
function attachUserListEventHandlers(uiObject, dataManagerObject, utilsObject) {
    console.log("UI_USER_LIST: Attaching user list event handlers.");

    if (!dataManagerObject || !utilsObject) {
        console.error("UI_USER_LIST: Missing required objects");
        return;
    }

    // 用户搜索功能
    if (uiObject.userSearchBtn) {
        uiObject.userSearchBtn.on("click", function () {
            var keyword = uiObject.userSearchInput ? uiObject.userSearchInput.getText().toString().trim() : '';
            utilsObject.log("UI_USER_LIST: Searching users with keyword: " + keyword);

            try {
                var searchResults = dataManagerObject.searchUsers(keyword);
                updateUserListDisplay(uiObject, searchResults, utilsObject);
                toast("找到 " + searchResults.length + " 个匹配用户");
            } catch (e) {
                utilsObject.log("UI_USER_LIST: Search error: " + e.toString());
                toast("搜索失败");
            }
        });
    }

    // 地区筛选功能
    if (uiObject.userFilterBtn) {
        uiObject.userFilterBtn.on("click", function () {
            var regionIndex = uiObject.userRegionFilter ? uiObject.userRegionFilter.getSelectedItemPosition() : 0;
            var regions = ['全部', '北京', '上海', '广东', '浙江', '江苏', '山东', '四川', '湖北', '湖南', '河南', '福建', '安徽', '河北', '陕西', '辽宁', '云南', '广西', '江西', '山西', '吉林', '贵州', '重庆', '天津', '内蒙古', '新疆', '甘肃', '海南', '宁夏', '青海', '西藏', '黑龙江', '未知'];
            var selectedRegion = regions[regionIndex] || '全部';

            utilsObject.log("UI_USER_LIST: Filtering users by region: " + selectedRegion);

            try {
                var filteredResults = dataManagerObject.filterUsersByRegion(selectedRegion);
                updateUserListDisplay(uiObject, filteredResults, utilsObject);
                toast(selectedRegion + "地区共 " + filteredResults.length + " 个用户");
            } catch (e) {
                utilsObject.log("UI_USER_LIST: Filter error: " + e.toString());
                toast("筛选失败");
            }
        });
    }

    // 导出CSV功能
    if (uiObject.exportCsvBtn) {
        uiObject.exportCsvBtn.on("click", function () {
            utilsObject.log("UI_USER_LIST: Export CSV button clicked");

            try {
                var allUsers = dataManagerObject.getScrapedCommentUsers();
                if (allUsers.length === 0) {
                    toast("没有用户数据可导出");
                    return;
                }

                var timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
                var filePath = files.join(files.getSdcardPath(), "XHS_Users_Export_" + timestamp + ".csv");

                var success = dataManagerObject.exportUsersToCSV(allUsers, filePath);
                if (success) {
                    utilsObject.log("UI_USER_LIST: CSV exported to " + filePath);
                } else {
                    toast("导出失败");
                }
            } catch (e) {
                utilsObject.log("UI_USER_LIST: Export CSV error: " + e.toString());
                toast("导出失败");
            }
        });
    }

    // 导出TXT功能
    if (uiObject.exportTxtBtn) {
        uiObject.exportTxtBtn.on("click", function () {
            utilsObject.log("UI_USER_LIST: Export TXT button clicked");

            try {
                var allUsers = dataManagerObject.getScrapedCommentUsers();
                if (allUsers.length === 0) {
                    toast("没有用户数据可导出");
                    return;
                }

                var timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
                var filePath = files.join(files.getSdcardPath(), "XHS_Users_Export_" + timestamp + ".txt");

                var success = dataManagerObject.exportUsersToTXT(allUsers, filePath);
                if (success) {
                    utilsObject.log("UI_USER_LIST: TXT exported to " + filePath);
                } else {
                    toast("导出失败");
                }
            } catch (e) {
                utilsObject.log("UI_USER_LIST: Export TXT error: " + e.toString());
                toast("导出失败");
            }
        });
    }

    // 更新地区信息功能
    if (uiObject.updateRegionBtn) {
        uiObject.updateRegionBtn.on("click", function () {
            utilsObject.log("UI_USER_LIST: Update region button clicked");

            try {
                if (dataManagerObject && typeof dataManagerObject.updateUsersRegionFromComments === 'function') {
                    var success = dataManagerObject.updateUsersRegionFromComments();
                    if (success) {
                        // 刷新用户列表显示
                        var allUsers = dataManagerObject.getScrapedCommentUsers();
                        updateUserListDisplay(uiObject, allUsers, utilsObject);
                        // 刷新统计信息
                        refreshUserStatistics(uiObject, dataManagerObject, utilsObject);
                        utilsObject.log("UI_USER_LIST: Region update completed");
                    }
                } else {
                    toast("更新地区功能暂不可用");
                }
            } catch (e) {
                utilsObject.log("UI_USER_LIST: Update region error: " + e.toString());
                toast("更新地区信息失败");
            }
        });
    }

    // 清空数据功能
    if (uiObject.clearUsersBtn) {
        uiObject.clearUsersBtn.on("click", function () {
            utilsObject.log("UI_USER_LIST: Clear users button clicked");

            // 确认对话框
            dialogs.confirm("确认清空", "确定要清空所有用户数据吗？此操作不可恢复！")
                .then(function (confirmed) {
                    if (confirmed) {
                        try {
                            var success = dataManagerObject.clearScrapedCommentUsers();
                            if (success) {
                                // 刷新用户列表显示
                                updateUserListDisplay(uiObject, [], utilsObject);
                                // 更新统计信息
                                if (uiObject.userStatsText) {
                                    uiObject.userStatsText.setText("总用户: 0 | 今日新增: 0");
                                }
                                utilsObject.log("UI_USER_LIST: All user data cleared");
                                toast("用户数据已清空");
                            } else {
                                toast("清空失败");
                            }
                        } catch (e) {
                            utilsObject.log("UI_USER_LIST: Clear users error: " + e.toString());
                            toast("清空失败");
                        }
                    }
                })
                .catch(function (error) {
                    utilsObject.log("UI_USER_LIST: Clear confirmation error: " + error);
                });
        });
    }

    // 用户列表项点击事件（显示详细信息）
    if (uiObject.usersList) {
        uiObject.usersList.on("item_click", function (item, i, itemView, listView) {
            try {
                var user = item;
                var detailText = "用户详情：\n" +
                    "昵称：" + (user.nickname || '未知') + "\n" +
                    "小红书号：" + (user.xhsId || '未获取') + "\n" +
                    "地区：" + (user.region || '未知') + "\n" +
                    "状态：" + (user.status || 'collected') + "\n" +
                    "评论内容：" + (user.commentText || user.comment || '无') + "\n" +
                    "笔记标题：" + (user.noteTitle || '未知') + "\n" +
                    "笔记ID：" + (user.noteId || '未知') + "\n" +
                    "采集时间：" + (user.timeFormatted || '未知');

                dialogs.alert("用户详情", detailText);
                utilsObject.log("UI_USER_LIST: Showing details for user: " + user.nickname);
            } catch (e) {
                utilsObject.log("UI_USER_LIST: Show user details error: " + e.toString());
                toast("显示详情失败");
            }
        });
    }
}

/**
 * 更新用户列表显示
 * @param {Object} uiObject - UI对象
 * @param {Array} users - 用户数组
 * @param {Object} utilsObject - 工具对象
 */
function updateUserListDisplay(uiObject, users, utilsObject) {
    try {
        // 格式化用户数据用于列表显示
        var formattedUsers = [];
        for (var i = 0; i < users.length; i++) {
            var user = users[i];
            var commentText = user.commentText || user.comment || '';
            var formattedUser = {
                uid: user.uid,
                nickname: user.nickname,
                xhsId: user.xhsId || '未获取',
                commentText: commentText.substring(0, 50) + (commentText.length > 50 ? '...' : ''),
                noteId: user.noteId,
                noteTitle: user.noteTitle,
                region: user.region || '未知',
                status: user.status || 'collected',
                timestamp: user.timestamp,
                timeFormatted: user.timestamp ? new Date(user.timestamp).toLocaleString('zh-CN') : '未知'
            };
            formattedUsers.push(formattedUser);
        }

        // 更新用户列表
        if (uiObject.usersList) {
            uiObject.usersList.setDataSource(formattedUsers);
        }

        utilsObject.log("UI_USER_LIST: Updated user list display with " + users.length + " users");

    } catch (e) {
        utilsObject.log("UI_USER_LIST: Error updating user list display: " + e.toString());
    }
}

/**
 * 刷新用户统计信息
 * @param {Object} uiObject - UI对象
 * @param {Object} dataManagerObject - 数据管理对象
 * @param {Object} utilsObject - 工具对象
 */
function refreshUserStatistics(uiObject, dataManagerObject, utilsObject) {
    try {
        var stats = dataManagerObject.getUserStatistics();

        // 更新统计信息显示
        if (uiObject.userStatsText) {
            var statsText = "总用户: " + stats.totalUsers + " | 今日新增: " + stats.recentUsers;
            uiObject.userStatsText.setText(statsText);
        }

        utilsObject.log("UI_USER_LIST: Updated statistics - Total: " + stats.totalUsers + ", Recent: " + stats.recentUsers);

    } catch (e) {
        utilsObject.log("UI_USER_LIST: Error refreshing statistics: " + e.toString());
    }
}

module.exports = {
    attachUserListEventHandlers: attachUserListEventHandlers,
    updateUserListDisplay: updateUserListDisplay,
    refreshUserStatistics: refreshUserStatistics
};
