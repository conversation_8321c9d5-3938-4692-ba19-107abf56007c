/**
 * 豆包WebView自动化模块 - 简化版
 */

var utils = require(files.path("./utils.js"));
var DoubaoLoginManager = require(files.path("./doubao_login.js")).DoubaoLoginManager;

/**
 * 简单的豆包WebView自动化
 */
function DoubaoWebViewSimple() {
    this.webview = null;
    this.loginManager = null;
}

/**
 * 初始化WebView
 */
DoubaoWebViewSimple.prototype.init = function () {
    try {
        utils.log("DOUBAO_SIMPLE: 创建WebView窗口...");

        // 创建带关闭按钮的WebView
        this.webview = floaty.rawWindow(
            `<vertical>
                <horizontal bg="#ff0000" h="50">
                    <text text="豆包WebView" textColor="#ffffff" layout_weight="1" gravity="center"/>
                    <button id="closeBtn" text="关闭" w="80" h="40" textColor="#ffffff" bg="#cc0000"/>
                </horizontal>
                <webview id="webview" h="*" w="*" />
            </vertical>`
        );

        // 设置窗口全屏
        this.webview.setPosition(0, 0);
        this.webview.setSize(device.width, device.height);
        this.webview.setTouchable(true);

        // 关闭按钮事件
        var self = this;
        this.webview.closeBtn.click(function () {
            self.cleanup();
        });

        sleep(3000);

        // 配置WebView - 和test_direct_input.js一样的方式
        this.webviewControl = this.webview.webview;  // 保存为类属性
        var webview = this.webviewControl;
        ui.run(function () {
            var settings = webview.getSettings();
            settings.setJavaScriptEnabled(true);
            settings.setDomStorageEnabled(true);
            settings.setUserAgentString(
                "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
            );

            webview.loadUrl("https://www.doubao.com/chat/");
        });

        utils.log("DOUBAO_SIMPLE: 等待页面加载...");
        sleep(20000); // 等待20秒

        // 初始化登录管理器
        this.loginManager = new DoubaoLoginManager();
        this.loginManager.init(this.webviewControl);

        utils.log("DOUBAO_SIMPLE: 初始化完成");
        return true;

    } catch (error) {
        utils.log("DOUBAO_SIMPLE: 初始化失败: " + error, "error");
        return false;
    }
};

/**
 * 发送消息 - 改为异步模式，参考test_direct_input.js的成功实现
 */
DoubaoWebViewSimple.prototype.sendMessage = function (message) {
    try {
        utils.log("DOUBAO_SIMPLE: 开始发送消息...");
        utils.log("DOUBAO_SIMPLE: 请观察页面，10秒后开始操作...");
        sleep(10000);

        var webview = this.webviewControl;
        var inputSuccess = false;
        var sendSuccess = false;

        // 步骤1：输入消息 - 完全复制test_direct_input.js的成功代码
        utils.log("DOUBAO_SIMPLE: 开始输入消息...");

        ui.run(function () {
            webview.evaluateJavascript(`
                (function() {
                    var input = document.querySelector('textarea[data-testid="chat_input_input"]');
                    if (!input) {
                        return '未找到输入框';
                    }

                    // 点击并聚焦
                    input.click();
                    input.focus();

                    // 使用React兼容的方式输入消息
                    var messageText = '${message.replace(/'/g, "\\'")}';

                    // 清空并重新设置
                    input.value = '';
                    input.focus();

                    // 使用原生setter绕过框架限制
                    var nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, "value").set;
                    nativeInputValueSetter.call(input, messageText);

                    // 触发必要的事件
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    input.dispatchEvent(new Event('change', { bubbles: true }));

                    // 确保值被设置
                    if (input.value !== messageText) {
                        input.value = messageText;
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                    }

                    return '输入完成，当前值: ' + input.value;
                })();
            `, new JavaAdapter(android.webkit.ValueCallback, {
                onReceiveValue: function (value) {
                    utils.log("DOUBAO_SIMPLE: 输入结果: " + (value || "无返回值"));
                    if (value && value.includes('输入完成')) {
                        inputSuccess = true;
                    }
                }
            }));
        });

        // 等待输入完成
        var inputWait = 0;
        while (!inputSuccess && inputWait < 50) {
            sleep(100);
            inputWait++;
        }

        if (!inputSuccess) {
            utils.log("DOUBAO_SIMPLE: 输入失败", "error");
            return null;
        }

        sleep(3000); // 等待3秒，让输入充分生效

        // 步骤2：点击发送按钮 - 使用更符合HTML结构的方式
        utils.log("DOUBAO_SIMPLE: 开始点击发送按钮...");

        ui.run(function () {
            webview.evaluateJavascript(`
                (function() {
                    // 方法1: 直接通过ID查找
                    var sendBtn = document.getElementById('flow-end-msg-send');
                    if (sendBtn) {
                        // 检查按钮状态
                        var isDisabled = sendBtn.getAttribute('aria-disabled') === 'true' || sendBtn.disabled;
                        if (isDisabled) {
                            return '发送按钮被禁用';
                        }

                        // 尝试多种点击方式
                        try {
                            // 方式1: 直接点击
                            sendBtn.click();

                            // 方式2: 触发鼠标事件
                            var clickEvent = new MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            });
                            sendBtn.dispatchEvent(clickEvent);

                            // 方式3: 触发指针事件
                            var pointerEvent = new PointerEvent('pointerdown', {
                                bubbles: true,
                                cancelable: true
                            });
                            sendBtn.dispatchEvent(pointerEvent);

                            return '发送按钮已点击（多种方式）';
                        } catch (e) {
                            return '点击发送按钮时出错: ' + e.message;
                        }
                    }

                    // 方法2: 通过data-testid查找
                    var sendBtnByTestId = document.querySelector('[data-testid="chat_input_send_button"]');
                    if (sendBtnByTestId) {
                        try {
                            sendBtnByTestId.click();
                            return '通过testid找到并点击发送按钮';
                        } catch (e) {
                            return '通过testid点击时出错: ' + e.message;
                        }
                    }

                    // 方法3: 通过类名查找
                    var sendBtnByClass = document.querySelector('.send-btn-sJshF9');
                    if (sendBtnByClass) {
                        try {
                            sendBtnByClass.click();
                            return '通过类名找到并点击发送按钮';
                        } catch (e) {
                            return '通过类名点击时出错: ' + e.message;
                        }
                    }

                    return '未找到发送按钮（尝试了多种方式）';
                })();
            `, new JavaAdapter(android.webkit.ValueCallback, {
                onReceiveValue: function (value) {
                    utils.log("DOUBAO_SIMPLE: 发送结果: " + (value || "无返回值"));
                    if (value && (value.includes('发送按钮已点击') || value.includes('找到并点击发送按钮'))) {
                        sendSuccess = true;
                    }
                }
            }));
        });

        // 等待发送完成
        var sendWait = 0;
        while (!sendSuccess && sendWait < 50) {
            sleep(100);
            sendWait++;
        }

        if (!sendSuccess) {
            utils.log("DOUBAO_SIMPLE: 发送失败", "error");
            return null;
        }

        utils.log("DOUBAO_SIMPLE: 消息发送成功，等待页面转换到聊天状态...");

        // 等待页面从图一转换到图二状态（减少等待时间）
        if (!this.waitForChatPageTransition(message)) {
            utils.log("DOUBAO_SIMPLE: 页面未转换到聊天状态", "error");
            return null;
        }

        utils.log("DOUBAO_SIMPLE: 页面已转换到聊天状态，等待AI回复...");
        return this.waitForResponse();

    } catch (error) {
        utils.log("DOUBAO_SIMPLE: 发送消息失败: " + error, "error");
        return null;
    }
};

/**
 * 等待页面从欢迎页面转换到聊天页面
 */
DoubaoWebViewSimple.prototype.waitForChatPageTransition = function (sentMessage) {
    utils.log("DOUBAO_SIMPLE: 检测页面状态转换...");

    var maxWait = 15; // 减少到15秒
    var waitCount = 0;
    var self = this;
    var transitionComplete = false;

    while (waitCount < maxWait && !transitionComplete) {
        var checkResult = false;

        ui.run(function () {
            self.webviewControl.evaluateJavascript(`
                (function() {
                    // 检查是否还在欢迎页面（图一状态）
                    var welcomeText = document.querySelector('*');
                    var hasWelcome = document.body.textContent.includes('你好，我是豆包');

                    // 检查是否已进入聊天页面（图二状态）
                    var hasChatMessages = false;
                    var userMessage = '${sentMessage.replace(/'/g, "\\'")}';

                    // 查找用户发送的消息是否出现在页面上
                    if (document.body.textContent.includes(userMessage)) {
                        hasChatMessages = true;
                    }

                    // 检查是否有聊天容器
                    var chatContainers = document.querySelectorAll('[class*="message"], [class*="chat"], [class*="conversation"]');

                    return {
                        hasWelcome: hasWelcome,
                        hasChatMessages: hasChatMessages,
                        chatContainerCount: chatContainers.length,
                        status: hasChatMessages ? 'chat' : (hasWelcome ? 'welcome' : 'unknown')
                    };
                })();
            `, new JavaAdapter(android.webkit.ValueCallback, {
                onReceiveValue: function (value) {
                    try {
                        var result = value ? JSON.parse(value.replace(/^"|"$/g, '')) : null;
                        if (result) {
                            utils.log("DOUBAO_SIMPLE: 页面状态 - " + result.status +
                                ", 欢迎页面: " + result.hasWelcome +
                                ", 聊天消息: " + result.hasChatMessages +
                                ", 聊天容器: " + result.chatContainerCount);

                            if (result.status === 'chat' && result.hasChatMessages) {
                                transitionComplete = true;
                                checkResult = true;
                            }
                        }
                    } catch (parseError) {
                        utils.log("DOUBAO_SIMPLE: 解析页面状态失败: " + parseError);
                    }
                }
            }));
        });

        // 等待回调执行
        var callbackWait = 0;
        while (!checkResult && callbackWait < 20) {
            sleep(100);
            callbackWait++;
        }

        if (transitionComplete) {
            utils.log("DOUBAO_SIMPLE: 页面已成功转换到聊天状态");
            return true;
        }

        sleep(500); // 减少检查间隔
        waitCount += 1;
    }

    utils.log("DOUBAO_SIMPLE: 页面转换超时，仍在欢迎页面");
    return false;
};

/**
 * 等待回复 - 改为异步回调模式
 */
DoubaoWebViewSimple.prototype.waitForResponse = function () {
    utils.log("DOUBAO_SIMPLE: 等待AI回复...");

    sleep(2000); // 减少初始等待时间

    var maxWait = 60;
    var waitCount = 0;
    var self = this;
    var finalResponse = null;

    while (waitCount < maxWait && !finalResponse) {
        // 使用异步方式检查回复
        var responseFound = false;

        ui.run(function () {
            self.webviewControl.evaluateJavascript(`
                (function() {
                    // 针对聊天页面（图二）优化的AI回复检测
                    var response = '';
                    var userMessage = '请为这个旅游笔记写一条评论';

                    // 方法1: 查找聊天消息容器 - 针对图二状态
                    var messageContainers = document.querySelectorAll('[class*="message"], [class*="chat"], [class*="conversation"], [class*="bubble"], [class*="item"]');
                    console.log("DOUBAO_SIMPLE: 在聊天页面找到 " + messageContainers.length + " 个消息容器");

                    // 查找最新的非用户消息
                    for (var i = messageContainers.length - 1; i >= 0; i--) {
                        var container = messageContainers[i];
                        var text = container.textContent || container.innerText || '';

                        // 过滤条件：
                        // 1. 长度足够（AI回复通常比较长）
                        // 2. 不包含用户输入的内容
                        // 3. 不包含界面提示文字
                        if (text.length > 15 &&
                            !text.includes(userMessage) &&
                            !text.includes('你好，我是豆包') &&
                            !text.includes('发消息') &&
                            !text.includes('输入') &&
                            !text.includes('选择技能') &&
                            !text.includes('写作') &&
                            !text.includes('图像生成') &&
                            !text.includes('AI 搜索') &&
                            text.trim() !== '你好') {

                            // 进一步检查是否是AI回复（通常包含评论相关词汇）
                            if (text.includes('评论') || text.includes('美丽') || text.includes('风景') ||
                                text.includes('海滩') || text.includes('景色') || text.includes('真') ||
                                text.includes('好') || text.includes('棒') || text.includes('！') ||
                                text.length > 30) {
                                response = text.trim();
                                console.log("DOUBAO_SIMPLE: 找到疑似AI回复: " + response.substring(0, 50));
                                break;
                            }
                        }
                    }

                    // 方法2: 如果没找到，查找页面上最新的长文本
                    if (!response) {
                        var allTexts = [];
                        var walker = document.createTreeWalker(
                            document.body,
                            NodeFilter.SHOW_TEXT,
                            null,
                            false
                        );

                        var node;
                        while (node = walker.nextNode()) {
                            var text = node.textContent.trim();
                            if (text.length > 20 &&
                                !text.includes(userMessage) &&
                                !text.includes('你好，我是豆包')) {
                                allTexts.push(text);
                            }
                        }

                        // 取最后一个符合条件的文本
                        if (allTexts.length > 0) {
                            response = allTexts[allTexts.length - 1];
                        }
                    }

                    return response || '未找到回复';
                })();
            `, new JavaAdapter(android.webkit.ValueCallback, {
                onReceiveValue: function (value) {
                    var response = value ? value.replace(/^"|"$/g, '') : null;
                    utils.log("DOUBAO_SIMPLE: 检测到内容: " + (response || "无内容"));

                    if (response && response.length > 15 && !response.includes('未找到回复')) {
                        utils.log("DOUBAO_SIMPLE: 获取到回复: " + response.substring(0, 100) + "...");
                        finalResponse = response;
                        responseFound = true;
                    }
                }
            }));
        });

        // 等待回调执行
        var callbackWait = 0;
        while (!responseFound && callbackWait < 20) {
            sleep(100);
            callbackWait++;
        }

        if (finalResponse) {
            return finalResponse;
        }

        sleep(2000);
        waitCount += 2;
    }

    utils.log("DOUBAO_SIMPLE: 等待回复超时");
    return null;
};

/**
 * 执行JavaScript - 改为异步回调模式，参考test_direct_input.js的成功实现
 */
DoubaoWebViewSimple.prototype.executeJSAsync = function (jsCode, callback) {
    if (!this.webview || !this.webview.webview) {
        callback(null, "WebView未初始化");
        return;
    }

    var self = this;
    ui.run(function () {
        try {
            self.webviewControl.evaluateJavascript(jsCode, new JavaAdapter(android.webkit.ValueCallback, {
                onReceiveValue: function (value) {
                    var result = value ? value.replace(/^"|"$/g, '') : null;
                    callback(result, null);
                }
            }));
        } catch (jsError) {
            utils.log("DOUBAO_SIMPLE: JS执行异常: " + jsError);
            callback(null, jsError.toString());
        }
    });
};

/**
 * 清理资源
 */
DoubaoWebViewSimple.prototype.cleanup = function () {
    try {
        if (this.webview) {
            ui.run(() => {
                this.webview.close();
            });
            this.webview = null;
        }
        utils.log("DOUBAO_SIMPLE: 资源已清理");
    } catch (error) {
        utils.log("DOUBAO_SIMPLE: 清理失败: " + error, "warn");
    }
};

/**
 * 主函数 - 不带登录
 */
function generateCommentWithDoubaoSimple(shareLink, promptTemplate) {
    var doubao = new DoubaoWebViewSimple();

    try {
        if (!doubao.init()) {
            return { success: false, comment: null, error: "初始化失败" };
        }

        var fullPrompt = "分享链接内容：\n" + shareLink + "\n\n" + promptTemplate;
        var response = doubao.sendMessage(fullPrompt);

        if (response && response.trim() !== "") {
            return { success: true, comment: response.trim(), error: null, webview: doubao };
        } else {
            return { success: false, comment: null, error: "未获取到有效回复" };
        }

    } catch (error) {
        utils.log("DOUBAO_SIMPLE: 生成评论失败: " + error, "error");
        return { success: false, comment: null, error: error.toString() };
    }
}

/**
 * 主函数 - 带自动登录功能
 */
function generateCommentWithDoubaoLogin(shareLink, promptTemplate, phoneNumber) {
    var doubao = new DoubaoWebViewSimple();

    try {
        if (!doubao.init()) {
            return { success: false, comment: null, error: "初始化失败" };
        }

        // 尝试自动登录
        utils.log("DOUBAO_SIMPLE: 开始检查登录状态...");
        var loginResult = doubao.loginManager.autoLogin(phoneNumber);

        if (!loginResult.success) {
            utils.log("DOUBAO_SIMPLE: 登录失败: " + loginResult.message, "error");
            return { success: false, comment: null, error: "登录失败: " + loginResult.message };
        }

        utils.log("DOUBAO_SIMPLE: 登录成功，开始生成评论...");

        var fullPrompt = "分享链接内容：\n" + shareLink + "\n\n" + promptTemplate;
        var response = doubao.sendMessage(fullPrompt);

        if (response && response.trim() !== "") {
            return { success: true, comment: response.trim(), error: null, webview: doubao };
        } else {
            return { success: false, comment: null, error: "未获取到有效回复" };
        }

    } catch (error) {
        utils.log("DOUBAO_SIMPLE: 生成评论失败: " + error, "error");
        return { success: false, comment: null, error: error.toString() };
    }
}

module.exports = {
    DoubaoWebViewSimple: DoubaoWebViewSimple,
    generateCommentWithDoubaoSimple: generateCommentWithDoubaoSimple,
    generateCommentWithDoubaoLogin: generateCommentWithDoubaoLogin
};

utils.log("豆包WebView简化模块加载完毕 (doubao_webview_simple.js)");
