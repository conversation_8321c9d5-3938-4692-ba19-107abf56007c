# 小红书自动化安全控制功能说明

## 功能概述

为了确保小红书自动化工具的安全使用，避免因过度频繁的操作而被平台检测，我们新增了三项重要的安全控制功能：

1. **评论数筛选**：只对评论数达到指定数量的笔记进行评论
2. **随机延迟**：在发布评论前执行随机时间延迟
3. **每日限制**：限制单个账号每日最大评论次数

## 功能详细说明

### 1. 评论数筛选

**功能描述**：只评论评论数达到指定数量以上的笔记

**配置项**：
- 设置项：`只评论评论数____次以上的笔记`
- 默认值：0（无限制）
- 数据来源：搜索结果页单个笔记容器内用户昵称的右侧，元素ID为 `com.xingin.xhs:id/dyq`

**工作原理**：
- 在搜索结果页面提取每个笔记的评论数信息
- 支持解析"万"、"k"等单位（如：1.2万 = 12000，5k = 5000）
- 在进入笔记详情页前进行筛选，不符合条件的笔记直接跳过

**使用场景**：
- 针对热门笔记进行评论，提高曝光效果
- 避免在冷门笔记上浪费评论机会
- 确保评论的目标受众更广泛

### 2. 随机延迟控制

**功能描述**：在发布评论前执行随机时间延迟，模拟真实用户行为

**配置项**：
- 设置项：`在随机时间___至___内发布评论`
- 默认值：5-15秒
- 单位：秒

**工作原理**：
- 在实际发布评论前，系统会计算一个随机延迟时间
- 延迟时间在设定的最小值和最大值之间随机生成
- 延迟期间显示倒计时，用户可观察执行状态

**使用场景**：
- 模拟真实用户的思考和输入时间
- 避免机器化的固定时间间隔
- 降低被平台检测为自动化行为的风险

### 3. 每日评论次数限制

**功能描述**：限制单个账号每日最大评论次数，防止过度使用

**配置项**：
- 设置项：`单个账号最多评论___次`
- 默认值：50次
- 重置时间：每日0点自动重置

**工作原理**：
- 系统自动记录每日评论次数
- 达到限制后自动停止评论功能
- 支持手动重置计数（用于测试或特殊情况）
- 数据持久化存储，重启应用后保持计数

**使用场景**：
- 防止单日评论过多引起平台注意
- 合理分配评论资源
- 符合平台的使用规范

## 安全状态监控

### 状态显示
在设置页面新增"安全状态"显示区域，实时显示：
- 今日评论次数
- 上次评论时间

### 管理功能
- **刷新状态**：手动更新安全状态显示
- **重置计数**：清零今日评论计数（用于测试或特殊需求）

## 技术实现

### 核心模块
- `xhs_safety_control.js`：安全控制核心模块
- 集成到主程序流程中，无需额外配置

### 数据存储
- 使用Auto.js的storages API进行数据持久化
- 存储内容：每日评论次数、上次评论时间、日期标识

### 集成点
1. **搜索结果页**：提取笔记评论数信息
2. **安全检查**：进入笔记前进行综合安全检查
3. **评论发布前**：执行随机延迟
4. **评论发布后**：记录评论操作，更新计数

## 配置建议

### 保守设置（推荐新用户）
- 最少评论数：10次以上
- 延迟时间：10-30秒
- 每日限制：20次

### 标准设置（日常使用）
- 最少评论数：5次以上
- 延迟时间：5-15秒
- 每日限制：50次

### 积极设置（经验用户）
- 最少评论数：0次（无限制）
- 延迟时间：3-10秒
- 每日限制：100次

## 注意事项

1. **首次使用**：建议从保守设置开始，观察平台反应后逐步调整
2. **账号安全**：即使有安全控制，也要注意评论内容的质量和相关性
3. **平台规则**：安全控制只是技术手段，仍需遵守小红书的使用条款
4. **监控调整**：定期检查安全状态，根据实际情况调整参数

## 故障排除

### 常见问题
1. **评论数显示为0**：检查元素ID是否正确，可能需要更新选择器
2. **延迟不生效**：检查配置值是否为有效数字
3. **计数不准确**：使用"重置计数"功能重新开始

### 日志查看
所有安全控制操作都会记录在系统日志中，可通过日志文件查看详细执行情况。

## 更新历史

- **v1.0**：初始版本，包含三项基础安全控制功能
- 支持评论数筛选、随机延迟、每日限制
- 集成UI配置界面和状态监控
