// test_scroll_logout.js - 专门测试设置页面滑动和退出登录按钮查找

"ui";

ui.layout(
    <vertical padding="16dp">
        <text textSize="18sp" textColor="#2196F3" text="设置页面滑动测试工具" gravity="center" marginBottom="16dp" />

        <card cardBackgroundColor="#fff3cd" cardCornerRadius="8dp" cardElevation="2dp" margin="8dp">
            <vertical padding="12dp">
                <text textSize="14sp" textColor="#856404" textStyle="bold" text="⚠️ 使用说明" />
                <text textSize="12sp" textColor="#856404" text="• 可以从小红书主页开始测试完整流程" marginTop="4dp" />
                <text textSize="12sp" textColor="#856404" text="• 或者手动进入设置页面测试滑动功能" marginTop="2dp" />
                <text textSize="12sp" textColor="#856404" text="• 正确流程：主页 → 设置按钮 → 弹出层底部设置 → 设置页面滑动 → 退出登录" marginTop="2dp" />
            </vertical>
        </card>

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试选项:" />
        <checkbox id="testScroll" text="测试滑动功能" checked="true" />
        <checkbox id="testElementFind" text="测试元素查找" checked="true" />
        <checkbox id="testTextFind" text="测试文本查找" checked="true" />
        <checkbox id="testFullFlow" text="测试完整流程" checked="false" />

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="滑动参数:" />
        <horizontal>
            <text text="滑动次数:" />
            <input id="scrollCount" text="8" inputType="number" layout_weight="1" />
        </horizontal>
        <horizontal>
            <text text="滑动间隔(ms):" />
            <input id="scrollDelay" text="800" inputType="number" layout_weight="1" />
        </horizontal>

        <horizontal marginTop="16dp">
            <button id="startTestBtn" text="滑动测试" layout_weight="1" bg="#4CAF50" textColor="white" />
            <button id="manualScrollBtn" text="手动滑动" layout_weight="1" bg="#FF9800" textColor="white" />
        </horizontal>
        <horizontal marginTop="8dp">
            <button id="fullFlowTestBtn" text="完整流程测试" layout_weight="1" bg="#9C27B0" textColor="white" />
            <button id="navigationTestBtn" text="导航测试" layout_weight="1" bg="#2196F3" textColor="white" />
        </horizontal>

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="测试结果:" />
        <text id="testResult" text="等待测试..." textSize="14sp" textColor="#666666" />

        <text textSize="16sp" textColor="#000000" marginTop="16dp" text="详细日志:" />
        <ScrollView layout_weight="1">
            <text id="detailLog" text="" textSize="10sp" />
        </ScrollView>

        <button id="clearBtn" text="清空日志" marginTop="8dp" />
    </vertical>
);

// 日志函数
function log(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;

    ui.run(() => {
        const currentLog = ui.detailLog.getText().toString();
        ui.detailLog.setText(currentLog + logMessage + "\n");
    });

    console.log(logMessage);
}

function setResult(result, color) {
    if (!color) color = "#666666";
    ui.run(() => {
        ui.testResult.setText(result);
        ui.testResult.setTextColor(colors.parseColor(color));
    });
}

// 测试滑动查找退出登录按钮
function testScrollToLogout() {
    log("=== 开始滑动测试 ===");
    setResult("测试中...", "#FF9800");

    try {
        // 检查当前是否在设置页面
        if (currentPackage() !== "com.xingin.xhs") {
            throw new Error("请先打开小红书应用");
        }

        const scrollCount = parseInt(ui.scrollCount.getText().toString()) || 8;
        const scrollDelay = parseInt(ui.scrollDelay.getText().toString()) || 800;

        log("滑动参数: 次数=" + scrollCount + ", 间隔=" + scrollDelay + "ms");

        // 记录当前屏幕上的所有文本元素
        log("--- 当前屏幕元素检测 ---");
        const allTexts = textMatches(/.+/).find();
        log("当前屏幕文本元素数量: " + allTexts.length);

        for (let i = 0; i < Math.min(allTexts.length, 10); i++) {
            const textContent = allTexts[i].text();
            if (textContent && textContent.trim()) {
                log("文本" + (i + 1) + ": " + textContent.trim());
            }
        }

        // 测试元素查找
        if (ui.testElementFind.checked) {
            log("--- 测试ID元素查找 ---");
            const initialLogoutBtn = id("com.xingin.xhs:id/ebt").findOne(1000);
            log("退出登录按钮(ID): " + (initialLogoutBtn ? "找到" : "未找到"));
        }

        // 测试文本查找
        if (ui.testTextFind.checked) {
            log("--- 测试文本查找 ---");
            const initialLogoutTextBtn = textContains("退出登录").findOne(1000);
            log("退出登录按钮(文本): " + (initialLogoutTextBtn ? "找到" : "未找到"));

            const initialLogoutTextBtn2 = text("退出登录").findOne(1000);
            log("退出登录按钮(精确文本): " + (initialLogoutTextBtn2 ? "找到" : "未找到"));
        }

        // 执行滑动测试
        if (ui.testScroll.checked) {
            log("--- 开始滑动测试 ---");

            for (let i = 0; i < scrollCount; i++) {
                log("第" + (i + 1) + "次滑动");

                // 记录滑动前的屏幕状态
                const beforeTexts = textMatches(/.+/).find();
                log("滑动前文本元素数量: " + beforeTexts.length);

                // 执行滑动
                swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.3, scrollDelay);
                sleep(scrollDelay);

                // 记录滑动后的屏幕状态
                const afterTexts = textMatches(/.+/).find();
                log("滑动后文本元素数量: " + afterTexts.length);

                // 检查退出登录按钮
                const scrollLogoutBtn = id("com.xingin.xhs:id/ebt").findOne(1000);
                const scrollLogoutTextBtn = textContains("退出登录").findOne(1000);

                log("滑动后检测结果:");
                log("  - ID查找: " + (scrollLogoutBtn ? "找到" : "未找到"));
                log("  - 文本查找: " + (scrollLogoutTextBtn ? "找到" : "未找到"));

                if (scrollLogoutBtn || scrollLogoutTextBtn) {
                    log("✓ 找到退出登录按钮！");
                    setResult("找到退出登录按钮 (第" + (i + 1) + "次滑动)", "#4CAF50");
                    return;
                }

                // 检查是否到底部
                if (textContains("已经到底了").exists() || textContains("没有更多").exists()) {
                    log("已滑动到底部");
                    break;
                }

                // 显示当前屏幕的一些文本
                const currentTexts = textMatches(/.+/).find();
                for (let j = 0; j < Math.min(currentTexts.length, 5); j++) {
                    const currentTextContent = currentTexts[j].text();
                    if (currentTextContent && currentTextContent.trim() && currentTextContent.length < 20) {
                        log("  当前文本: " + currentTextContent.trim());
                    }
                }
            }

            log("滑动完成，未找到退出登录按钮");
            setResult("未找到退出登录按钮", "#f44336");
        }

    } catch (e) {
        log("测试失败: " + e.toString());
        setResult("测试失败", "#f44336");
    }
}

// 手动滑动一次
function manualScroll() {
    log("执行手动滑动");

    try {
        const scrollDelay = parseInt(ui.scrollDelay.getText().toString()) || 800;

        // 记录滑动前状态
        const beforeTexts = textMatches(/.+/).find();
        log("滑动前文本元素数量: " + beforeTexts.length);

        // 执行滑动
        swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.3, scrollDelay);
        sleep(scrollDelay);

        // 记录滑动后状态
        const afterTexts = textMatches(/.+/).find();
        log("滑动后文本元素数量: " + afterTexts.length);

        // 检查退出登录按钮
        const manualLogoutBtn = id("com.xingin.xhs:id/ebt").findOne(1000);
        const manualLogoutTextBtn = textContains("退出登录").findOne(1000);

        log("手动滑动后检测结果:");
        log("  - ID查找: " + (manualLogoutBtn ? "找到" : "未找到"));
        log("  - 文本查找: " + (manualLogoutTextBtn ? "找到" : "未找到"));

        if (manualLogoutBtn || manualLogoutTextBtn) {
            setResult("手动滑动找到退出登录按钮", "#4CAF50");
        } else {
            setResult("手动滑动未找到", "#FF9800");
        }

    } catch (e) {
        log("手动滑动失败: " + e.toString());
        setResult("手动滑动失败", "#f44336");
    }
}

// UI事件处理
ui.startTestBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    threads.start(testScrollToLogout);
});

ui.manualScrollBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    threads.start(manualScroll);
});

ui.clearBtn.on("click", () => {
    ui.detailLog.setText("");
    setResult("日志已清空", "#666666");
});

// 添加缺失的事件处理器
ui.fullFlowTestBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    toast("完整流程测试功能开发中...");
    log("完整流程测试功能开发中...");
});

ui.navigationTestBtn.on("click", () => {
    if (!auto.service) {
        toast("请先开启无障碍服务");
        return;
    }
    toast("导航测试功能开发中...");
    log("导航测试功能开发中...");
});

// 初始化
log("滑动测试工具已启动");

if (!auto.service) {
    log("警告: 无障碍服务未开启");
    setResult("需要无障碍服务", "#f44336");
} else {
    setResult("就绪", "#4CAF50");
}
