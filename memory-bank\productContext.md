# 产品需求文档 - 小红书自动化脚本

## 项目目标
开发一款安卓端的小红书自动化脚本，使用RPA技术模拟真人操作，具备可视化UI，能自动采集评论、发送私信、进行笔记评论和点赞等。

## 现有基础
已完成基础UI框架的搭建（基于Auto.js），可以显示界面并支持导出示例数据。

## 新增功能需求 (2025-05-18)

### 1. 高级笔记搜索与筛选
*   **需求**：根据用户输入的关键字搜索笔记。
*   **筛选功能**：支持小红书的搜索筛选条件：
    *   排序依据：综合、最新、最多点赞、最多评论、最多收藏。
    *   发布时间：一天内、一周内、半年内。
    *   位置距离：同城、附近。
*   **实现考虑**：需要在UI上添加相应的筛选条件输入/选择控件。`xhs_actions.js` 需要实现与小红书搜索和筛选功能的交互。

### 2. 评论区用户采集
*   **需求**：自动轮询搜索出来的笔记（基于功能1的结果）。
*   **采集内容**：根据用户输入的评论区关键字，采集评论用户的UID、用户昵称、评论内容。
*   **存储**：存为任务结果列表。
*   **实现考虑**：`xhs_actions.js` 需要实现遍历笔记、进入评论区、识别和提取评论内容及用户信息。`data_manager.js` 需要存储这些采集到的数据。UI需要显示此列表。

### 3. 笔记评论截流 (自动评论)
*   **需求**：对搜索筛选出来的笔记，在轮询笔记时自动进行评论。
*   **评论内容来源**：
    *   用户预置的评论话术。
    *   (可选，见功能6) AI大模型根据笔记内容生成话术。
*   **实现考虑**：`xhs_actions.js` 需要实现发表评论的操作。UI需要输入预置话术的区域。

### 4. 笔记截流点赞 (评论区用户点赞)
*   **需求**：对搜索筛选出来的笔记，在轮询笔记时自动点赞符合筛选条件的评论。
*   **目标用户**：针对功能2中采集到的、其评论内容符合“评论区关键字”的用户所发表的评论进行点赞。
*   **实现考虑**：`xhs_actions.js` 需要实现点赞评论的操作。需要与功能2采集的数据联动。

### 5. 精准过滤 (二次提取目标客户)
*   **需求**：在功能2采集到的用户基础上，进行二次筛选。
*   **过滤条件**：
    *   限制地区。
    *   AI分析评论内容，判断是否为目标客户。
*   **实现考虑**：需要在UI上增加地区设置。AI分析部分可能需要对接外部API或本地模型，提示词设置。`data_manager.js` 可能需要标记或分离出二次筛选后的用户。

### 6. AI大模型生成评论话术 (辅助功能3)
*   **需求**：作为功能3的增强，使用AI大模型为每条笔记生成独特的评论话术。
*   **用户设置**：用户需要提供提示词 (prompt)。
*   **流程**：
    1.  获取笔记内容：
        *   图文笔记：直接提取文字。
        *   视频笔记：通过第三方接口解析视频下载地址，下载视频后，（如果AI支持视频输入）发送给AI，或提取视频语音转文字发送给AI。
    2.  将笔记内容（文字/视频信息）和用户提示词发送给AI大模型。
    3.  接收AI生成的评论话术。
    4.  程序自动发布评论。
*   **关键点**：复制图文笔记文字的实现；视频链接解析和下载；与AI大模型的API交互。
*   **实现考虑**：此功能复杂，可考虑后期实现。需要在UI上设置AI提示词、API Key等。`xhs_actions.js` 需要增加复制笔记内容、解析视频链接的逻辑。需要一个新模块或在 `utils.js` 中封装AI交互逻辑。

### 7. 任务结果列表增强与后续操作
*   **需求**：对功能2采集到的用户列表进行管理和操作。
*   **列表信息增强**：除用户昵称、UID、评论内容外，增加评论日期。
*   **操作**：
    *   全选或手动选择用户。
    *   轮询批量关注。
    *   轮询批量私信：
        *   使用预置的私信话术。
        *   (可选) 将每个用户的评论内容和用户设定的私信提示词发给AI大模型，生成个性化私信内容后发送。
*   **实现考虑**：`data_manager.js` 需要存储评论日期。UI需要增强列表显示和选择功能，增加关注/私信按钮、私信话术输入框、AI私信提示词输入框。`xhs_actions.js` 需要实现关注和发送私信的操作。

## 总体开发策略
建议逐个功能模块进行规划、设计伪代码、编码实现和测试。AI相关功能可以作为高级特性在核心功能稳定后再重点攻关。