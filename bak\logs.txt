六月 2, 2025 2:32:02 下午 GMT+08:00: UTILS_FULL: utils.js (full version) initialized.
六月 2, 2025 2:32:02 下午 GMT+08:00: MAIN_V3: Attempting to require config.js...
六月 2, 2025 2:32:02 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:02 下午 GMT+08:00: MAIN_V3: config.js loaded successfully.
六月 2, 2025 2:32:02 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:02 下午 GMT+08:00: MAIN_V3: Attempting to require ui.js...
六月 2, 2025 2:32:02 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:02 下午 GMT+08:00: MAIN_V3: ui.js loaded successfully.
六月 2, 2025 2:32:02 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:02 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_actions.js...
六月 2, 2025 2:32:02 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:02 下午 GMT+08:00: PPACTIONS: 小红书评论操作模块 (xhs_comment_actions.js) 加载完毕。
六月 2, 2025 2:32:02 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:02 下午 GMT+08:00: NOTE_TYPE: 小红书笔记类型检测模块 (xhs_note_types.js) 加载完毕。
六月 2, 2025 2:32:02 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:02 下午 GMT+08:00: PPACTIONS: 小红书操作模块 (xhs_actions.js) 加载完毕 (with adapted official extraction logic)。
六月 2, 2025 2:32:02 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:02 下午 GMT+08:00: MAIN_V3: xhs_actions.js loaded successfully.
六月 2, 2025 2:32:02 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:02 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_note_commenting.js...
六月 2, 2025 2:32:02 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: NOTE_COMMENT: XHS Note Commenting Module loaded successfully.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: xhs_note_commenting.js loaded successfully.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: Attempting to require llm_service.js...
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: 短信验证码读取模块加载完毕 (sms_reader.js)
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: 豆包登录模块加载完毕 (doubao_login.js)
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: 豆包WebView模块加载完毕 (doubao_webview.js)
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: LLM服务模块加载完毕 (llm_service.js)
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: llm_service.js loaded successfully.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: Attempting to require llm_retry_service.js...
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: LLM重试服务模块加载完毕 (llm_retry_service.js)
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: llm_retry_service.js loaded successfully.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: Attempting to require note_navigation.js (for detailed info)...
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: note_navigation.js (detailed info) loaded successfully.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: Attempting to require xhs_share_link.js...
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: SHARE_LINK: XHS Share Link Module loaded successfully.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: xhs_share_link.js loaded successfully.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: uiManagerRef not set or updateLog not a function.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL: setUiManager called.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: mainUiLogUpdater passed to utils.setUiManager.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: Getting UI XML from uiModule...
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: UI XML string received. Calling ui.layout()...
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: ui.layout() call completed. globalUIObject populated.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: Attaching UI event handlers via uiModule.attachUIEventHandlers...
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: WARN: UI: btn_start_search UI element not found for attaching handler.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: WARN: UI: startCommentScrapingBtn UI element not found for attaching handler.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: WARN: UI: stopCommentScrapingBtn UI element not found for attaching handler.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: UI: task_comment_notes checkbox changed, new state: true
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: UI: enableLlmComments checkbox changed, customComments enabled: false
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: UI: enableLlmComments状态已保存: true
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: UI: Doubao AI enabled
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: UI: useDoubaoProxy状态已保存: true
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: UI: llmUseContentExtraction状态已保存: false
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: UI: Share link selected, content extraction deselected
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: UI: llmUseShareLink状态已保存: true
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: UI: 显示模板 "旅游"
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: 配置已加载到UI。
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: UI: 显示模板 "旅游"
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: uiModule.attachUIEventHandlers() call completed.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:03 下午 GMT+08:00: MAIN_V3: Script fully initialized. UI should be active.
六月 2, 2025 2:32:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:13 下午 GMT+08:00: 开始任务按钮被点击
六月 2, 2025 2:32:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:13 下午 GMT+08:00: 开始执行任务，选择的任务类型：笔记截流 
六月 2, 2025 2:32:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:13 下午 GMT+08:00: 开始搜索笔记，参数：{"keyword":"旅游","sortBy":3,"publishTime":1,"locationDistance":1,"targetRegion":0}
六月 2, 2025 2:32:13 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:14 下午 GMT+08:00: MAIN_V3: Received search request: {"keyword":"旅游","sortBy":3,"publishTime":1,"locationDistance":1,"targetRegion":0}
六月 2, 2025 2:32:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:14 下午 GMT+08:00: PPACTIONS: 开始笔记搜索: 关键字="旅游", 排序=3, 时间=1, 位置=1
六月 2, 2025 2:32:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:14 下午 GMT+08:00: UTILS: 尝试确保App '小红书' 打开并切换到前台...
六月 2, 2025 2:32:14 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:15 下午 GMT+08:00: UTILS: App '小红书' (com.xingin.xhs) 不在前台。尝试启动...
六月 2, 2025 2:32:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:15 下午 GMT+08:00: UTILS: App '小红书' 启动成功。等待应用响应...
六月 2, 2025 2:32:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:18 下午 GMT+08:00: UTILS: App '小红书' 已成功切换到前台。
六月 2, 2025 2:32:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:18 下午 GMT+08:00: PPACTIONS: 小红书App已准备好。
六月 2, 2025 2:32:18 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:19 下午 GMT+08:00: PPACTIONS: 找到首页搜索图标/按钮，点击进入搜索页。
六月 2, 2025 2:32:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:22 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 1)...
六月 2, 2025 2:32:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:24 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 2)...
六月 2, 2025 2:32:24 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:26 下午 GMT+08:00: PPACTIONS: 尝试查找搜索框 (方法 3)...
六月 2, 2025 2:32:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:26 下午 GMT+08:00: PPACTIONS: 找到搜索框 (方法 3)。Bounds: Rect(228, 114 - 786, 210)
六月 2, 2025 2:32:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:26 下午 GMT+08:00: PPACTIONS: 准备向搜索框输入文本...
六月 2, 2025 2:32:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:26 下午 GMT+08:00: PPACTIONS: 已调用 setText 输入关键字: 旅游
六月 2, 2025 2:32:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:28 下午 GMT+08:00: PPACTIONS: 准备触发搜索。
六月 2, 2025 2:32:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:28 下午 GMT+08:00: PPACTIONS: 步骤A - 尝试确保搜索框或其父控件有焦点...
六月 2, 2025 2:32:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:28 下午 GMT+08:00: PPACTIONS: 点击焦点目标: Rect(228, 114 - 786, 210) 以确保焦点。
六月 2, 2025 2:32:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:29 下午 GMT+08:00: PPACTIONS: 步骤B - 尝试查找并点击页面上的搜索按钮...
六月 2, 2025 2:32:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:29 下午 GMT+08:00: PPACTIONS: 尝试查找搜索确认按钮 (策略: Exact ID 'com.xingin.xhs:id/fce', Text='搜索', ClassName=Button, Clickable)...
六月 2, 2025 2:32:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:29 下午 GMT+08:00: PPACTIONS: 找到潜在搜索按钮。详情: Text: "搜索", Desc: "null", ID: com.xingin.xhs:id/fce, Class: android.widget.Button, Bounds: Rect(942, 96 - 1032, 228), Clickable: true, Visible: true
六月 2, 2025 2:32:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:29 下午 GMT+08:00: PPACTIONS: 按钮可见且可点击，尝试执行 click()...
六月 2, 2025 2:32:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:29 下午 GMT+08:00: PPACTIONS: searchConfirmButton.click() 执行成功 (返回true)。等待页面跳转...
六月 2, 2025 2:32:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:34 下午 GMT+08:00: PPACTIONS: 搜索已触发，准备应用筛选条件...
六月 2, 2025 2:32:34 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:34 下午 GMT+08:00: PPACTIONS: 需要应用排序、发布时间或位置筛选，尝试打开主筛选面板。
六月 2, 2025 2:32:34 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:34 下午 GMT+08:00: PPACTIONS: 尝试查找主筛选按钮 (策略: FilterButtonByIDHPY_UserExact)
六月 2, 2025 2:32:34 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:39 下午 GMT+08:00: PPACTIONS: 未通过策略 "FilterButtonByIDHPY_UserExact" 找到主筛选按钮。
六月 2, 2025 2:32:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:39 下午 GMT+08:00: PPACTIONS: 尝试查找主筛选按钮 (策略: FilterButtonByIDHPY_Clickable)
六月 2, 2025 2:32:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:39 下午 GMT+08:00: PPACTIONS: 找到主筛选按钮候选 (策略: FilterButtonByIDHPY_Clickable)。详情: Text: "", Desc: "null", ID: com.xingin.xhs:id/hpy, Class: android.widget.Button, Bounds: Rect(822, 228 - 1068, 360), Clickable: true, Visible: true
六月 2, 2025 2:32:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:39 下午 GMT+08:00: PPACTIONS: 主筛选按钮候选可用，将使用此按钮 (策略: FilterButtonByIDHPY_Clickable)。
六月 2, 2025 2:32:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:39 下午 GMT+08:00: PPACTIONS: 点击主筛选按钮: null
六月 2, 2025 2:32:39 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:42 下午 GMT+08:00: PPACTIONS: Validating if filter panel is open after clicking main filter button...
六月 2, 2025 2:32:42 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: Panel validation details (selectors include id="com.xingin.xhs:id/e1u", checking for visibility):
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00:   - Sort trigger ('综合'): found, visible: true
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00:   - Publish time option ('发布时间'): not found
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00:   - Location option ('位置距离'): not found
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: Filter panel confirmed open: At least one characteristic element is visible.
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: 在筛选面板内开始应用排序依据: 最多评论
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: 尝试直接查找并点击目标排序选项 "最多评论"
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: 尝试定位排序选项文本元素: id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text("最多评论")
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: 已定位排序选项 "最多评论"，调用 tryClickInPanel。
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "排序选项 "最多评论"".
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 初始属性: ID: com.xingin.xhs:id/e1u, Class: android.widget.TextView, Text: "最多评论", Desc: "null", Bounds: Rect(825, 508 - 1011, 616), Clickable: false, Visible: true
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "排序选项 "最多评论"" 文本元素自身.
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "排序选项 "最多评论"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "排序选项 "最多评论"".
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(825, 508 - 1011, 616), Clickable: false, Visible: true
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "排序选项 "最多评论"" 不可见 (true) 或不可点击 (false).
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "排序选项 "最多评论"".
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(807, 502 - 1030, 622), Clickable: true, Visible: true
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "排序选项 "最多评论"" 可见且可点击. 尝试 click().
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 成功点击 "排序选项 "最多评论"" 的父控件 (层级 2).
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "排序选项 "最多评论"" 的点击操作已标记为成功.
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:44 下午 GMT+08:00: PPACTIONS: 成功处理点击排序选项 "最多评论".
六月 2, 2025 2:32:44 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:46 下午 GMT+08:00: PPACTIONS: Delaying after sort option processing (1s).
六月 2, 2025 2:32:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: 尝试应用发布时间: "一天内"
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: 尝试定位发布时间选项文本元素: id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text("一天内")
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: 已定位发布时间选项 "一天内"，调用 tryClickInPanel。
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "发布时间选项 "一天内"".
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一天内"" 初始属性: ID: com.xingin.xhs:id/e1u, Class: android.widget.TextView, Text: "一天内", Desc: "null", Bounds: Rect(340, 1210 - 484, 1318), Clickable: false, Visible: true
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "发布时间选项 "一天内"" 文本元素自身.
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一天内"" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "发布时间选项 "一天内"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "发布时间选项 "一天内"".
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(340, 1210 - 484, 1318), Clickable: false, Visible: true
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "发布时间选项 "一天内"" 不可见 (true) 或不可点击 (false).
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "发布时间选项 "一天内"".
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(301, 1204 - 524, 1324), Clickable: true, Visible: true
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "发布时间选项 "一天内"" 可见且可点击. 尝试 click().
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 成功点击 "发布时间选项 "一天内"" 的父控件 (层级 2).
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "发布时间选项 "一天内"" 的点击操作已标记为成功.
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:48 下午 GMT+08:00: PPACTIONS: 成功处理点击发布时间选项 "一天内".
六月 2, 2025 2:32:48 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:50 下午 GMT+08:00: PPACTIONS: Delaying after publish time option processing (1s).
六月 2, 2025 2:32:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: 尝试应用位置距离: "同城"
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: 尝试定位位置距离选项文本元素: id("com.xingin.xhs:id/e1u").className("android.widget.TextView").text("同城")
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: 已定位位置距离选项 "同城"，调用 tryClickInPanel。
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 开始处理点击 "位置距离选项 "同城"".
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 初始属性: ID: com.xingin.xhs:id/e1u, Class: android.widget.TextView, Text: "同城", Desc: "null", Bounds: Rect(361, 1756 - 463, 1864), Clickable: false, Visible: true
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 1: 尝试直接点击 "位置距离选项 "同城"" 文本元素自身.
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 可见但不可直接点击 (clickable: false). 跳过直接点击自身.
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 步骤 2: "位置距离选项 "同城"" 直接点击未成功或未尝试，尝试查找并点击其可点击的父控件.
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 1) for "位置距离选项 "同城"".
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(361, 1756 - 463, 1864), Clickable: false, Visible: true
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 1) for "位置距离选项 "同城"" 不可见 (true) 或不可点击 (false).
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 检查父控件 (层级 2) for "位置距离选项 "同城"".
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) 属性: ID: null, Class: android.widget.FrameLayout, Text: "", Desc: "null", Bounds: Rect(301, 1750 - 524, 1870), Clickable: true, Visible: true
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 父控件 (层级 2) for "位置距离选项 "同城"" 可见且可点击. 尝试 click().
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] 成功点击 "位置距离选项 "同城"" 的父控件 (层级 2).
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:52 下午 GMT+08:00: PPACTIONS: [tryClickInPanel] "位置距离选项 "同城"" 的点击操作已标记为成功.
六月 2, 2025 2:32:52 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:53 下午 GMT+08:00: PPACTIONS: 成功处理点击位置距离选项 "同城".
六月 2, 2025 2:32:53 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:55 下午 GMT+08:00: PPACTIONS: 所有筛选选项已应用（或尝试应用）。现在关闭筛选面板。
六月 2, 2025 2:32:55 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:56 下午 GMT+08:00: PPACTIONS: “收起”文本元素 (id="e1u" and text="收起") 未找到。
六月 2, 2025 2:32:56 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:56 下午 GMT+08:00: PPACTIONS: “收起”按钮交互失败或未找到，将使用 back() 关闭筛选面板。
六月 2, 2025 2:32:56 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:58 下午 GMT+08:00: PPACTIONS: 尝试额外方法 - 点击筛选面板外的区域
六月 2, 2025 2:32:58 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:59 下午 GMT+08:00: PPACTIONS: 点击屏幕顶部区域后，筛选面板似乎已关闭
六月 2, 2025 2:32:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:59 下午 GMT+08:00: PPACTIONS: 笔记搜索和筛选操作流程结束。
六月 2, 2025 2:32:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:32:59 下午 GMT+08:00: MAIN_V3: 搜索成功。 搜索和筛选流程已执行
六月 2, 2025 2:32:59 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:02 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 2, 2025 2:33:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:02 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/ch9'
六月 2, 2025 2:33:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:02 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: ID exists, now chaining text: '筛选'
六月 2, 2025 2:33:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:02 下午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(864, 266 - 960, 321)
六月 2, 2025 2:33:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:02 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: 搜索完成，开始笔记截流(评论笔记)
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: MAIN_V3_COMMENTING: startNoteCommenting task initiated by UI.
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: MAIN_V3_COMMENTING: Note commenting thread started.
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/ch9'
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: ID exists, now chaining text: '筛选'
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(864, 266 - 960, 321)
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: MAIN_V3_COMMENTING: 开始处理当前屏幕内的所有笔记...
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 开始提取当前屏幕可见的笔记...
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 找到 5 个笔记容器
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 处理容器 1/5
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 标题: "真的是被骗来海南的 #海南旅游..."
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 作者: "贝易玩海南"
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "真的是被骗来海南的 #海南旅游", 作者: "贝易玩海南", signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 处理容器 2/5
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 标题: "百花岭 拿下！ 终于完成了准备好久好久的爬山计划 耶耶耶 百..."
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 作者: "不好玩"
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "百花岭 拿下！ 终于完成了准备好久好久的爬山计划 耶耶耶 百花岭尊嘟很美[派对R][派对R][派对R][派对R]#自然户外森林徒步", 作者: "不好玩", signature: "不好玩::百花岭 拿下！ 终于完成了准备好久好久的爬山计划 耶耶耶 百花岭尊嘟很美[派对R][派对R][派对R][派对R]#自然户外森林徒步"
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 处理容器 3/5
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 标题: "开着银河E5闯入海南东方小桂林！被美到失语 宝子们！谁懂啊！..."
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 作者: "卷卷hyo"
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "开着银河E5闯入海南东方小桂林！被美到失语 宝子们！谁懂啊！！ 开着我的梦中情车🚗银河E5自驾海南东方小桂林 直接解锁了现实版「山水画卷露营」 每一帧都像仙侠剧取", 作者: "卷卷hyo", signature: "卷卷hyo::开着银河E5闯入海南东方小桂林！被美到失语 宝子们！谁懂啊！！ 开着我的梦中情车🚗银河E5自驾海南东方小桂林 直接解锁了现实版「山水画卷露营」 每一帧都像仙侠剧取"
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 处理容器 4/5
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 标题: "安集海大峡谷，姐妹们都说拍出了“故事感” 新疆旅拍第1️⃣站..."
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 作者: "萌菜"
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "安集海大峡谷，姐妹们都说拍出了“故事感” 新疆旅拍第1️⃣站：安集海大峡谷 [爆炸R]36℃高温从乌鲁木齐出发，途经百里丹霞、肯斯瓦特水库，终抵目的地。站在几百米", 作者: "萌菜", signature: "萌菜::安集海大峡谷，姐妹们都说拍出了“故事感” 新疆旅拍第1️⃣站：安集海大峡谷 [爆炸R]36℃高温从乌鲁木齐出发，途经百里丹霞、肯斯瓦特水库，终抵目的地。站在几百米"
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 处理容器 5/5
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 标题: "三亚Daily vlog🫧🏝️海南旅游+日落浪漫｜cdf..."
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 作者: "儋州一枝花"
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "三亚Daily vlog🫧🏝️海南旅游+日落浪漫｜cdf中免海南618囤货攻略来啦！ #海南旅游  #海南日落", 作者: "儋州一枝花", signature: "儋州一枝花::三亚Daily vlog🫧🏝️海南旅游+日落浪漫｜cdf中免海南618囤货攻略来啦！ #海南旅游  #海南日落"
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 提取完成，找到 5 个有效笔记
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 返回前验证数组内容:
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:03 下午 GMT+08:00: PPACTIONS: 数组[0] - 标题: "真的是被骗来海南的 #海南旅游", 作者: "贝易玩海南", signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:03 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:04 下午 GMT+08:00: PPACTIONS: 数组[1] - 标题: "百花岭 拿下！ 终于完成了准备好久好久的爬山计划 耶耶耶 百花岭尊嘟很美[派对R][派对R][派对R][派对R]#自然户外森林徒步", 作者: "不好玩", signature: "不好玩::百花岭 拿下！ 终于完成了准备好久好久的爬山计划 耶耶耶 百花岭尊嘟很美[派对R][派对R][派对R][派对R]#自然户外森林徒步"
六月 2, 2025 2:33:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:04 下午 GMT+08:00: PPACTIONS: 数组[2] - 标题: "开着银河E5闯入海南东方小桂林！被美到失语 宝子们！谁懂啊！！ 开着我的梦中情车🚗银河E5自驾海南东方小桂林 直接解锁了现实版「山水画卷露营」 每一帧都像仙侠剧取", 作者: "卷卷hyo", signature: "卷卷hyo::开着银河E5闯入海南东方小桂林！被美到失语 宝子们！谁懂啊！！ 开着我的梦中情车🚗银河E5自驾海南东方小桂林 直接解锁了现实版「山水画卷露营」 每一帧都像仙侠剧取"
六月 2, 2025 2:33:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:04 下午 GMT+08:00: PPACTIONS: 数组[3] - 标题: "安集海大峡谷，姐妹们都说拍出了“故事感” 新疆旅拍第1️⃣站：安集海大峡谷 [爆炸R]36℃高温从乌鲁木齐出发，途经百里丹霞、肯斯瓦特水库，终抵目的地。站在几百米", 作者: "萌菜", signature: "萌菜::安集海大峡谷，姐妹们都说拍出了“故事感” 新疆旅拍第1️⃣站：安集海大峡谷 [爆炸R]36℃高温从乌鲁木齐出发，途经百里丹霞、肯斯瓦特水库，终抵目的地。站在几百米"
六月 2, 2025 2:33:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:04 下午 GMT+08:00: PPACTIONS: 数组[4] - 标题: "三亚Daily vlog🫧🏝️海南旅游+日落浪漫｜cdf中免海南618囤货攻略来啦！ #海南旅游  #海南日落", 作者: "儋州一枝花", signature: "儋州一枝花::三亚Daily vlog🫧🏝️海南旅游+日落浪漫｜cdf中免海南618囤货攻略来啦！ #海南旅游  #海南日落"
六月 2, 2025 2:33:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:04 下午 GMT+08:00: MAIN_V3_COMMENTING: 找到 5 个笔记，开始处理
六月 2, 2025 2:33:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:04 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 1/5
六月 2, 2025 2:33:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:04 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:04 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "贝易玩海南"
六月 2, 2025 2:33:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:04 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:04 下午 GMT+08:00: MAIN_V3_COMMENTING: 处理笔记: 真的是被骗来海南的 #海南旅游
六月 2, 2025 2:33:04 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:07 下午 GMT+08:00: PPACTIONS: Checking if current page is note detail page.
六月 2, 2025 2:33:07 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:07 下午 GMT+08:00: PPACTIONS: 找到 gn_ 元素 - 确认为图文笔记详情页
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: PPACTIONS: Note detail page check - 图文笔记: (indicator: true, comment: true, gn_: true) = true
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: PPACTIONS: Note detail page check - 视频笔记: false
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: PPACTIONS: Note detail page check - 最终结果: true
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 成功进入笔记详情页，检测笔记类型...
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: NOTE_TYPE: 开始在详情页检测笔记类型...
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: NOTE_TYPE: ✓ 找到 gn_ 元素 - 确认为图文笔记
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 检测到笔记类型: image_text
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: MAIN_V3_COMMENTING: 调用图文笔记处理模块
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: SIMPLE: 小红书评论采集模块 (简化版) 加载完毕。
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: SIMPLE_PROCESS: 开始处理图文笔记: 真的是被骗来海南的 #海南旅游
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: SIMPLE_PROCESS: 开始评论发布...
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: NOTE_COMMENT: Starting to publish comment in note: "真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: NOTE_COMMENT: Comment config: {"customComments":"好想去\n好漂亮","commentMode":"random","enableCommenting":true,"enableDeduplication":true,"enableLlmComments":true,"useDoubaoProxy":true,"doubaoPhoneNumber":"13876513399","llmUseShareLink":true,"llmUseContentExtraction":false,"selectedLlmPromptTemplateName":"旅游","llmPromptTemplates":[{"name":"默认通用模板","content":"请针对以下小红书笔记内容，生成一条友好且相关的评论。笔记内容：\n{笔记内容}"},{"name":"旅游","content":"请你先分析以上链接内的内容，包含评论部份，再根据这些内容和评论，结合以下我的业务和其它要求帮我设计评论话术：\n\n【我的角色和需求】\n我是一名旅游行业的从业者，希望在小红书的旅游类笔记评论区发布引流话术。我的目标是：\n高度相关与自然融入： 话术内容必须与原笔记主题紧密结合，不突兀，就像一个真实的、有经验的旅行爱好者在分享。\n巧妙透露潜在价值： 通过分享个人化的经验、独到的见解或实用的补充信息，让用户在不经意间感受到我在特定领域（例如，如何让多人出行更划算、更有趣）的了解或热情，从而对我这个人产生兴趣。绝不是直接推销或明示我经营的项目。\n引发好奇与点击： 话术要能引发其他用户的好奇心，让他们因为觉得我的评论有价值、有意思或提供了新视角，而主动点击我的头像来了解我。\n\n\n【禁止项】\n话术中绝对不能出现任何直接的、硬广式的推销词语或表述，例如“来找我”、“购买”、“优惠活动”、“价格咨询”、“预订”、“限时”、“特价”、“私信我了解”、“戳我头像看更多”、“关注我带你玩”\\等明确的营销用语或引导行动的词汇。\n话术不应包含任何能让人直接联想到我是商家或正在推广特定服务（如团队游）的表述。 例如，严格避免出现类似“我带过的团队都说超划算”、“我组织的团…”、“我帮人规划的行程…”、“我们公司做这个…”、“需要行程规划可以看我主页”等直接暴露从业者身份或暗示提供服务的说法。\n目标是让用户因为我的评论内容本身而对我产生好奇，而不是因为我暗示了某种服务。 要做到“润物细无声”。\n\n【我想让用户感知到的潜在价值】\n我对于如何组织高性价比的集体/多人出行（比如和朋友、家人一起）非常有心得和经验，总能发现一些让行程更有价值或更划算的小窍门，或者对这类行程的利弊有自己独到的见解和感受。我希望通过评论，间接体现出我对这方面的热情和一定的“懂行”。\n\n【话术风格要求】\n友好、热情\n互动性强\n\n【其他特殊要求】：\n使用1-2个emoji表情。\n字数控制在30字以内。\n请直接回复我你设计的评论话术即可，别的内容不要回复。"}],"llmApiUrl":"","llmModelName":"","llmTemperature":0.7,"llmMaxTokens":512}
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: DEDUP: 检查笔记是否已评论: "note_1685085832_22" -> true (原始值: [object Object])
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: NOTE_COMMENT: Note "真的是被骗来海南的 #海南旅游" already commented, skipping
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: SIMPLE_PROCESS: ✗ 评论发布失败或跳过
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: SIMPLE_PROCESS: 返回搜索结果页...
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: PPACTIONS: 尝试返回上一页。
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: PPACTIONS: 使用传入的笔记类型: image_text
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: PPACTIONS: 根据传入类型，使用图文笔记返回逻辑。
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:08 下午 GMT+08:00: PPACTIONS: 图文笔记 - 标准返回按钮点击成功
六月 2, 2025 2:33:08 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:10 下午 GMT+08:00: SIMPLE_PROCESS: 图文笔记处理完成，成功: false
六月 2, 2025 2:33:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:10 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已标记为已处理: 贝易玩海南::真的是被骗来海南的 #海南旅游...
六月 2, 2025 2:33:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:10 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记处理失败或跳过
六月 2, 2025 2:33:10 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 2/5
六月 2, 2025 2:33:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "贝易玩海南"
六月 2, 2025 2:33:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 贝易玩海南::真的是被骗来海南的 #海南旅游...
六月 2, 2025 2:33:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 3/5
六月 2, 2025 2:33:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:11 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "贝易玩海南"
六月 2, 2025 2:33:11 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 贝易玩海南::真的是被骗来海南的 #海南旅游...
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 4/5
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "贝易玩海南"
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 贝易玩海南::真的是被骗来海南的 #海南旅游...
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 5/5
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "贝易玩海南"
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 贝易玩海南::真的是被骗来海南的 #海南旅游...
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: MAIN_V3_COMMENTING: 当前屏幕处理完成。处理了 0 个笔记
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:12 下午 GMT+08:00: PPACTIONS: 在搜索结果页面下滚加载更多笔记...
六月 2, 2025 2:33:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:15 下午 GMT+08:00: PPACTIONS: 下滚完成，等待新内容加载...
六月 2, 2025 2:33:15 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:19 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 2, 2025 2:33:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:19 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/ch9'
六月 2, 2025 2:33:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:19 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: ID exists, now chaining text: '筛选'
六月 2, 2025 2:33:19 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS_SUCCESS: isSearchResultsPage: Found with ID (and optional text/desc). Element: Rect(864, 134 - 960, 189)
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: true
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: MAIN_V3_COMMENTING: 开始处理当前屏幕内的所有笔记...
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 开始提取当前屏幕可见的笔记...
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 找到 8 个笔记容器
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 处理容器 1/8
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 标题: "真的是被骗来海南的 #海南旅游..."
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 作者: "贝易玩海南"
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "真的是被骗来海南的 #海南旅游", 作者: "贝易玩海南", signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 处理容器 2/8
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 标题: "百花岭 拿下！ 终于完成了准备好久好久的爬山计划 耶耶耶 百..."
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 作者: "不好玩"
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "百花岭 拿下！ 终于完成了准备好久好久的爬山计划 耶耶耶 百花岭尊嘟很美[派对R][派对R][派对R][派对R]#自然户外森林徒步", 作者: "不好玩", signature: "不好玩::百花岭 拿下！ 终于完成了准备好久好久的爬山计划 耶耶耶 百花岭尊嘟很美[派对R][派对R][派对R][派对R]#自然户外森林徒步"
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 处理容器 3/8
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 标题: "开着银河E5闯入海南东方小桂林！被美到失语 宝子们！谁懂啊！..."
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 作者: "卷卷hyo"
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "开着银河E5闯入海南东方小桂林！被美到失语 宝子们！谁懂啊！！ 开着我的梦中情车🚗银河E5自驾海南东方小桂林 直接解锁了现实版「山水画卷露营」 每一帧都像仙侠剧取", 作者: "卷卷hyo", signature: "卷卷hyo::开着银河E5闯入海南东方小桂林！被美到失语 宝子们！谁懂啊！！ 开着我的梦中情车🚗银河E5自驾海南东方小桂林 直接解锁了现实版「山水画卷露营」 每一帧都像仙侠剧取"
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 处理容器 4/8
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 标题: "安集海大峡谷，姐妹们都说拍出了“故事感” 新疆旅拍第1️⃣站..."
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 作者: "萌菜"
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "安集海大峡谷，姐妹们都说拍出了“故事感” 新疆旅拍第1️⃣站：安集海大峡谷 [爆炸R]36℃高温从乌鲁木齐出发，途经百里丹霞、肯斯瓦特水库，终抵目的地。站在几百米", 作者: "萌菜", signature: "萌菜::安集海大峡谷，姐妹们都说拍出了“故事感” 新疆旅拍第1️⃣站：安集海大峡谷 [爆炸R]36℃高温从乌鲁木齐出发，途经百里丹霞、肯斯瓦特水库，终抵目的地。站在几百米"
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 处理容器 5/8
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 标题: "三亚Daily vlog🫧🏝️海南旅游+日落浪漫｜cdf..."
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 作者: "儋州一枝花"
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "三亚Daily vlog🫧🏝️海南旅游+日落浪漫｜cdf中免海南618囤货攻略来啦！ #海南旅游  #海南日落", 作者: "儋州一枝花", signature: "儋州一枝花::三亚Daily vlog🫧🏝️海南旅游+日落浪漫｜cdf中免海南618囤货攻略来啦！ #海南旅游  #海南日落"
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 处理容器 6/8
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 标题: "第一次来伊宁，我的建议是…... 在伊宁晃悠了四天，这座城就..."
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:20 下午 GMT+08:00: PPACTIONS: 作者: "奶思兔咪鱿"
六月 2, 2025 2:33:20 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "第一次来伊宁，我的建议是…... 在伊宁晃悠了四天，这座城就像一个装满惊喜的百宝箱🎁 既有异域风情的街巷，又有令人垂涎的美食。现在就把我的私藏体验分享给", 作者: "奶思兔咪鱿", signature: "奶思兔咪鱿::第一次来伊宁，我的建议是…... 在伊宁晃悠了四天，这座城就像一个装满惊喜的百宝箱🎁 既有异域风情的街巷，又有令人垂涎的美食。现在就把我的私藏体验分享给"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 处理容器 7/8
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 标题: "印尼穿搭+拍照地点合集！ #美术生  #ootd  #穿搭 ..."
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 作者: "小林姑儿"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "印尼穿搭+拍照地点合集！ #美术生  #ootd  #穿搭  #旅行穿搭  #旅游穿搭分享  #世界这本书又看了几页  l#旅行真的会让人变美", 作者: "小林姑儿", signature: "小林姑儿::印尼穿搭+拍照地点合集！ #美术生  #ootd  #穿搭  #旅行穿搭  #旅游穿搭分享  #世界这本书又看了几页  l#旅行真的会让人变美"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 处理容器 8/8
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 标题: "旅行日记｜🇲🇴 喜欢澳门🇲🇴下次还去 #旅游  #澳..."
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 作者: "小曾koi-"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 创建笔记对象 - 标题: "旅行日记｜🇲🇴 喜欢澳门🇲🇴下次还去 #旅游  #澳门旅行  #旅行日记", 作者: "小曾koi-", signature: "小曾koi-::旅行日记｜🇲🇴 喜欢澳门🇲🇴下次还去 #旅游  #澳门旅行  #旅行日记"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 提取完成，找到 8 个有效笔记
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 返回前验证数组内容:
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 数组[0] - 标题: "真的是被骗来海南的 #海南旅游", 作者: "贝易玩海南", signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 数组[1] - 标题: "百花岭 拿下！ 终于完成了准备好久好久的爬山计划 耶耶耶 百花岭尊嘟很美[派对R][派对R][派对R][派对R]#自然户外森林徒步", 作者: "不好玩", signature: "不好玩::百花岭 拿下！ 终于完成了准备好久好久的爬山计划 耶耶耶 百花岭尊嘟很美[派对R][派对R][派对R][派对R]#自然户外森林徒步"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 数组[2] - 标题: "开着银河E5闯入海南东方小桂林！被美到失语 宝子们！谁懂啊！！ 开着我的梦中情车🚗银河E5自驾海南东方小桂林 直接解锁了现实版「山水画卷露营」 每一帧都像仙侠剧取", 作者: "卷卷hyo", signature: "卷卷hyo::开着银河E5闯入海南东方小桂林！被美到失语 宝子们！谁懂啊！！ 开着我的梦中情车🚗银河E5自驾海南东方小桂林 直接解锁了现实版「山水画卷露营」 每一帧都像仙侠剧取"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 数组[3] - 标题: "安集海大峡谷，姐妹们都说拍出了“故事感” 新疆旅拍第1️⃣站：安集海大峡谷 [爆炸R]36℃高温从乌鲁木齐出发，途经百里丹霞、肯斯瓦特水库，终抵目的地。站在几百米", 作者: "萌菜", signature: "萌菜::安集海大峡谷，姐妹们都说拍出了“故事感” 新疆旅拍第1️⃣站：安集海大峡谷 [爆炸R]36℃高温从乌鲁木齐出发，途经百里丹霞、肯斯瓦特水库，终抵目的地。站在几百米"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 数组[4] - 标题: "三亚Daily vlog🫧🏝️海南旅游+日落浪漫｜cdf中免海南618囤货攻略来啦！ #海南旅游  #海南日落", 作者: "儋州一枝花", signature: "儋州一枝花::三亚Daily vlog🫧🏝️海南旅游+日落浪漫｜cdf中免海南618囤货攻略来啦！ #海南旅游  #海南日落"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 数组[5] - 标题: "第一次来伊宁，我的建议是…... 在伊宁晃悠了四天，这座城就像一个装满惊喜的百宝箱🎁 既有异域风情的街巷，又有令人垂涎的美食。现在就把我的私藏体验分享给", 作者: "奶思兔咪鱿", signature: "奶思兔咪鱿::第一次来伊宁，我的建议是…... 在伊宁晃悠了四天，这座城就像一个装满惊喜的百宝箱🎁 既有异域风情的街巷，又有令人垂涎的美食。现在就把我的私藏体验分享给"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 数组[6] - 标题: "印尼穿搭+拍照地点合集！ #美术生  #ootd  #穿搭  #旅行穿搭  #旅游穿搭分享  #世界这本书又看了几页  l#旅行真的会让人变美", 作者: "小林姑儿", signature: "小林姑儿::印尼穿搭+拍照地点合集！ #美术生  #ootd  #穿搭  #旅行穿搭  #旅游穿搭分享  #世界这本书又看了几页  l#旅行真的会让人变美"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: PPACTIONS: 数组[7] - 标题: "旅行日记｜🇲🇴 喜欢澳门🇲🇴下次还去 #旅游  #澳门旅行  #旅行日记", 作者: "小曾koi-", signature: "小曾koi-::旅行日记｜🇲🇴 喜欢澳门🇲🇴下次还去 #旅游  #澳门旅行  #旅行日记"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: MAIN_V3_COMMENTING: 找到 5 个笔记，开始处理
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 1/5
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "贝易玩海南"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:21 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 贝易玩海南::真的是被骗来海南的 #海南旅游...
六月 2, 2025 2:33:21 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 2/5
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "贝易玩海南"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 贝易玩海南::真的是被骗来海南的 #海南旅游...
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 3/5
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "贝易玩海南"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 贝易玩海南::真的是被骗来海南的 #海南旅游...
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 4/5
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "贝易玩海南"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 贝易玩海南::真的是被骗来海南的 #海南旅游...
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 检查笔记 5/5
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记标题: "真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记作者: "贝易玩海南"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记signature: "贝易玩海南::真的是被骗来海南的 #海南旅游"
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 笔记已处理过，跳过: 贝易玩海南::真的是被骗来海南的 #海南旅游...
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 当前屏幕处理完成。处理了 0 个笔记
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: MAIN_V3_COMMENTING: 当前屏幕所有笔记都已处理过，准备滚动到下一页
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:22 下午 GMT+08:00: PPACTIONS: 在搜索结果页面下滚加载更多笔记...
六月 2, 2025 2:33:22 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:26 下午 GMT+08:00: PPACTIONS: 下滚完成，等待新内容加载...
六月 2, 2025 2:33:26 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:29 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Function called.
六月 2, 2025 2:33:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:29 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Attempting with ID: 'com.xingin.xhs:id/ch9'
六月 2, 2025 2:33:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:29 下午 GMT+08:00: PPACTIONS_DEBUG: isSearchResultsPage: Element with ID 'com.xingin.xhs:id/ch9' does not exist initially.
六月 2, 2025 2:33:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:29 下午 GMT+08:00: PPACTIONS_RESULT: isSearchResultsPage: Final check result: false
六月 2, 2025 2:33:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:29 下午 GMT+08:00: MAIN_V3_COMMENTING: Not on a search results page. Stopping task.
六月 2, 2025 2:33:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:29 下午 GMT+08:00: MAIN_V3_COMMENTING: Note commenting thread finished. Total notes commented: 0
六月 2, 2025 2:33:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:29 下午 GMT+08:00: 停止任务按钮被点击
六月 2, 2025 2:33:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
六月 2, 2025 2:33:29 下午 GMT+08:00: MAIN_V3: Stop comment user scraping requested.
六月 2, 2025 2:33:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
