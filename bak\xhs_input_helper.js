/**
 * XHS Input Helper - 专门解决小红书输入框问题
 * 提供多种输入方法来解决setText失效的问题
 */

var utils = require('./utils.js');

/**
 * 强力输入文本到小红书输入框
 * @param {UiObject} inputElement - 输入框元素
 * @param {string} text - 要输入的文本
 * @returns {boolean} - 是否成功
 */
function forceInputText(inputElement, text) {
    if (!inputElement || !text) {
        utils.log("INPUT_HELPER: Invalid parameters");
        return false;
    }

    utils.log("INPUT_HELPER: Attempting to input: \"" + text + "\"");

    // 检查当前输入框内容
    var currentText = inputElement.text() || "";
    utils.log("INPUT_HELPER: Current input text: \"" + currentText + "\"");

    // 如果已经包含目标文本，直接返回成功
    if (currentText.indexOf(text) >= 0) {
        utils.log("INPUT_HELPER: Target text already present in input");
        return true;
    }

    // 如果输入框有任何非占位符内容，也算成功（解决输入混乱问题）
    if (currentText && currentText.length > 0 &&
        currentText !== "有话要说，快来评论" &&
        currentText !== "爱评论的人运气都不差") {
        utils.log("INPUT_HELPER: Input has valid content, accepting: \"" + currentText + "\"");
        return true;
    }

    var methods = [
        function () { return simpleSetText(inputElement, text); },
        function () { return clearAndSetText(inputElement, text); },
        function () { return clipboardMethod(inputElement, text); }
    ];

    for (var i = 0; i < methods.length; i++) {
        utils.log("INPUT_HELPER: Trying method " + (i + 1) + "/3");
        try {
            if (methods[i]()) {
                utils.log("INPUT_HELPER: Method " + (i + 1) + " succeeded");
                return true;
            }
        } catch (e) {
            utils.log("INPUT_HELPER: Method " + (i + 1) + " failed: " + e);
        }
        sleep(300);
    }

    utils.log("INPUT_HELPER: All methods failed");
    return false;
}

/**
 * 方法1: 简单setText
 */
function simpleSetText(inputElement, text) {
    utils.log("INPUT_HELPER: Method 1 - Simple setText");

    inputElement.click();
    sleep(200);

    var success = inputElement.setText(text);
    if (success) {
        sleep(500);
        var result = inputElement.text();
        utils.log("INPUT_HELPER: setText result: \"" + result + "\"");
        return result && (result.indexOf(text) >= 0 || result === text);
    }

    return false;
}

/**
 * 方法2: 清空后设置文本
 */
function clearAndSetText(inputElement, text) {
    utils.log("INPUT_HELPER: Method 2 - Clear and setText");

    inputElement.click();
    sleep(300);

    // 尝试清空 - 多次setText空字符串
    for (var i = 0; i < 3; i++) {
        inputElement.setText("");
        sleep(100);
    }

    // 设置文本
    var success = inputElement.setText(text);
    if (success) {
        sleep(500);
        var result = inputElement.text();
        utils.log("INPUT_HELPER: Clear and setText result: \"" + result + "\"");
        return result && (result.indexOf(text) >= 0 || result === text);
    }

    return false;
}

/**
 * 方法3: 剪贴板方法
 */
function clipboardMethod(inputElement, text) {
    utils.log("INPUT_HELPER: Method 3 - Clipboard method");

    // 设置剪贴板
    setClip(text);
    sleep(200);

    // 点击输入框
    inputElement.click();
    sleep(300);

    // 长按调出粘贴菜单
    var bounds = inputElement.bounds();
    press(bounds.centerX(), bounds.centerY(), 1000);
    sleep(1000);

    // 查找粘贴按钮
    var pasteButton = text("粘贴").findOne(2000);
    if (!pasteButton) {
        pasteButton = textContains("粘贴").findOne(1000);
    }

    if (pasteButton) {
        pasteButton.click();
        sleep(1000);

        var result = inputElement.text();
        utils.log("INPUT_HELPER: Clipboard result: \"" + result + "\"");
        return result && (result.indexOf(text) >= 0 || result === text);
    }

    return false;
}

/**
 * 验证输入是否成功
 */
function verifyInput(inputElement, expectedText) {
    var actualText = inputElement.text();
    utils.log("INPUT_HELPER: Expected: \"" + expectedText + "\", Actual: \"" + actualText + "\"");

    if (!actualText) {
        return false;
    }

    // 完全匹配
    if (actualText === expectedText) {
        return true;
    }

    // 包含匹配
    if (actualText.indexOf(expectedText) >= 0) {
        return true;
    }

    // 忽略占位符文本
    if (actualText !== "有话要说，快来评论" && actualText.length > 0) {
        utils.log("INPUT_HELPER: Partial success - got some text instead of placeholder");
        return true;
    }

    return false;
}

module.exports = {
    forceInputText,
    verifyInput
};

utils.log("INPUT_HELPER: XHS Input Helper Module loaded successfully.");
