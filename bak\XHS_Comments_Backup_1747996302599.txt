五月 25, 2025 6:18:43 下午 GMT+08:00: SIMPLE: 评论匹配关键字: 奶油巧克力
五月 25, 2025 6:18:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:43 下午 GMT+08:00: SIMPLE: 当前已采集用户列表: []
五月 25, 2025 6:18:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:43 下午 GMT+08:00: SIMPLE: ⚡ 首次遇到用户 奶油巧克力，开始采集用户信息
五月 25, 2025 6:18:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:43 下午 GMT+08:00: USER_PROFILE: 开始获取用户信息...
五月 25, 2025 6:18:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:43 下午 GMT+08:00: USER_PROFILE: 准备获取用户 "奶油巧克力" 的信息
五月 25, 2025 6:18:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:43 下午 GMT+08:00: USER_PROFILE: 关联评论: "饭是怎么报名吃的 8小时前  海南 回复"
五月 25, 2025 6:18:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:43 下午 GMT+08:00: USER_PROFILE: 点击昵称进入用户页面...
五月 25, 2025 6:18:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:43 下午 GMT+08:00: USER_PROFILE: ✓ 成功点击昵称，等待页面加载...
五月 25, 2025 6:18:43 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:46 下午 GMT+08:00: USER_PROFILE: 查找用户信息容器...
五月 25, 2025 6:18:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:46 下午 GMT+08:00: USER_PROFILE: ✓ 找到用户信息容器
五月 25, 2025 6:18:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:46 下午 GMT+08:00: USER_PROFILE: 在容器内查找小红书号...
五月 25, 2025 6:18:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:46 下午 GMT+08:00: USER_PROFILE: ✓ 成功获取小红书号: "小红书号：192289842"
五月 25, 2025 6:18:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:46 下午 GMT+08:00: USER_PROFILE: 准备返回评论区...
五月 25, 2025 6:18:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:46 下午 GMT+08:00: USER_PROFILE: ✓ 使用返回键返回
五月 25, 2025 6:18:46 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:50 下午 GMT+08:00: USER_PROFILE: 检测到图文笔记评论区
五月 25, 2025 6:18:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:18:50 下午 GMT+08:00: USER_PROFILE: ✓ 确认成功返回评论区
五月 25, 2025 6:18:50 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: ✗ 立即获取用户信息超时
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"奶油巧克力","content":"饭是怎么报名吃的 8小时前  海南 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: 提取到评论 #3: 奶油巧克力 - "饭是怎么报名吃的 8小时前  海南 回复"
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: 评论元素 #4: commentText="下午下雨了 昨天 22:56 海南 回复", nickname="一只小棠梨"
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一只小棠梨","content":"下午下雨了 昨天 22:56 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: 提取到评论 #4: 一只小棠梨 - "下午下雨了 昨天 22:56 海南 回复"
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: 评论元素 #5: commentText="上午去的 昨天 23:49 海南 回复", nickname="晨晓"
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"晨晓","content":"上午去的 昨天 23:49 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: 提取到评论 #5: 晨晓 - "上午去的 昨天 23:49 海南 回复"
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: 本次提取到 5 条评论
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: DEBUG - newComments[0] = {"nickname":"是阿傅呀","content":"好好玩 昨天 22:11 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="是阿傅呀", content="好好玩 昨天 22:11 海南 回复", matchesKeyword=false
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:00 下午 GMT+08:00: SIMPLE: DEBUG - allComments[0] = {"nickname":"是阿傅呀","content":"好好玩 昨天 22:11 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:00 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - newComments[1] = {"nickname":"一只小棠梨","content":"是滴 昨天 23:50 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一只小棠梨", content="是滴 昨天 23:50 海南 回复", matchesKeyword=false
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - allComments[1] = {"nickname":"一只小棠梨","content":"是滴 昨天 23:50 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - newComments[2] = {"nickname":"奶油巧克力","content":"饭是怎么报名吃的 8小时前  海南 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="奶油巧克力", content="饭是怎么报名吃的 8小时前  海南 回复", matchesKeyword=true
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - allComments[2] = {"nickname":"奶油巧克力","content":"饭是怎么报名吃的 8小时前  海南 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - newComments[3] = {"nickname":"一只小棠梨","content":"下午下雨了 昨天 22:56 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一只小棠梨", content="下午下雨了 昨天 22:56 海南 回复", matchesKeyword=false
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - allComments[3] = {"nickname":"一只小棠梨","content":"下午下雨了 昨天 22:56 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - newComments[4] = {"nickname":"晨晓","content":"上午去的 昨天 23:49 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="晨晓", content="上午去的 昨天 23:49 海南 回复", matchesKeyword=false
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: DEBUG - allComments[4] = {"nickname":"晨晓","content":"上午去的 昨天 23:49 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:01 下午 GMT+08:00: SIMPLE: 本次新增 5 条评论，当前总计 5 条评论
五月 25, 2025 6:19:01 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:02 下午 GMT+08:00: SIMPLE: 本次采集到 5 条新评论，继续滚动寻找更多评论...
五月 25, 2025 6:19:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:02 下午 GMT+08:00: SIMPLE: 滚动加载更多评论 (第 1 次)...
五月 25, 2025 6:19:02 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组长度: 5
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组中的前5条评论:
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "是阿傅呀", content: "好好玩 昨天 22:11 海南 回复" }
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "一只小棠梨", content: "是滴 昨天 23:50 海南 回复" }
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "奶油巧克力", content: "饭是怎么报名吃的 8小时前  海南 回复" }
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "一只小棠梨", content: "下午下雨了 昨天 22:56 海南 回复" }
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "晨晓", content: "上午去的 昨天 23:49 海南 回复" }
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组长度: 5
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组中的前5条评论:
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "是阿傅呀", content: "好好玩 昨天 22:11 海南 回复" }
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "一只小棠梨", content: "是滴 昨天 23:50 海南 回复" }
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "奶油巧克力", content: "饭是怎么报名吃的 8小时前  海南 回复" }
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "一只小棠梨", content: "下午下雨了 昨天 22:56 海南 回复" }
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "晨晓", content: "上午去的 昨天 23:49 海南 回复" }
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: 提取当前屏幕上的评论...
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: 找到 5 个评论内容元素和 6 个昵称元素
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:05 下午 GMT+08:00: SIMPLE: 评论元素 #1: commentText="好好玩 昨天 22:11 海南 回复", nickname="是阿傅呀"
五月 25, 2025 6:19:05 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"是阿傅呀","content":"好好玩 昨天 22:11 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: SIMPLE: 提取到评论 #1: 是阿傅呀 - "好好玩 昨天 22:11 海南 回复"
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: SIMPLE: 评论元素 #2: commentText="是滴 昨天 23:50 海南 回复", nickname="一只小棠梨"
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一只小棠梨","content":"是滴 昨天 23:50 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: SIMPLE: 提取到评论 #2: 一只小棠梨 - "是滴 昨天 23:50 海南 回复"
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: SIMPLE: 评论元素 #3: commentText="饭是怎么报名吃的 8小时前  海南 回复", nickname="奶油巧克力"
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "怎么": 奶油巧克力 - "饭是怎么报名吃的 8小时前  海南 回复..."
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: SIMPLE: 评论匹配关键字: 奶油巧克力
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: SIMPLE: 当前已采集用户列表: []
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: SIMPLE: ⚡ 首次遇到用户 奶油巧克力，开始采集用户信息
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: USER_PROFILE: 开始获取用户信息...
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: USER_PROFILE: 准备获取用户 "奶油巧克力" 的信息
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: USER_PROFILE: 关联评论: "饭是怎么报名吃的 8小时前  海南 回复"
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: USER_PROFILE: 点击昵称进入用户页面...
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:06 下午 GMT+08:00: USER_PROFILE: ✓ 成功点击昵称，等待页面加载...
五月 25, 2025 6:19:06 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:09 下午 GMT+08:00: USER_PROFILE: 查找用户信息容器...
五月 25, 2025 6:19:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:09 下午 GMT+08:00: USER_PROFILE: ✓ 找到用户信息容器
五月 25, 2025 6:19:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:09 下午 GMT+08:00: USER_PROFILE: 在容器内查找小红书号...
五月 25, 2025 6:19:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:09 下午 GMT+08:00: USER_PROFILE: ✓ 成功获取小红书号: "小红书号：192289842"
五月 25, 2025 6:19:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:09 下午 GMT+08:00: USER_PROFILE: 准备返回评论区...
五月 25, 2025 6:19:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:09 下午 GMT+08:00: USER_PROFILE: ✓ 使用返回键返回
五月 25, 2025 6:19:09 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:12 下午 GMT+08:00: USER_PROFILE: 检测到图文笔记评论区
五月 25, 2025 6:19:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:12 下午 GMT+08:00: USER_PROFILE: ✓ 确认成功返回评论区
五月 25, 2025 6:19:12 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: ✗ 立即获取用户信息超时
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"奶油巧克力","content":"饭是怎么报名吃的 8小时前  海南 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: 提取到评论 #3: 奶油巧克力 - "饭是怎么报名吃的 8小时前  海南 回复"
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: 评论元素 #4: commentText="下午下雨了 昨天 22:56 海南 回复", nickname="一只小棠梨"
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一只小棠梨","content":"下午下雨了 昨天 22:56 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: 提取到评论 #4: 一只小棠梨 - "下午下雨了 昨天 22:56 海南 回复"
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: 评论元素 #5: commentText="上午去的 昨天 23:49 海南 回复", nickname="晨晓"
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"晨晓","content":"上午去的 昨天 23:49 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: 提取到评论 #5: 晨晓 - "上午去的 昨天 23:49 海南 回复"
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: 本次提取到 5 条评论
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - newComments[0] = {"nickname":"是阿傅呀","content":"好好玩 昨天 22:11 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="是阿傅呀", content="好好玩 昨天 22:11 海南 回复", matchesKeyword=false
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - allComments[5] = {"nickname":"是阿傅呀","content":"好好玩 昨天 22:11 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - newComments[1] = {"nickname":"一只小棠梨","content":"是滴 昨天 23:50 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一只小棠梨", content="是滴 昨天 23:50 海南 回复", matchesKeyword=false
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - allComments[6] = {"nickname":"一只小棠梨","content":"是滴 昨天 23:50 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - newComments[2] = {"nickname":"奶油巧克力","content":"饭是怎么报名吃的 8小时前  海南 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="奶油巧克力", content="饭是怎么报名吃的 8小时前  海南 回复", matchesKeyword=true
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - allComments[7] = {"nickname":"奶油巧克力","content":"饭是怎么报名吃的 8小时前  海南 回复","matchesKeyword":true,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - newComments[3] = {"nickname":"一只小棠梨","content":"下午下雨了 昨天 22:56 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="一只小棠梨", content="下午下雨了 昨天 22:56 海南 回复", matchesKeyword=false
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - allComments[8] = {"nickname":"一只小棠梨","content":"下午下雨了 昨天 22:56 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - newComments[4] = {"nickname":"晨晓","content":"上午去的 昨天 23:49 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - 当前评论值: nickname="晨晓", content="上午去的 昨天 23:49 海南 回复", matchesKeyword=false
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: DEBUG - allComments[9] = {"nickname":"晨晓","content":"上午去的 昨天 23:49 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:23 下午 GMT+08:00: SIMPLE: 本次新增 5 条评论，当前总计 10 条评论
五月 25, 2025 6:19:23 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:24 下午 GMT+08:00: SIMPLE: 本次采集到 5 条新评论，继续滚动寻找更多评论...
五月 25, 2025 6:19:24 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:24 下午 GMT+08:00: SIMPLE: 滚动加载更多评论 (第 2 次)...
五月 25, 2025 6:19:24 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:27 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组长度: 10
五月 25, 2025 6:19:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:27 下午 GMT+08:00: SIMPLE: 递归调用前，allComments数组中的前5条评论:
五月 25, 2025 6:19:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:27 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "是阿傅呀", content: "好好玩 昨天 22:11 海南 回复" }
五月 25, 2025 6:19:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:27 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "一只小棠梨", content: "是滴 昨天 23:50 海南 回复" }
五月 25, 2025 6:19:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:27 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "奶油巧克力", content: "饭是怎么报名吃的 8小时前  海南 回复" }
五月 25, 2025 6:19:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:27 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "一只小棠梨", content: "下午下雨了 昨天 22:56 海南 回复" }
五月 25, 2025 6:19:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:27 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "晨晓", content: "上午去的 昨天 23:49 海南 回复" }
五月 25, 2025 6:19:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:27 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组长度: 10
五月 25, 2025 6:19:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:27 下午 GMT+08:00: SIMPLE: 递归调用开始，allComments数组中的前5条评论:
五月 25, 2025 6:19:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:27 下午 GMT+08:00: SIMPLE: allComments[0] = { nickname: "是阿傅呀", content: "好好玩 昨天 22:11 海南 回复" }
五月 25, 2025 6:19:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:27 下午 GMT+08:00: SIMPLE: allComments[1] = { nickname: "一只小棠梨", content: "是滴 昨天 23:50 海南 回复" }
五月 25, 2025 6:19:27 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: allComments[2] = { nickname: "奶油巧克力", content: "饭是怎么报名吃的 8小时前  海南 回复" }
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: allComments[3] = { nickname: "一只小棠梨", content: "下午下雨了 昨天 22:56 海南 回复" }
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: allComments[4] = { nickname: "晨晓", content: "上午去的 昨天 23:49 海南 回复" }
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: 提取当前屏幕上的评论...
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: 找到 7 个评论内容元素和 8 个昵称元素
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: 评论元素 #1: commentText="好好玩 昨天 22:11 海南 回复", nickname="是阿傅呀"
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"是阿傅呀","content":"好好玩 昨天 22:11 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: 提取到评论 #1: 是阿傅呀 - "好好玩 昨天 22:11 海南 回复"
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: 评论元素 #2: commentText="是滴 昨天 23:50 海南 回复", nickname="一只小棠梨"
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: DEBUG - 创建评论对象: {"nickname":"一只小棠梨","content":"是滴 昨天 23:50 海南 回复","matchesKeyword":false,"xhsId":"","userInfoCollected":false}, 内存地址: [object Object]
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: 提取到评论 #2: 一只小棠梨 - "是滴 昨天 23:50 海南 回复"
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: 评论元素 #3: commentText="饭是怎么报名吃的 8小时前  海南 回复", nickname="奶油巧克力"
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: 评论匹配关键字 "怎么": 奶油巧克力 - "饭是怎么报名吃的 8小时前  海南 回复..."
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: 评论匹配关键字: 奶油巧克力
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: 当前已采集用户列表: []
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: SIMPLE: ⚡ 首次遇到用户 奶油巧克力，开始采集用户信息
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: USER_PROFILE: 开始获取用户信息...
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: USER_PROFILE: 准备获取用户 "奶油巧克力" 的信息
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: USER_PROFILE: 关联评论: "饭是怎么报名吃的 8小时前  海南 回复"
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: USER_PROFILE: 点击昵称进入用户页面...
五月 25, 2025 6:19:28 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:28 下午 GMT+08:00: USER_PROFILE: ✓ 成功点击昵称，等待页面加载...
五月 25, 2025 6:19:29 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:32 下午 GMT+08:00: USER_PROFILE: 查找用户信息容器...
五月 25, 2025 6:19:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:32 下午 GMT+08:00: USER_PROFILE: ✓ 找到用户信息容器
五月 25, 2025 6:19:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:32 下午 GMT+08:00: USER_PROFILE: 在容器内查找小红书号...
五月 25, 2025 6:19:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:32 下午 GMT+08:00: USER_PROFILE: ✓ 成功获取小红书号: "小红书号：192289842"
五月 25, 2025 6:19:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:32 下午 GMT+08:00: USER_PROFILE: 准备返回评论区...
五月 25, 2025 6:19:32 下午 GMT+08:00: UTILS_FULL_ADV: Attempting to update UI log via uiManagerRef.
五月 25, 2025 6:19:32 下午 GMT+08:00: USER_PROFILE: ✓ 使用返回键返回